package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoRespVO extends BrokerPlanInfoBaseVO {

    @Schema(description = "编号", required = true, example = "27422")
    private Long id;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
