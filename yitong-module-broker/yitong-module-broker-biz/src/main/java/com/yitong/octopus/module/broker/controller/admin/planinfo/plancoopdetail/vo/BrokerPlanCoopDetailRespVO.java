package com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划合作细则 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanCoopDetailRespVO extends BrokerPlanCoopDetailBaseVO {

    @Schema(description = "编号", required = true, example = "3509")
    private Long id;

    @Schema(description = "计划ID", example = "21218")
    private Long planId;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
