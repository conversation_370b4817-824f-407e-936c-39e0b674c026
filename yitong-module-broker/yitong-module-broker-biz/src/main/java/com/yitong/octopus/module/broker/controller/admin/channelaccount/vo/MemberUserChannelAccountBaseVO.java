package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
* 经纪人渠道账户信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class MemberUserChannelAccountBaseVO {

    @Schema(description = "用户ID", example = "31731")
    private Long userId;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省/直辖市")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "地区")
    private String area;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "平台ID", required = true, example = "1223")
    @NotNull(message = "平台ID不能为空")
    private String platformId;

    @Schema(description = "平台号")
    private String platformNo;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台地址", example = "https://www.iocoder.cn")
    private String platformUrl;

    @Schema(description = "平台等级")
    private Integer platformLevel;

    @Schema(description = "平台带货分")
    private Integer platformThrScore;

    @Schema(description = "平台粉丝数", example = "1231")
    private Integer platformFansCount;

    @Schema(description = "平台粉丝级别")
    private Integer platformFansLevel;

}
