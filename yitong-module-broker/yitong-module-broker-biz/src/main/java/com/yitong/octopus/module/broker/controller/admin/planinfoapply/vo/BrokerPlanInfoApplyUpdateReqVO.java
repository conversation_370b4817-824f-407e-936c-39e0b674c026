package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划申请更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoApplyUpdateReqVO extends BrokerPlanInfoApplyBaseVO {

    @Schema(description = "编号", required = true, example = "18294")
    @NotNull(message = "编号不能为空")
    private Long id;

}
