package com.yitong.octopus.module.broker.service.plansku;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import com.yitong.octopus.module.broker.convert.planstore.BrokerPlanStoreConvert;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.plansku.BrokerPlanSkuConvert;
import com.yitong.octopus.module.broker.dal.mysql.plansku.BrokerPlanSkuMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划sku信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanSkuServiceImpl implements BrokerPlanSkuService {

    @Resource
    private BrokerPlanSkuMapper planSkuMapper;

    @Override
    public Long createPlanSku(BrokerPlanSkuReqVO createReqVO) {
        // 插入
        BrokerPlanSkuDO planSku = BrokerPlanSkuConvert.INSTANCE.convert(createReqVO);
        planSkuMapper.insert(planSku);
        // 返回
        return planSku.getId();
    }

    @Override
    public void updatePlanSku(BrokerPlanSkuReqVO updateReqVO) {
        // 校验存在
        validatePlanSkuExists(updateReqVO.getId());
        // 更新
        BrokerPlanSkuDO updateObj = BrokerPlanSkuConvert.INSTANCE.convert(updateReqVO);
        planSkuMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanSku(Long id) {
        // 校验存在
        validatePlanSkuExists(id);
        // 删除
        planSkuMapper.deleteById(id);
    }

    private void validatePlanSkuExists(Long id) {
        if (planSkuMapper.selectById(id) == null) {
            throw exception(PLAN_SKU_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanSkuDO getPlanSku(Long id) {
        return planSkuMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanSkuDO> getPlanSkuList(Collection<Long> ids) {
        return planSkuMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanSkuDO> getPlanSkuPage(BrokerPlanSkuPageReqVO pageReqVO) {
        return planSkuMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanSkuDO> getPlanSkuList(BrokerPlanSkuExportReqVO exportReqVO) {
        return planSkuMapper.selectList(exportReqVO);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanSkuList(Long planId, List<BrokerPlanSkuReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanSkuDO> list = getPlanSkuListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanSkuDO> saveList =   Lists.newArrayList();
        List<BrokerPlanSkuDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanSkuDO so = BrokerPlanSkuConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanSkuDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planSkuMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planSkuMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planSkuMapper.updateBatchById(updateList);
        }
    }

    @Override
    public void deletePlanSkuByPlanId(Long planId) {
        planSkuMapper.delete(new LambdaQueryWrapperX<BrokerPlanSkuDO>()
                .eq(BrokerPlanSkuDO::getPlanId,planId));
    }

    @Override
    public List<BrokerPlanSkuDO> getPlanSkuListByPlanId(Long planId) {
        return planSkuMapper.selectList(new LambdaQueryWrapperX<BrokerPlanSkuDO>()
                .eq(BrokerPlanSkuDO::getPlanId,planId));
    }

}
