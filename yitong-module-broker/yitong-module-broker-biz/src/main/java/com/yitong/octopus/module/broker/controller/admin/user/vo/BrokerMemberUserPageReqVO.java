package com.yitong.octopus.module.broker.controller.admin.user.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerMemberUserPageReqVO extends PageParam {

    @Schema(description = "用户姓名", example = "张三")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "性别1男 0女")
    private Boolean sex;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "用户昵称", example = "芋艿")
    private String nickName;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省/直辖市")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "地区")
    private String area;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "状态: 0 无效，1有效", example = "2")
    private Byte status;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "注册 IP")
    private String registerIp;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] loginDate;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
