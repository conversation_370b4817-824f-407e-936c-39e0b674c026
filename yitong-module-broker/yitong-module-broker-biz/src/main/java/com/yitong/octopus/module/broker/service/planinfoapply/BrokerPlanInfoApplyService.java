package com.yitong.octopus.module.broker.service.planinfoapply;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划申请 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanInfoApplyService {

    /**
     * 创建经纪人计划申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long applyPlanInfo(@Valid BrokerPlanInfoApplyReqVO createReqVO);

    /**
     * 确认经纪人计划
     *
     * @param ids 更新信息
     */
    void confirmPlanInfoApply(Collection<Long> ids);

    /**
     * 拒绝经纪人计划
     *
     * @param ids 更新信息
     */
    void rejectPlanInfoApply(Collection<Long> ids);

    /**
     * 完成经纪人计划
     *
     * @param reqVO 更新信息
     */
    void finishPlanInfoApply(BrokerPlanInfoApplyFinishReqVO reqVO);

    /**
     * 删除经纪人计划申请
     *
     * @param id 编号
     */
    void deletePlanInfoApply(Long id);

    /**
     * 获得经纪人计划申请
     *
     * @param id 编号
     * @return 经纪人计划申请
     */
    BrokerPlanInfoApplyDO getPlanInfoApply(Long id);

    /**
     * 获得经纪人计划申请列表
     *
     * @param ids 编号
     * @return 经纪人计划申请列表
     */
    List<BrokerPlanInfoApplyDO> getPlanInfoApplyList(Collection<Long> ids);

    /**
     * 获得经纪人计划申请分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划申请分页
     */
    PageResult<BrokerPlanInfoApplyDO> getPlanInfoApplyPage(BrokerPlanInfoApplyPageReqVO pageReqVO);

    /**
     * 获得经纪人计划申请列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划申请列表
     */
    List<BrokerPlanInfoApplyDO> getPlanInfoApplyList(BrokerPlanInfoApplyExportReqVO exportReqVO);

    /**
     * 根据计划和渠道账号查询记录
     * @param planId 计划Id
     * @param channelAccountId 账号Id
     * @return
     */
    BrokerPlanInfoApplyDO getEffectivePlanInfoApply(Long planId,Long channelAccountId);
}
