package com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划渠道信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanChannelRespVO extends BrokerPlanChannelBaseVO {

    @Schema(description = "编号", required = true, example = "20633")
    private Long id;

    @Schema(description = "计划ID", example = "30376")
    private Long planId;
    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
