//package com.yitong.octopus.module.broker.api.user;
//
//import com.yitong.octopus.module.broker.api.user.dto.MemberUserRespDTO;
//import com.yitong.octopus.module.broker.service.user.BrokerMemberUserService;
//import com.yitong.octopus.module.broker.convert.user.UserConvert;
//import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
//import org.springframework.stereotype.Service;
//import org.springframework.validation.annotation.Validated;
//
//import javax.annotation.Resource;
//import java.util.Collection;
//import java.util.List;
//
///**
// * 会员用户的 API 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Validated
//public class MemberUserApiImpl implements MemberUserApi {
//
//    @Resource
//    private BrokerMemberUserService userService;
//
//    @Override
//    public MemberUserRespDTO getUser(Long id) {
//        BrokerMemberUserDO user = userService.getUser(id);
//        return UserConvert.INSTANCE.convert2(user);
//    }
//
//    @Override
//    public List<MemberUserRespDTO> getUsers(Collection<Long> ids) {
//        return UserConvert.INSTANCE.convertList2(userService.getUserList(ids));
//    }
//
//    @Override
//    public List<MemberUserRespDTO> getUserListByNickname(String nickname) {
//        return UserConvert.INSTANCE.convertList2(userService.getUserListByNickname(nickname));
//    }
//
//    @Override
//    public MemberUserRespDTO getUserByMobile(String mobile) {
//        return UserConvert.INSTANCE.convert2(userService.getUserByMobile(mobile));
//    }
//
//}
