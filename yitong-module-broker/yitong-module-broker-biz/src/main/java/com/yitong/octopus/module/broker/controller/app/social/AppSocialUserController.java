package com.yitong.octopus.module.broker.controller.app.social;

import com.yitong.octopus.framework.common.enums.UserTypeEnum;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.module.broker.controller.app.social.vo.AppSocialUserBindReqVO;
import com.yitong.octopus.module.broker.controller.app.social.vo.AppSocialUserUnbindReqVO;
import com.yitong.octopus.module.broker.convert.social.SocialUserConvert;
import com.yitong.octopus.module.system.api.social.SocialUserApi;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "经纪人 App - 社交用户")
@RestController
@RequestMapping("/broker-member/social-user")
@Validated
public class AppSocialUserController {

    @Resource
    private SocialUserApi socialUserApi;

    @PostMapping("/bind")
    @Operation(summary = "社交绑定，使用 code 授权码")
    public CommonResult<Boolean> socialBind(@RequestBody @Valid AppSocialUserBindReqVO reqVO) {
        socialUserApi.bindSocialUser(SocialUserConvert.INSTANCE.convert(getLoginUserId(), UserTypeEnum.MEMBER.getValue(), reqVO));
        return CommonResult.success(true);
    }

    @DeleteMapping("/unbind")
    @Operation(summary = "取消社交绑定")
    public CommonResult<Boolean> socialUnbind(@RequestBody AppSocialUserUnbindReqVO reqVO) {
        socialUserApi.unbindSocialUser(SocialUserConvert.INSTANCE.convert(getLoginUserId(), UserTypeEnum.MEMBER.getValue(), reqVO));
        return CommonResult.success(true);
    }

}
