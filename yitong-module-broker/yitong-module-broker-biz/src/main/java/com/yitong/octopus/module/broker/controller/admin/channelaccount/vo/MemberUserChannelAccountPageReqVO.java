package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人渠道账户信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserChannelAccountPageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "31731")
    private Long userId;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "备注", example = "随便")
    private String remark;


    @Schema(description = "平台ID", example = "1223")
    private String platformId;

    @Schema(description = "平台号")
    private String platformNo;

    @Schema(description = "平台名称")
    private String platformName;

    @Schema(description = "平台等级")
    private Integer platformLevel;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "状态", example = "1")
    private Boolean status;


    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
