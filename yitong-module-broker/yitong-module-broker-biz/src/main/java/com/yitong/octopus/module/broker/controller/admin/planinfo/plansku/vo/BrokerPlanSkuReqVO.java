package com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划sku信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanSkuReqVO extends BrokerPlanSkuBaseVO {

    @Schema(description = "编号", example = "2555")
    private Long id;

}
