package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoUpdateReqVO extends BrokerPlanInfoBaseVO {

    @Schema(description = "编号", required = true, example = "27422")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "期望周几,多个','分隔")
    private String talentStoreDays;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "渠道信息", required = true)
    @NotNull(message = "渠道不能为空")
    private List<BrokerPlanChannelReqVO> channelList;

    @Schema(description = "门店信息", required = true)
    @NotNull(message = "门店不能为空")
    private List<BrokerPlanStoreReqVO> storeList;

    @Schema(description = "商品信息", required = true)
    @NotNull(message = "商品不能为空")
    private List<BrokerPlanSkuReqVO> skuList;

    @Schema(description = "招募费用")
    private List<BrokerPlanFissionReqVO> fissionList;

    @Schema(description = "结算信息", required = true)
    @NotNull(message = "结算信息不能为空")
    private List<BrokerPlanBillItemReqVO> billItem;

    @Schema(description = "联系信息", required = true)
    @NotNull(message = "联系信息不能为空")
    private List<BrokerPlanContactReqVO> contactList;

}
