package com.yitong.octopus.module.broker.dal.mysql.planinfoapply;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;

/**
 * 经纪人计划申请 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanInfoApplyMapper extends BaseMapperX<BrokerPlanInfoApplyDO> {

    default PageResult<BrokerPlanInfoApplyDO> selectPage(BrokerPlanInfoApplyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
                .eqIfPresent(BrokerPlanInfoApplyDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getTalentId, reqVO.getTalentId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getTalentAccountId, reqVO.getTalentAccountId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(BrokerPlanInfoApplyDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksId, reqVO.getWorksId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksShortUrl, reqVO.getWorksShortUrl())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksUrl, reqVO.getWorksUrl())
                .eqIfPresent(BrokerPlanInfoApplyDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanInfoApplyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanInfoApplyDO::getId));
    }

    default List<BrokerPlanInfoApplyDO> selectList(BrokerPlanInfoApplyExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
                .eqIfPresent(BrokerPlanInfoApplyDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getTalentId, reqVO.getTalentId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getTalentAccountId, reqVO.getTalentAccountId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(BrokerPlanInfoApplyDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksId, reqVO.getWorksId())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksShortUrl, reqVO.getWorksShortUrl())
                .eqIfPresent(BrokerPlanInfoApplyDO::getWorksUrl, reqVO.getWorksUrl())
                .eqIfPresent(BrokerPlanInfoApplyDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanInfoApplyDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanInfoApplyDO::getId));
    }

}
