package com.yitong.octopus.module.broker.convert.channelaccount;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;

/**
 * 经纪人渠道账户信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberUserChannelAccountConvert {

    MemberUserChannelAccountConvert INSTANCE = Mappers.getMapper(MemberUserChannelAccountConvert.class);

    MemberUserChannelAccountDO convert(MemberUserChannelAccountCreateReqVO bean);

    MemberUserChannelAccountDO convert(MemberUserChannelAccountUpdateReqVO bean);

    MemberUserChannelAccountRespVO convert(MemberUserChannelAccountDO bean);

    List<MemberUserChannelAccountRespVO> convertList(List<MemberUserChannelAccountDO> list);

    PageResult<MemberUserChannelAccountRespVO> convertPage(PageResult<MemberUserChannelAccountDO> page);

    List<MemberUserChannelAccountExcelVO> convertList02(List<MemberUserChannelAccountDO> list);

}
