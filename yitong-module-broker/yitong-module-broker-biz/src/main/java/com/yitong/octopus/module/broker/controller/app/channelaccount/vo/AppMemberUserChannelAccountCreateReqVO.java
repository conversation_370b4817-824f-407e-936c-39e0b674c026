package com.yitong.octopus.module.broker.controller.app.channelaccount.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "用户 APP - 经纪人渠道账户信息创建 Request VO")
@Data
@ToString(callSuper = true)
public class AppMemberUserChannelAccountCreateReqVO {

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "平台地址", example = "https://www.iocoder.cn")
    private String platformUrl;

}
