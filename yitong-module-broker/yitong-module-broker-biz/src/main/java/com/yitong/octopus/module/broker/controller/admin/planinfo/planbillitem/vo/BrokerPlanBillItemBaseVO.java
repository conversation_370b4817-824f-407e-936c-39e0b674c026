package com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划结算信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanBillItemBaseVO {

    @Schema(description = "计划ID", example = "17116")
    private Long planId;

    @Schema(description = "所属商家ID", example = "14674")
    private Long spId;

    @Schema(description = "商品SpuId", example = "1579")
    private Long spuId;

    @Schema(description = "商品SkuId", example = "16681")
    private Long skuId;

    @Schema(description = "结算项ID", example = "28790")
    private Long billItemId;

    @Schema(description = "结算项值")
    private String billItemValue;

}
