package com.yitong.octopus.module.broker.service.planstore;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStorePageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planstore.BrokerPlanStoreConvert;
import com.yitong.octopus.module.broker.dal.mysql.planstore.BrokerPlanStoreMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 计划门店信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanStoreServiceImpl implements BrokerPlanStoreService {

    @Resource
    private BrokerPlanStoreMapper planStoreMapper;

    @Override
    public Long createPlanStore(BrokerPlanStoreReqVO createReqVO) {
        // 插入
        BrokerPlanStoreDO planStore = BrokerPlanStoreConvert.INSTANCE.convert(createReqVO);
        planStoreMapper.insert(planStore);
        // 返回
        return planStore.getId();
    }

    @Override
    public void updatePlanStore(BrokerPlanStoreReqVO updateReqVO) {
        // 校验存在
        validatePlanStoreExists(updateReqVO.getId());
        // 更新
        BrokerPlanStoreDO updateObj = BrokerPlanStoreConvert.INSTANCE.convert(updateReqVO);
        planStoreMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanStore(Long id) {
        // 校验存在
        validatePlanStoreExists(id);
        // 删除
        planStoreMapper.deleteById(id);
    }

    private void validatePlanStoreExists(Long id) {
        if (planStoreMapper.selectById(id) == null) {
            throw exception(PLAN_STORE_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanStoreDO getPlanStore(Long id) {
        return planStoreMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanStoreDO> getPlanStoreList(Collection<Long> ids) {
        return planStoreMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanStoreDO> getPlanStorePage(BrokerPlanStorePageReqVO pageReqVO) {
        return planStoreMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanStoreDO> getPlanStoreList(BrokerPlanStoreExportReqVO exportReqVO) {
        return planStoreMapper.selectList(exportReqVO);
    }
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanStoreList(Long planId, List<BrokerPlanStoreReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanStoreDO> list = getPlanStoreListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanStoreDO> saveList =   Lists.newArrayList();
        List<BrokerPlanStoreDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanStoreDO so = BrokerPlanStoreConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanStoreDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planStoreMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planStoreMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planStoreMapper.updateBatchById(updateList);
        }
    }

    @Override
    public void deletePlanStoreByPlanId(Long planId) {
        planStoreMapper.delete(new LambdaQueryWrapperX<BrokerPlanStoreDO>()
                .eq(BrokerPlanStoreDO::getPlanId,planId));
    }

    @Override
    public List<BrokerPlanStoreDO> getPlanStoreListByPlanId(Long planId) {
        return planStoreMapper.selectList(new LambdaQueryWrapperX<BrokerPlanStoreDO>()
                .eq(BrokerPlanStoreDO::getPlanId,planId));
    }
}
