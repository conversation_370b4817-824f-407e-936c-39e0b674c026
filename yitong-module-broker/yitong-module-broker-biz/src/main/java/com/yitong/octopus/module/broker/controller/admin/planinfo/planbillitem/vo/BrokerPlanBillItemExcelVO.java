package com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划结算信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanBillItemExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("所属商家ID")
    private Long spId;

    @ExcelProperty("商品SpuId")
    private Long spuId;

    @ExcelProperty("商品SkuId")
    private Long skuId;

    @ExcelProperty("结算项ID")
    private Long billItemId;

    @ExcelProperty("结算项值")
    private String billItemValue;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
