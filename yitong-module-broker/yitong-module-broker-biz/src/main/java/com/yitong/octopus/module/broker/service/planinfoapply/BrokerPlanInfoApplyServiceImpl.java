package com.yitong.octopus.module.broker.service.planinfoapply;

import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.module.broker.enums.PlanApplyStatusEnum;
import com.yitong.octopus.module.broker.service.channelaccount.MemberUserChannelAccountService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planinfoapply.BrokerPlanInfoApplyConvert;
import com.yitong.octopus.module.broker.dal.mysql.planinfoapply.BrokerPlanInfoApplyMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划申请 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanInfoApplyServiceImpl implements BrokerPlanInfoApplyService {

    @Resource
    private BrokerPlanInfoApplyMapper planInfoApplyMapper;

    @Resource
    private MemberUserChannelAccountService memberUserChannelAccountService;

    @Override
    public Long applyPlanInfo(BrokerPlanInfoApplyReqVO createReqVO) {
        // 插入
        BrokerPlanInfoApplyDO checkPlanInfoApply = getEffectivePlanInfoApply(createReqVO.getPlanId(),createReqVO.getTalentAccountId());
        if (ObjectUtil.isNotNull(checkPlanInfoApply)){
            throw exception(PLAN_APPLY_EXISTS);
        }

        BrokerPlanInfoApplyDO planInfoApply = BrokerPlanInfoApplyConvert.INSTANCE.convert(createReqVO);
        MemberUserChannelAccountDO  channelAccount = memberUserChannelAccountService.getMemberUserChannelAccount(createReqVO.getTalentAccountId(), createReqVO.getTalentId());
        if (ObjectUtil.isNull(channelAccount)){
            throw exception(MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS);
        }
        planInfoApply.setStatus(PlanApplyStatusEnum.APPLY.getStatus());
        planInfoApplyMapper.insert(planInfoApply);
        // 返回
        return planInfoApply.getId();
    }

    @Override
    public void confirmPlanInfoApply(Collection<Long> ids) {
        BrokerPlanInfoApplyDO updateObjs = new BrokerPlanInfoApplyDO();
        updateObjs.setStatus(PlanApplyStatusEnum.PROGRESS.getStatus());
        planInfoApplyMapper.update(updateObjs,new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
                .eq(BrokerPlanInfoApplyDO::getStatus,PlanApplyStatusEnum.APPLY.getStatus())
                .in(BrokerPlanInfoApplyDO::getId,ids));
    }

    @Override
    public void rejectPlanInfoApply(Collection<Long> ids) {
        BrokerPlanInfoApplyDO updateObjs = new BrokerPlanInfoApplyDO();
        updateObjs.setStatus(PlanApplyStatusEnum.REJECT.getStatus());
        planInfoApplyMapper.update(updateObjs,new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
                .eq(BrokerPlanInfoApplyDO::getStatus,PlanApplyStatusEnum.APPLY.getStatus())
                .in(BrokerPlanInfoApplyDO::getId,ids));
    }

    @Override
    public void finishPlanInfoApply(BrokerPlanInfoApplyFinishReqVO reqVO) {
//        BrokerPlanInfoApplyDO planInfoApply = BrokerPlanInfoApplyConvert.INSTANCE.convert(reqVO);
        BrokerPlanInfoApplyDO updateObjs = new BrokerPlanInfoApplyDO();
        updateObjs.setStatus(PlanApplyStatusEnum.FINISH.getStatus());
        updateObjs.setWorksId(reqVO.getWorksId());
        updateObjs.setWorksShortUrl(reqVO.getWorksShortUrl());
        updateObjs.setWorksUrl(reqVO.getWorksUrl());
        planInfoApplyMapper.update(updateObjs,new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
                .eq(BrokerPlanInfoApplyDO::getStatus,PlanApplyStatusEnum.PROGRESS.getStatus())
                .eq(BrokerPlanInfoApplyDO::getId,reqVO.getId()));
    }

    @Override
    public void deletePlanInfoApply(Long id) {
        // 校验存在
        validatePlanInfoApplyExists(id);
        // 删除
        planInfoApplyMapper.deleteById(id);
    }

    private void validatePlanInfoApplyExists(Long id) {
        if (planInfoApplyMapper.selectById(id) == null) {
            throw exception(PLAN_INFO_APPLY_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanInfoApplyDO getPlanInfoApply(Long id) {
        return planInfoApplyMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanInfoApplyDO> getPlanInfoApplyList(Collection<Long> ids) {
        return planInfoApplyMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanInfoApplyDO> getPlanInfoApplyPage(BrokerPlanInfoApplyPageReqVO pageReqVO) {
        return planInfoApplyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanInfoApplyDO> getPlanInfoApplyList(BrokerPlanInfoApplyExportReqVO exportReqVO) {
        return planInfoApplyMapper.selectList(exportReqVO);
    }

    /**
     * 获取有效的申请【非拒绝】
     * @param planId
     * @param channelAccountId
     * @return
     */
    public BrokerPlanInfoApplyDO getEffectivePlanInfoApply(Long planId, Long channelAccountId) {
        // 检查是否已经申请过了
        return planInfoApplyMapper.selectOne(new LambdaQueryWrapperX<BrokerPlanInfoApplyDO>()
            .eq(BrokerPlanInfoApplyDO::getPlanId,planId)
            .eq(BrokerPlanInfoApplyDO::getTalentAccountId,channelAccountId)
            .ne(BrokerPlanInfoApplyDO::getStatus,PlanApplyStatusEnum.REJECT.getStatus())
        );
    }

}
