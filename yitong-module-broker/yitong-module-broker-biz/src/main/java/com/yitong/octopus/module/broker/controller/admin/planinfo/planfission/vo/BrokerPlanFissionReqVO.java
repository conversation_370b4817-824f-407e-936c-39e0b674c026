package com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划招募费用更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanFissionReqVO extends BrokerPlanFissionBaseVO {

    @Schema(description = "编号", example = "22177")
    private Long id;

}
