package com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划渠道信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanChannelBaseVO {

    @Schema(description = "计划ID", example = "30376")
    private Long planId;

    @Schema(description = "渠道Id", example = "30020")
    private Long channelId;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "经纪人带货能力要求")
    private Boolean talentLevelNeed;

    @Schema(description = "经纪人等级")
    private Integer talentLevel;

    @Schema(description = "经纪人能力要求")
    private Boolean talentMeritNeed;

    @Schema(description = "经纪人能力等级")
    private Integer talentMeritLevel;

    @Schema(description = "经纪人粉丝数要求")
    private Boolean talentFansNeed;

    @Schema(description = "经纪人粉丝数")
    private Integer talentFans;
}
