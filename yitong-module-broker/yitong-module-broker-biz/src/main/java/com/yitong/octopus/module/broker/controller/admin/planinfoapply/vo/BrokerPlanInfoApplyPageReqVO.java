package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoApplyPageReqVO extends PageParam {

    @Schema(description = "计划ID", example = "5926")
    private Long planId;

    @Schema(description = "经纪人Id", example = "19797")
    private Long talentId;

    @Schema(description = "经纪人渠道Id", example = "30311")
    private Long talentAccountId;

    @Schema(description = "渠道Id", example = "13473")
    private Long channelId;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "状态: 0 待审核，1 已通过，2已拒绝，3进行中，4已完成，5已结算", example = "2")
    private Byte status;

    @Schema(description = "作品ID", example = "24009")
    private Long worksId;

    @Schema(description = "作品短连接", example = "https://www.iocoder.cn")
    private String worksShortUrl;

    @Schema(description = "作品连接", example = "https://www.iocoder.cn")
    private String worksUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
