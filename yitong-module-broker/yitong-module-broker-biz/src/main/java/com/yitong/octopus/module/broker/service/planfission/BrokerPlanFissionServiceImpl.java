package com.yitong.octopus.module.broker.service.planfission;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import com.yitong.octopus.module.broker.convert.plancontact.BrokerPlanContactConvert;
import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planfission.BrokerPlanFissionConvert;
import com.yitong.octopus.module.broker.dal.mysql.planfission.BrokerPlanFissionMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划招募费用 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanFissionServiceImpl implements BrokerPlanFissionService {

    @Resource
    private BrokerPlanFissionMapper planFissionMapper;

    @Override
    public Long createPlanFission(BrokerPlanFissionReqVO createReqVO) {
        // 插入
        BrokerPlanFissionDO planFission = BrokerPlanFissionConvert.INSTANCE.convert(createReqVO);
        planFissionMapper.insert(planFission);
        // 返回
        return planFission.getId();
    }

    @Override
    public void updatePlanFission(BrokerPlanFissionReqVO updateReqVO) {
        // 校验存在
        validatePlanFissionExists(updateReqVO.getId());
        // 更新
        BrokerPlanFissionDO updateObj = BrokerPlanFissionConvert.INSTANCE.convert(updateReqVO);
        planFissionMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanFission(Long id) {
        // 校验存在
        validatePlanFissionExists(id);
        // 删除
        planFissionMapper.deleteById(id);
    }

    private void validatePlanFissionExists(Long id) {
        if (planFissionMapper.selectById(id) == null) {
            throw exception(PLAN_FISSION_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanFissionDO getPlanFission(Long id) {
        return planFissionMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanFissionDO> getPlanFissionList(Collection<Long> ids) {
        return planFissionMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanFissionDO> getPlanFissionPage(BrokerPlanFissionPageReqVO pageReqVO) {
        return planFissionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanFissionDO> getPlanFissionList(BrokerPlanFissionExportReqVO exportReqVO) {
        return planFissionMapper.selectList(exportReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanFissionList(Long planId, List<BrokerPlanFissionReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanFissionDO> list = getPlanFissionListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanFissionDO> saveList =   Lists.newArrayList();
        List<BrokerPlanFissionDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanFissionDO so = BrokerPlanFissionConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanFissionDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planFissionMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planFissionMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planFissionMapper.updateBatchById(updateList);
        }
    }

    @Override
    public void deletePlanFissionByPlanId(Long planId) {
        planFissionMapper.delete(new LambdaQueryWrapperX<BrokerPlanFissionDO>()
                .eq(BrokerPlanFissionDO::getPlanId,planId));
    }

    @Override
    public List<BrokerPlanFissionDO> getPlanFissionListByPlanId(Long planId) {
        return planFissionMapper.selectList(new LambdaQueryWrapperX<BrokerPlanFissionDO>()
                .eq(BrokerPlanFissionDO::getPlanId,planId));
    }

}
