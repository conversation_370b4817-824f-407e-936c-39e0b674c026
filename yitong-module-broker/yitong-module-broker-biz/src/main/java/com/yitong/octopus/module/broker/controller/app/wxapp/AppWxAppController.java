package com.yitong.octopus.module.broker.controller.app.wxapp;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

@Tag(name = "经纪人微信小程序")
@RestController
@RequestMapping("/broker-member/wx-app")
@Validated
@Slf4j
public class AppWxAppController {

    @Resource
    private WxMaService wxMaService;

    @PostMapping("/get-phone-no")
    @Operation(summary = "微信手机号接码")
    public CommonResult<WxMaPhoneNumberInfo> getPhoneMobile(@RequestParam("mobileCode") String mobileCode) throws WxErrorException {
        return success(wxMaService.getUserService().getPhoneNoInfo(mobileCode));
    }

}
