package com.yitong.octopus.module.broker.dal.mysql.planrefund;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;

/**
 * 经纪人计划取消 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanRefundMapper extends BaseMapperX<BrokerPlanRefundDO> {

    default PageResult<BrokerPlanRefundDO> selectPage(BrokerPlanRefundPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanRefundDO>()
                .eqIfPresent(BrokerPlanRefundDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanRefundDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanRefundDO::getRefundMsg, reqVO.getRefundMsg())
                .eqIfPresent(BrokerPlanRefundDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanRefundDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanRefundDO::getId));
    }

    default List<BrokerPlanRefundDO> selectList(BrokerPlanRefundExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanRefundDO>()
                .eqIfPresent(BrokerPlanRefundDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanRefundDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanRefundDO::getRefundMsg, reqVO.getRefundMsg())
                .eqIfPresent(BrokerPlanRefundDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanRefundDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanRefundDO::getId));
    }

}
