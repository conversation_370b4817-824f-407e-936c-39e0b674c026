package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
* 经纪人计划申请 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanInfoApplyBaseVO {

    @Schema(description = "计划ID", example = "5926")
    private Long planId;

    @Schema(description = "经纪人Id", example = "19797")
    private Long talentId;

    @Schema(description = "经纪人渠道Id", example = "30311")
    private Long talentAccountId;

    @Schema(description = "渠道Id", example = "13473")
    private Long channelId;

    @Schema(description = "渠道编码")
    private String channelCode;

    @Schema(description = "状态: 1 待审核，2已拒绝，3进行中，4已完成，5已结算", example = "2")
    private Integer status;

    @Schema(description = "作品ID", example = "24009")
    private String worksId;

    @Schema(description = "作品短连接", example = "https://www.iocoder.cn")
    private String worksShortUrl;

    @Schema(description = "作品连接", example = "https://www.iocoder.cn")
    private String worksUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
