package com.yitong.octopus.module.broker.service.channelaccount;

import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.module.broker.enums.AuditStatusEnum;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.channelaccount.MemberUserChannelAccountConvert;
import com.yitong.octopus.module.broker.dal.mysql.memberuserchannelaccount.MemberUserChannelAccountMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人渠道账户信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberUserChannelAccountServiceImpl implements MemberUserChannelAccountService {

    @Resource
    private MemberUserChannelAccountMapper memberUserChannelAccountMapper;

    @Override
    public Long createMemberUserChannelAccount(MemberUserChannelAccountCreateReqVO createReqVO) {
        // 插入
        MemberUserChannelAccountDO memberUserChannelAccount = MemberUserChannelAccountConvert.INSTANCE.convert(createReqVO);
        memberUserChannelAccount.setStatus(true);
        memberUserChannelAccount.setAuditStatus(AuditStatusEnum.CREATE.getStatus());
        memberUserChannelAccountMapper.insert(memberUserChannelAccount);
        // 返回
        return memberUserChannelAccount.getId();
    }

    @Override
    public void updateMemberUserChannelAccount(MemberUserChannelAccountUpdateReqVO updateReqVO) {
        // 校验存在
        validateMemberUserChannelAccountExists(updateReqVO.getId());
        // 更新
        MemberUserChannelAccountDO updateObj = MemberUserChannelAccountConvert.INSTANCE.convert(updateReqVO);
        memberUserChannelAccountMapper.updateById(updateObj);
    }

    @Override
    public void deleteMemberUserChannelAccount(Long id) {
        // 校验存在
        validateMemberUserChannelAccountExists(id);
        // 删除
        memberUserChannelAccountMapper.deleteById(id);
    }

    private void validateMemberUserChannelAccountExists(Long id) {
        if (memberUserChannelAccountMapper.selectById(id) == null) {
            throw exception(MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS);
        }
    }

    @Override
    public MemberUserChannelAccountDO getMemberUserChannelAccount(Long id,Long userId) {
        MemberUserChannelAccountDO accountDO = memberUserChannelAccountMapper.selectById(id);
        if (ObjectUtil.isNull(userId)){
            return accountDO;
        }
        if (ObjectUtil.isNotNull(accountDO) && !accountDO.getUserId().equals(userId)){
            return null;
        }
        return accountDO;
    }

    @Override
    public List<MemberUserChannelAccountDO> getMemberUserChannelAccountList(Collection<Long> ids) {
        return memberUserChannelAccountMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<MemberUserChannelAccountDO> getMemberUserChannelAccountPage(MemberUserChannelAccountPageReqVO pageReqVO) {
        return memberUserChannelAccountMapper.selectPage(pageReqVO);
    }

    @Override
    public List<MemberUserChannelAccountDO> getMemberUserChannelAccountList(MemberUserChannelAccountExportReqVO exportReqVO) {
        return memberUserChannelAccountMapper.selectList(exportReqVO);
    }

    @Override
    public void updateUserStatus(Long id, Boolean status) {
        // 校验用户存在
        validateMemberUserChannelAccountExists(id);
        // 更新状态
        MemberUserChannelAccountDO updateObj = new MemberUserChannelAccountDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        memberUserChannelAccountMapper.updateById(updateObj);
    }

    @Override
    public void updateUserAuditStatus(Long id, Integer auditStatus, String remark) {
        // 校验用户存在
        MemberUserChannelAccountDO ado = memberUserChannelAccountMapper.selectById(id);
        if (ObjectUtil.isNull(ado)) {
            throw exception(MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS);
        }
        //检查审核状态
        if (!AuditStatusEnum.CREATE.getStatus().equals( ado.getAuditStatus())){
            throw exception(MEMBER_USER_CHANNEL_ACCOUNT_AUDIT_STATUS_ERROR);
        }

        // 更新状态
        MemberUserChannelAccountDO updateObj = new MemberUserChannelAccountDO();
        updateObj.setId(id);
        updateObj.setAuditStatus(auditStatus);
        memberUserChannelAccountMapper.updateById(updateObj);
    }

}
