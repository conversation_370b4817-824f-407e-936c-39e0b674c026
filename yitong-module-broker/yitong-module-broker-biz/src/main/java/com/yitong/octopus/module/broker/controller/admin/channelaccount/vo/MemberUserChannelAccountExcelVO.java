package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人渠道账户信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class MemberUserChannelAccountExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("用户ID")
    private Long userId;

    @ExcelProperty("渠道编码")
    private String channelCode;

    @ExcelProperty("头像")
    private String avatar;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("省/直辖市")
    private String province;

    @ExcelProperty("城市")
    private String city;

    @ExcelProperty("地区")
    private String area;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("平台ID")
    private String platformId;

    @ExcelProperty("平台号")
    private String platformNo;

    @ExcelProperty("平台名称")
    private String platformNanme;

    @ExcelProperty("平台地址")
    private String platformUrl;

    @ExcelProperty("平台等级")
    private Integer platformLevel;

    @ExcelProperty("平台带货分")
    private Integer platformThrScore;

    @ExcelProperty("平台粉丝数")
    private Integer platformFansCount;

    @ExcelProperty("平台粉丝级别")
    private Integer platformFansLevel;

    @ExcelProperty("审核状态")
    private Integer auditStatus;

    @ExcelProperty("状态")
    private Boolean status;

}
