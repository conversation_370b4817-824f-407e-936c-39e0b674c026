package com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划联系信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanContactPageReqVO extends PageParam {

    @Schema(description = "计划ID", example = "26998")
    private Long planId;

    @Schema(description = "联系方式类型：1.电话，2.微信号，3.企微二维码，4.微信群二维码", example = "2")
    private Integer type;

    @Schema(description = "组件类型:1 input，2 image_upload", example = "2")
    private Integer widgetType;

    @Schema(description = "联系方式内容")
    private String contact;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
