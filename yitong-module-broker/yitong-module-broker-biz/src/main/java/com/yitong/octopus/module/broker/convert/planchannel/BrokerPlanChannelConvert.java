package com.yitong.octopus.module.broker.convert.planchannel;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;

/**
 * 经纪人计划渠道信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanChannelConvert {

    BrokerPlanChannelConvert INSTANCE = Mappers.getMapper(BrokerPlanChannelConvert.class);

    BrokerPlanChannelDO convert(BrokerPlanChannelReqVO bean);

    BrokerPlanChannelRespVO convert(BrokerPlanChannelDO bean);

    List<BrokerPlanChannelRespVO> convertList(List<BrokerPlanChannelDO> list);

    PageResult<BrokerPlanChannelRespVO> convertPage(PageResult<BrokerPlanChannelDO> page);

    List<BrokerPlanChannelExcelVO> convertList02(List<BrokerPlanChannelDO> list);

}
