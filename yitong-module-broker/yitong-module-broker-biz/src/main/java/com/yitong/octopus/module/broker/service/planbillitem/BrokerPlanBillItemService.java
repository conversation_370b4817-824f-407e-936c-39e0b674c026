package com.yitong.octopus.module.broker.service.planbillitem;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划结算信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanBillItemService {

    /**
     * 创建经纪人计划结算信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanBillItem(@Valid BrokerPlanBillItemReqVO createReqVO);

    /**
     * 更新经纪人计划结算信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanBillItem(@Valid BrokerPlanBillItemReqVO updateReqVO);

    /**
     * 删除经纪人计划结算信息
     *
     * @param id 编号
     */
    void deletePlanBillItem(Long id);

    /**
     * 获得经纪人计划结算信息
     *
     * @param id 编号
     * @return 经纪人计划结算信息
     */
    BrokerPlanBillItemDO getPlanBillItem(Long id);

    /**
     * 获得经纪人计划结算信息列表
     *
     * @param ids 编号
     * @return 经纪人计划结算信息列表
     */
    List<BrokerPlanBillItemDO> getPlanBillItemList(Collection<Long> ids);

    /**
     * 获得经纪人计划结算信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划结算信息分页
     */
    PageResult<BrokerPlanBillItemDO> getPlanBillItemPage(BrokerPlanBillItemPageReqVO pageReqVO);

    /**
     * 获得经纪人计划结算信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划结算信息列表
     */
    List<BrokerPlanBillItemDO> getPlanBillItemList(BrokerPlanBillItemExportReqVO exportReqVO);

    /**
     * 创建经纪人计划结算信息
     *
     * @param planId 计划Id
     * @param createReqVOList 创建信息列表
     */
    void createOrUpdatePlanBillItemList(Long planId, List<BrokerPlanBillItemReqVO> createReqVOList);

    /**
     * 删除经纪人计划结算信息
     *
     * @param planId 计划Id
     */
    void deletePlanBillItemByPlanId(Long planId);

    /**
     * 删除经纪人计划结算信息
     *
     * @param planId 计划Id
     * @return 经纪人计划结算信息列表
     */
    List<BrokerPlanBillItemDO> getPlanBillItemListByPlanId(Long planId);
}
