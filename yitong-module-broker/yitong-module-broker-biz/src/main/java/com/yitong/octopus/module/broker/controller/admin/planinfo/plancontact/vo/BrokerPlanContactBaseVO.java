package com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划联系信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanContactBaseVO {

    @Schema(description = "计划ID", example = "26998")
    private Long planId;

    @Schema(description = "联系方式类型：1.电话，2.微信号，3.企微二维码，4.微信群二维码", example = "2")
    private Integer type;

    @Schema(description = "组件类型:1 input，2 image_upload", example = "2")
    private Integer widgetType;

    @Schema(description = "联系方式内容")
    private String contact;

}
