package com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划联系信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanContactExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("联系方式类型：1.电话，2.微信号，3.企微二维码，4.微信群二维码")
    private Integer type;

    @ExcelProperty("组件类型:1 input，2 image_upload")
    private Integer widgetType;

    @ExcelProperty("联系方式内容")
    private String contact;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
