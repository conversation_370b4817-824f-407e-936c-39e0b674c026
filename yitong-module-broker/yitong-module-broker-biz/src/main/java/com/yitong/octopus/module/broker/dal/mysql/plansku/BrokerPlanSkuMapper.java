package com.yitong.octopus.module.broker.dal.mysql.plansku;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划sku信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanSkuMapper extends BaseMapperX<BrokerPlanSkuDO> {

    default PageResult<BrokerPlanSkuDO> selectPage(BrokerPlanSkuPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanSkuDO>()
                .eqIfPresent(BrokerPlanSkuDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanSkuDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanSkuDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(BrokerPlanSkuDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(BrokerPlanSkuDO::getCps, reqVO.getCps())
                .betweenIfPresent(BrokerPlanSkuDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanSkuDO::getId));
    }

    default List<BrokerPlanSkuDO> selectList(BrokerPlanSkuExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanSkuDO>()
                .eqIfPresent(BrokerPlanSkuDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanSkuDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanSkuDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(BrokerPlanSkuDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(BrokerPlanSkuDO::getCps, reqVO.getCps())
                .betweenIfPresent(BrokerPlanSkuDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanSkuDO::getId));
    }

}
