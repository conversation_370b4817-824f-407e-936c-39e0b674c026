package com.yitong.octopus.module.broker.dal.dataobject.planinfoapply;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划申请 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_info_apply")
@KeySequence("broker_plan_info_apply_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanInfoApplyDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 经纪人Id
     */
    private Long talentId;
    /**
     * 经纪人渠道Id
     */
    private Long talentAccountId;
    /**
     * 渠道Id
     */
    private Long channelId;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 状态: 0 待审核，1 已通过，2已拒绝，3进行中，4已完成，5已结算
     */
    private Integer status;
    /**
     * 作品ID
     */
    private String worksId;
    /**
     * 作品短连接
     */
    private String worksShortUrl;
    /**
     * 作品连接
     */
    private String worksUrl;
    /**
     * 备注
     */
    private String remark;

}
