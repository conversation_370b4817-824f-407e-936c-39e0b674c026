package com.yitong.octopus.module.broker.dal.dataobject.plancontact;

import com.yitong.octopus.framework.mybatis.core.dataobject.BaseCuDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划联系信息 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_contact")
@KeySequence("broker_plan_contact_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanContactDO extends BaseCuDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 联系方式类型：1.电话，2.微信号，3.企微二维码，4.微信群二维码
     */
    private Byte type;
    /**
     * 组件类型:1 input，2 image_upload
     */
    private Byte widgetType;
    /**
     * 联系方式内容
     */
    private String contact;

}
