package com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划渠道信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanChannelExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("渠道Id")
    private Long channelId;

    @ExcelProperty("渠道编码")
    private String channelCode;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
