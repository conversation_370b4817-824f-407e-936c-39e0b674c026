package com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 计划门店信息 Excel 导出 Request VO，参数和 BrokerPlanStorePageReqVO 是一致的")
@Data
public class BrokerPlanStoreExportReqVO {

    @Schema(description = "计划ID", example = "32502")
    private Long planId;

    @Schema(description = "所属商家ID", example = "1052")
    private Long spId;

    @Schema(description = "门店Id", example = "27984")
    private Long storeId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
