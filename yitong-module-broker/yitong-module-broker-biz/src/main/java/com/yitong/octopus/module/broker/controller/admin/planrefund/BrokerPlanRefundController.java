package com.yitong.octopus.module.broker.controller.admin.planrefund;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;
import com.yitong.octopus.module.broker.convert.planrefund.BrokerPlanRefundConvert;
import com.yitong.octopus.module.broker.service.planrefund.BrokerPlanRefundService;

@Tag(name = "管理后台 - 经纪人计划取消")
@RestController
@RequestMapping("/broker/plan-refund")
@Validated
public class BrokerPlanRefundController {

    @Resource
    private BrokerPlanRefundService planRefundService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人计划取消")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:create')")
    public CommonResult<Long> createPlanRefund(@Valid @RequestBody BrokerPlanRefundCreateReqVO createReqVO) {
        return success(planRefundService.createPlanRefund(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人计划取消")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:update')")
    public CommonResult<Boolean> updatePlanRefund(@Valid @RequestBody BrokerPlanRefundUpdateReqVO updateReqVO) {
        planRefundService.updatePlanRefund(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人计划取消")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:delete')")
    public CommonResult<Boolean> deletePlanRefund(@RequestParam("id") Long id) {
        planRefundService.deletePlanRefund(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人计划取消")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:query')")
    public CommonResult<BrokerPlanRefundRespVO> getPlanRefund(@RequestParam("id") Long id) {
        BrokerPlanRefundDO planRefund = planRefundService.getPlanRefund(id);
        return success(BrokerPlanRefundConvert.INSTANCE.convert(planRefund));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人计划取消列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:query')")
    public CommonResult<List<BrokerPlanRefundRespVO>> getPlanRefundList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerPlanRefundDO> list = planRefundService.getPlanRefundList(ids);
        return success(BrokerPlanRefundConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人计划取消分页")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:query')")
    public CommonResult<PageResult<BrokerPlanRefundRespVO>> getPlanRefundPage(@Valid BrokerPlanRefundPageReqVO pageVO) {
        PageResult<BrokerPlanRefundDO> pageResult = planRefundService.getPlanRefundPage(pageVO);
        return success(BrokerPlanRefundConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人计划取消 Excel")
    @PreAuthorize("@ss.hasPermission('broker:plan-refund:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanRefundExcel(@Valid BrokerPlanRefundExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerPlanRefundDO> list = planRefundService.getPlanRefundList(exportReqVO);
        // 导出 Excel
        List<BrokerPlanRefundExcelVO> datas = BrokerPlanRefundConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人计划取消.xls", "数据", BrokerPlanRefundExcelVO.class, datas);
    }

}
