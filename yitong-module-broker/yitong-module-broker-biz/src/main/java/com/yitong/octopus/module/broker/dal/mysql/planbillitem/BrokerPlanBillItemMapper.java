package com.yitong.octopus.module.broker.dal.mysql.planbillitem;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划结算信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanBillItemMapper extends BaseMapperX<BrokerPlanBillItemDO> {

    default PageResult<BrokerPlanBillItemDO> selectPage(BrokerPlanBillItemPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanBillItemDO>()
                .eqIfPresent(BrokerPlanBillItemDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanBillItemDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanBillItemDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(BrokerPlanBillItemDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(BrokerPlanBillItemDO::getBillItemId, reqVO.getBillItemId())
                .eqIfPresent(BrokerPlanBillItemDO::getBillItemValue, reqVO.getBillItemValue())
                .betweenIfPresent(BrokerPlanBillItemDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanBillItemDO::getId));
    }

    default List<BrokerPlanBillItemDO> selectList(BrokerPlanBillItemExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanBillItemDO>()
                .eqIfPresent(BrokerPlanBillItemDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanBillItemDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanBillItemDO::getSpuId, reqVO.getSpuId())
                .eqIfPresent(BrokerPlanBillItemDO::getSkuId, reqVO.getSkuId())
                .eqIfPresent(BrokerPlanBillItemDO::getBillItemId, reqVO.getBillItemId())
                .eqIfPresent(BrokerPlanBillItemDO::getBillItemValue, reqVO.getBillItemValue())
                .betweenIfPresent(BrokerPlanBillItemDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanBillItemDO::getId));
    }

}
