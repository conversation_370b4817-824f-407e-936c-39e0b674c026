package com.yitong.octopus.module.broker.dal.dataobject.planfission;

import com.yitong.octopus.framework.mybatis.core.dataobject.BaseCuDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划招募费用 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_fission")
@KeySequence("broker_plan_fission_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanFissionDO extends BaseCuDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 经纪人等级
     */
    private Integer talentLevel;
    /**
     * 费用，招募经纪人的费用
     */
    private Long amount;

}
