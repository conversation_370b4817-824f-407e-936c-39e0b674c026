package com.yitong.octopus.module.broker.convert.planbillinfocheck;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;

/**
 * 经纪人计划账单确认记录 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanBillInfoCheckConvert {

    BrokerPlanBillInfoCheckConvert INSTANCE = Mappers.getMapper(BrokerPlanBillInfoCheckConvert.class);

    BrokerPlanBillInfoCheckDO convert(BrokerPlanBillInfoCheckCreateReqVO bean);

    BrokerPlanBillInfoCheckDO convert(BrokerPlanBillInfoCheckUpdateReqVO bean);

    BrokerPlanBillInfoCheckRespVO convert(BrokerPlanBillInfoCheckDO bean);

    List<BrokerPlanBillInfoCheckRespVO> convertList(List<BrokerPlanBillInfoCheckDO> list);

    PageResult<BrokerPlanBillInfoCheckRespVO> convertPage(PageResult<BrokerPlanBillInfoCheckDO> page);

    List<BrokerPlanBillInfoCheckExcelVO> convertList02(List<BrokerPlanBillInfoCheckDO> list);

}
