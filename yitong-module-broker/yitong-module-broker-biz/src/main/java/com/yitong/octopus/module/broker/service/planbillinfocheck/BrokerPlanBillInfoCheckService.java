package com.yitong.octopus.module.broker.service.planbillinfocheck;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划账单确认记录 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanBillInfoCheckService {

    /**
     * 创建经纪人计划账单确认记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanBillInfoCheck(@Valid BrokerPlanBillInfoCheckCreateReqVO createReqVO);

    /**
     * 更新经纪人计划账单确认记录
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanBillInfoCheck(@Valid BrokerPlanBillInfoCheckUpdateReqVO updateReqVO);

    /**
     * 删除经纪人计划账单确认记录
     *
     * @param id 编号
     */
    void deletePlanBillInfoCheck(Long id);

    /**
     * 获得经纪人计划账单确认记录
     *
     * @param id 编号
     * @return 经纪人计划账单确认记录
     */
    BrokerPlanBillInfoCheckDO getPlanBillInfoCheck(Long id);

    /**
     * 获得经纪人计划账单确认记录列表
     *
     * @param ids 编号
     * @return 经纪人计划账单确认记录列表
     */
    List<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckList(Collection<Long> ids);

    /**
     * 获得经纪人计划账单确认记录分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划账单确认记录分页
     */
    PageResult<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckPage(BrokerPlanBillInfoCheckPageReqVO pageReqVO);

    /**
     * 获得经纪人计划账单确认记录列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划账单确认记录列表
     */
    List<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckList(BrokerPlanBillInfoCheckExportReqVO exportReqVO);

}
