package com.yitong.octopus.module.broker.dal.mysql.planchannel;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划渠道信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanChannelMapper extends BaseMapperX<BrokerPlanChannelDO> {

    default PageResult<BrokerPlanChannelDO> selectPage(BrokerPlanChannelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanChannelDO>()
                .eqIfPresent(BrokerPlanChannelDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanChannelDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(BrokerPlanChannelDO::getChannelCode, reqVO.getChannelCode())
                .betweenIfPresent(BrokerPlanChannelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanChannelDO::getId));
    }

    default List<BrokerPlanChannelDO> selectList(BrokerPlanChannelExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanChannelDO>()
                .eqIfPresent(BrokerPlanChannelDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanChannelDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(BrokerPlanChannelDO::getChannelCode, reqVO.getChannelCode())
                .betweenIfPresent(BrokerPlanChannelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanChannelDO::getId));
    }

}
