package com.yitong.octopus.module.broker.convert.channelaccount;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.broker.controller.app.channelaccount.vo.AppMemberUserChannelAccountRespVO;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 经纪人渠道账户信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AppMemberUserChannelAccountConvert {

    AppMemberUserChannelAccountConvert INSTANCE = Mappers.getMapper(AppMemberUserChannelAccountConvert.class);

    AppMemberUserChannelAccountRespVO convert(MemberUserChannelAccountDO bean);

    List<AppMemberUserChannelAccountRespVO> convertList(List<MemberUserChannelAccountDO> list);

    PageResult<AppMemberUserChannelAccountRespVO> convertPage(PageResult<MemberUserChannelAccountDO> page);


}
