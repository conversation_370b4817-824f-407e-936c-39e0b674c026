package com.yitong.octopus.module.broker.convert.address;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.broker.api.address.dto.AddressRespDTO;
import com.yitong.octopus.module.broker.controller.app.address.vo.AppAddressCreateReqVO;
import com.yitong.octopus.module.broker.controller.app.address.vo.AppAddressRespVO;
import com.yitong.octopus.module.broker.controller.app.address.vo.AppAddressUpdateReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.address.AddressDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 用户收件地址 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface AddressConvert {

    AddressConvert INSTANCE = Mappers.getMapper(AddressConvert.class);

    AddressDO convert(AppAddressCreateReqVO bean);

    AddressDO convert(AppAddressUpdateReqVO bean);

    AppAddressRespVO convert(AddressDO bean);

    List<AppAddressRespVO> convertList(List<AddressDO> list);

    PageResult<AppAddressRespVO> convertPage(PageResult<AddressDO> page);

    AddressRespDTO convert02(AddressDO bean);

}
