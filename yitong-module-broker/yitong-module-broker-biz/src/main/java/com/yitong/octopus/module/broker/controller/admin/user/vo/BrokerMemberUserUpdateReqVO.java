package com.yitong.octopus.module.broker.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人用户更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerMemberUserUpdateReqVO extends BrokerMemberUserBaseVO {

    @Schema(description = "编号", required = true, example = "16161")
    @NotNull(message = "编号不能为空")
    private Long id;

}
