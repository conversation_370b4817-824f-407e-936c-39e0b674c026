package com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划sku信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanSkuExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("所属商家ID")
    private Long spId;

    @ExcelProperty("商品SpuId")
    private Long spuId;

    @ExcelProperty("商品SkuId")
    private Long skuId;

    @ExcelProperty("cps分佣比例(小数点4位)")
    private Integer cps;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
