package com.yitong.octopus.module.broker.service.plancontact;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划联系信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanContactService {

    /**
     * 创建经纪人计划联系信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanContact(@Valid BrokerPlanContactReqVO createReqVO);

    /**
     * 更新经纪人计划联系信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanContact(@Valid BrokerPlanContactReqVO updateReqVO);

    /**
     * 删除经纪人计划联系信息
     *
     * @param id 编号
     */
    void deletePlanContact(Long id);

    /**
     * 获得经纪人计划联系信息
     *
     * @param id 编号
     * @return 经纪人计划联系信息
     */
    BrokerPlanContactDO getPlanContact(Long id);

    /**
     * 获得经纪人计划联系信息列表
     *
     * @param ids 编号
     * @return 经纪人计划联系信息列表
     */
    List<BrokerPlanContactDO> getPlanContactList(Collection<Long> ids);

    /**
     * 获得经纪人计划联系信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划联系信息分页
     */
    PageResult<BrokerPlanContactDO> getPlanContactPage(BrokerPlanContactPageReqVO pageReqVO);

    /**
     * 获得经纪人计划联系信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划联系信息列表
     */
    List<BrokerPlanContactDO> getPlanContactList(BrokerPlanContactExportReqVO exportReqVO);

    /**
     * 创建经纪人计划联系信息
     *
     * @param planId 计划Id
     * @param createReqVOList 创建信息
     */
    void createOrUpdatePlanContactList(Long planId, List<BrokerPlanContactReqVO> createReqVOList);

    /**
     * 删除经纪人计划联系信息
     *
     * @param planId 计划ID
     */
    void deletePlanContactByPlanId(Long planId);

    /**
     * 获得经纪人计划联系信息列表
     *
     * @param planId 计划Id
     * @return 经纪人计划联系信息列表
     */
    List<BrokerPlanContactDO> getPlanContactListByPlanId(Long planId);

}
