package com.yitong.octopus.module.broker.service.planrefund;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planrefund.BrokerPlanRefundConvert;
import com.yitong.octopus.module.broker.dal.mysql.planrefund.BrokerPlanRefundMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划取消 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanRefundServiceImpl implements BrokerPlanRefundService {

    @Resource
    private BrokerPlanRefundMapper planRefundMapper;

    @Override
    public Long createPlanRefund(BrokerPlanRefundCreateReqVO createReqVO) {
        // 插入
        BrokerPlanRefundDO planRefund = BrokerPlanRefundConvert.INSTANCE.convert(createReqVO);
        planRefundMapper.insert(planRefund);
        // 返回
        return planRefund.getId();
    }

    @Override
    public void updatePlanRefund(BrokerPlanRefundUpdateReqVO updateReqVO) {
        // 校验存在
        validatePlanRefundExists(updateReqVO.getId());
        // 更新
        BrokerPlanRefundDO updateObj = BrokerPlanRefundConvert.INSTANCE.convert(updateReqVO);
        planRefundMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanRefund(Long id) {
        // 校验存在
        validatePlanRefundExists(id);
        // 删除
        planRefundMapper.deleteById(id);
    }

    private void validatePlanRefundExists(Long id) {
        if (planRefundMapper.selectById(id) == null) {
            throw exception(PLAN_REFUND_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanRefundDO getPlanRefund(Long id) {
        return planRefundMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanRefundDO> getPlanRefundList(Collection<Long> ids) {
        return planRefundMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanRefundDO> getPlanRefundPage(BrokerPlanRefundPageReqVO pageReqVO) {
        return planRefundMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanRefundDO> getPlanRefundList(BrokerPlanRefundExportReqVO exportReqVO) {
        return planRefundMapper.selectList(exportReqVO);
    }

}
