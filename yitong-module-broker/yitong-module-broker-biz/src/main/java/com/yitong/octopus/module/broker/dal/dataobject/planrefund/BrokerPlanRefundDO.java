package com.yitong.octopus.module.broker.dal.dataobject.planrefund;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划取消 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_refund")
@KeySequence("broker_plan_refund_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanRefundDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 状态: 0 待审核，1 已通过，2已拒绝
     */
    private Byte status;
    /**
     * 计划渠道备注
     */
    private String refundMsg;
    /**
     * 备注
     */
    private String remark;

}
