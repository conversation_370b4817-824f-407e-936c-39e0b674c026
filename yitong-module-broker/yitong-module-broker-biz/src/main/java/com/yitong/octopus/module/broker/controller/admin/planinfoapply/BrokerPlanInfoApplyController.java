package com.yitong.octopus.module.broker.controller.admin.planinfoapply;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;
import com.yitong.octopus.module.broker.convert.planinfoapply.BrokerPlanInfoApplyConvert;
import com.yitong.octopus.module.broker.service.planinfoapply.BrokerPlanInfoApplyService;

@Tag(name = "管理后台 - 经纪人计划申请")
@RestController
@RequestMapping("/broker/plan-info-apply")
@Validated
public class BrokerPlanInfoApplyController {

    @Resource
    private BrokerPlanInfoApplyService planInfoApplyService;

    @PostMapping("/apply")
    @Operation(summary = "计划申请")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:apply')")
    public CommonResult<Long> applyPlanInfo(@Valid @RequestBody BrokerPlanInfoApplyReqVO createReqVO) {
        return success(planInfoApplyService.applyPlanInfo(createReqVO));
    }

    @PutMapping("/confirm")
    @Operation(summary = "计划确认")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:confirm')")
    public CommonResult<Boolean> confirmPlanInfoApply(@RequestParam("ids") Collection<Long> ids) {
        planInfoApplyService.confirmPlanInfoApply(ids);
        return success(true);
    }

    @PutMapping("/reject")
    @Operation(summary = "计划拒绝")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:confirm')")
    public CommonResult<Boolean> rejectPlanInfoApply(@RequestParam("ids") Collection<Long> ids) {
        planInfoApplyService.rejectPlanInfoApply(ids);
        return success(true);
    }

    @PutMapping("/finish")
    @Operation(summary = "计划完成回填记录")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:finish')")
    public CommonResult<Boolean> finishPlanInfoApply(@Valid @RequestBody BrokerPlanInfoApplyFinishReqVO reqVO) {
        planInfoApplyService.finishPlanInfoApply(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人计划申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:delete')")
    public CommonResult<Boolean> deletePlanInfoApply(@RequestParam("id") Long id) {
        planInfoApplyService.deletePlanInfoApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人计划申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:query')")
    public CommonResult<BrokerPlanInfoApplyRespVO> getPlanInfoApply(@RequestParam("id") Long id) {
        BrokerPlanInfoApplyDO planInfoApply = planInfoApplyService.getPlanInfoApply(id);
        return success(BrokerPlanInfoApplyConvert.INSTANCE.convert(planInfoApply));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人计划申请列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:query')")
    public CommonResult<List<BrokerPlanInfoApplyRespVO>> getPlanInfoApplyList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerPlanInfoApplyDO> list = planInfoApplyService.getPlanInfoApplyList(ids);
        return success(BrokerPlanInfoApplyConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人计划申请分页")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:query')")
    public CommonResult<PageResult<BrokerPlanInfoApplyRespVO>> getPlanInfoApplyPage(@Valid BrokerPlanInfoApplyPageReqVO pageVO) {
        PageResult<BrokerPlanInfoApplyDO> pageResult = planInfoApplyService.getPlanInfoApplyPage(pageVO);
        return success(BrokerPlanInfoApplyConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人计划申请 Excel")
    @PreAuthorize("@ss.hasPermission('broker:plan-info-apply:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanInfoApplyExcel(@Valid BrokerPlanInfoApplyExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerPlanInfoApplyDO> list = planInfoApplyService.getPlanInfoApplyList(exportReqVO);
        // 导出 Excel
        List<BrokerPlanInfoApplyExcelVO> datas = BrokerPlanInfoApplyConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人计划申请.xls", "数据", BrokerPlanInfoApplyExcelVO.class, datas);
    }
}
