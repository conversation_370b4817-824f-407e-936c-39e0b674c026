package com.yitong.octopus.module.broker.controller.admin.planrefund.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划取消更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanRefundUpdateReqVO extends BrokerPlanRefundBaseVO {

    @Schema(description = "编号", required = true, example = "936")
    @NotNull(message = "编号不能为空")
    private Long id;

}
