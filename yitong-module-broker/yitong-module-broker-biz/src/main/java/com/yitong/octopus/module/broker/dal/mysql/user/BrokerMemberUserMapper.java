package com.yitong.octopus.module.broker.dal.mysql.user;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserExportReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 会员 User Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerMemberUserMapper extends BaseMapperX<BrokerMemberUserDO> {

    default BrokerMemberUserDO selectByMobile(String mobile) {
        return selectOne(BrokerMemberUserDO::getPhone, mobile);
    }

    default List<BrokerMemberUserDO> selectListByNicknameLike(String nickname) {
        return selectList(new LambdaQueryWrapperX<BrokerMemberUserDO>()
                .likeIfPresent(BrokerMemberUserDO::getPhone, nickname));
    }


    default PageResult<BrokerMemberUserDO> selectPage(BrokerMemberUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerMemberUserDO>()
                .likeIfPresent(BrokerMemberUserDO::getName, reqVO.getName())
                .eqIfPresent(BrokerMemberUserDO::getPhone, reqVO.getPhone())
                .eqIfPresent(BrokerMemberUserDO::getSex, reqVO.getSex())
                .eqIfPresent(BrokerMemberUserDO::getAvatar, reqVO.getAvatar())
                .likeIfPresent(BrokerMemberUserDO::getNickName, reqVO.getNickName())
                .eqIfPresent(BrokerMemberUserDO::getCountry, reqVO.getCountry())
                .eqIfPresent(BrokerMemberUserDO::getProvince, reqVO.getProvince())
                .eqIfPresent(BrokerMemberUserDO::getCity, reqVO.getCity())
                .eqIfPresent(BrokerMemberUserDO::getArea, reqVO.getArea())
                .eqIfPresent(BrokerMemberUserDO::getAddress, reqVO.getAddress())
                .eqIfPresent(BrokerMemberUserDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerMemberUserDO::getPassword, reqVO.getPassword())
                .eqIfPresent(BrokerMemberUserDO::getRegisterIp, reqVO.getRegisterIp())
                .eqIfPresent(BrokerMemberUserDO::getLoginIp, reqVO.getLoginIp())
                .betweenIfPresent(BrokerMemberUserDO::getLoginDate, reqVO.getLoginDate())
                .eqIfPresent(BrokerMemberUserDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerMemberUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerMemberUserDO::getId));
    }

    default List<BrokerMemberUserDO> selectList(BrokerMemberUserExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerMemberUserDO>()
                .likeIfPresent(BrokerMemberUserDO::getName, reqVO.getName())
                .eqIfPresent(BrokerMemberUserDO::getPhone, reqVO.getPhone())
                .eqIfPresent(BrokerMemberUserDO::getSex, reqVO.getSex())
                .eqIfPresent(BrokerMemberUserDO::getAvatar, reqVO.getAvatar())
                .likeIfPresent(BrokerMemberUserDO::getNickName, reqVO.getNickName())
                .eqIfPresent(BrokerMemberUserDO::getCountry, reqVO.getCountry())
                .eqIfPresent(BrokerMemberUserDO::getProvince, reqVO.getProvince())
                .eqIfPresent(BrokerMemberUserDO::getCity, reqVO.getCity())
                .eqIfPresent(BrokerMemberUserDO::getArea, reqVO.getArea())
                .eqIfPresent(BrokerMemberUserDO::getAddress, reqVO.getAddress())
                .eqIfPresent(BrokerMemberUserDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerMemberUserDO::getPassword, reqVO.getPassword())
                .eqIfPresent(BrokerMemberUserDO::getRegisterIp, reqVO.getRegisterIp())
                .eqIfPresent(BrokerMemberUserDO::getLoginIp, reqVO.getLoginIp())
                .betweenIfPresent(BrokerMemberUserDO::getLoginDate, reqVO.getLoginDate())
                .eqIfPresent(BrokerMemberUserDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerMemberUserDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerMemberUserDO::getId));
    }

}
