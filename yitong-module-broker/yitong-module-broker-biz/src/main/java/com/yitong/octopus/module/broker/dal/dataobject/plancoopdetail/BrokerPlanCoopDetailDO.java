package com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail;

import com.yitong.octopus.framework.mybatis.core.dataobject.BaseCuDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划合作细则 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_coop_detail")
@KeySequence("broker_plan_coop_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanCoopDetailDO extends BaseCuDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 合作细节
     */
    private String cooperationInfo;

}
