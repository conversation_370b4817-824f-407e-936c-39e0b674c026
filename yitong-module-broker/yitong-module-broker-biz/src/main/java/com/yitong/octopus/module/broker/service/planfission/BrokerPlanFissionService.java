package com.yitong.octopus.module.broker.service.planfission;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划招募费用 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanFissionService {

    /**
     * 创建经纪人计划招募费用
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanFission(@Valid BrokerPlanFissionReqVO createReqVO);

    /**
     * 更新经纪人计划招募费用
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanFission(@Valid BrokerPlanFissionReqVO updateReqVO);

    /**
     * 删除经纪人计划招募费用
     *
     * @param id 编号
     */
    void deletePlanFission(Long id);

    /**
     * 获得经纪人计划招募费用
     *
     * @param id 编号
     * @return 经纪人计划招募费用
     */
    BrokerPlanFissionDO getPlanFission(Long id);

    /**
     * 获得经纪人计划招募费用列表
     *
     * @param ids 编号
     * @return 经纪人计划招募费用列表
     */
    List<BrokerPlanFissionDO> getPlanFissionList(Collection<Long> ids);

    /**
     * 获得经纪人计划招募费用分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划招募费用分页
     */
    PageResult<BrokerPlanFissionDO> getPlanFissionPage(BrokerPlanFissionPageReqVO pageReqVO);

    /**
     * 获得经纪人计划招募费用列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划招募费用列表
     */
    List<BrokerPlanFissionDO> getPlanFissionList(BrokerPlanFissionExportReqVO exportReqVO);

    /**
     * 创建经纪人计划招募费用
     * @param planId 计划Id
     * @param createReqVOList 创建信息
     */
    void createOrUpdatePlanFissionList(Long planId, List<BrokerPlanFissionReqVO> createReqVOList);

    /**
     * 删除经纪人计划招募费用
     * @param planId 计划Id
     */
    void deletePlanFissionByPlanId(Long planId);

    /**
     * 获得经纪人计划招募费用列表
     *
     * @param planId 计划Id
     * @return 经纪人计划招募费用列表
     */
    List<BrokerPlanFissionDO> getPlanFissionListByPlanId(Long planId);
}
