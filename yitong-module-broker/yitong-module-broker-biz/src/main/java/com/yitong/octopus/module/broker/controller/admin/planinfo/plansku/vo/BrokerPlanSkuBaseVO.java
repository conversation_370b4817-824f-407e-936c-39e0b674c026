package com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划sku信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanSkuBaseVO {

    @Schema(description = "计划ID", example = "25755")
    private Long planId;

    @Schema(description = "所属商家ID", example = "18634")
    private Long spId;

    @Schema(description = "商品SpuId", example = "25834")
    private Long spuId;

    @Schema(description = "商品SkuId", example = "13400")
    private Long skuId;

    @Schema(description = "cps分佣比例(小数点4位)")
    private Integer cps;

}
