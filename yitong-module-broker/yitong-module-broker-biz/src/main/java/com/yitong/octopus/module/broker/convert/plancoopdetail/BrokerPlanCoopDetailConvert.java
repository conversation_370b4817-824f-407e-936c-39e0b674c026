package com.yitong.octopus.module.broker.convert.plancoopdetail;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailUpdateReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;

/**
 * 经纪人计划合作细则 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanCoopDetailConvert {

    BrokerPlanCoopDetailConvert INSTANCE = Mappers.getMapper(BrokerPlanCoopDetailConvert.class);

    BrokerPlanCoopDetailDO convert(BrokerPlanCoopDetailCreateReqVO bean);

    BrokerPlanCoopDetailDO convert(BrokerPlanCoopDetailUpdateReqVO bean);

    BrokerPlanCoopDetailRespVO convert(BrokerPlanCoopDetailDO bean);

    List<BrokerPlanCoopDetailRespVO> convertList(List<BrokerPlanCoopDetailDO> list);

    PageResult<BrokerPlanCoopDetailRespVO> convertPage(PageResult<BrokerPlanCoopDetailDO> page);

    List<BrokerPlanCoopDetailExcelVO> convertList02(List<BrokerPlanCoopDetailDO> list);

}
