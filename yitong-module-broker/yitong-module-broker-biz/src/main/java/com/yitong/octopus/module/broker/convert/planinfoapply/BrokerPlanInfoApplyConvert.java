package com.yitong.octopus.module.broker.convert.planinfoapply;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;

/**
 * 经纪人计划申请 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanInfoApplyConvert {

    BrokerPlanInfoApplyConvert INSTANCE = Mappers.getMapper(BrokerPlanInfoApplyConvert.class);

    BrokerPlanInfoApplyDO convert(BrokerPlanInfoApplyReqVO bean);

    BrokerPlanInfoApplyDO convert(BrokerPlanInfoApplyUpdateReqVO bean);

    BrokerPlanInfoApplyDO convert(BrokerPlanInfoApplyFinishReqVO bean);

    BrokerPlanInfoApplyRespVO convert(BrokerPlanInfoApplyDO bean);

    List<BrokerPlanInfoApplyRespVO> convertList(List<BrokerPlanInfoApplyDO> list);

    PageResult<BrokerPlanInfoApplyRespVO> convertPage(PageResult<BrokerPlanInfoApplyDO> page);

    List<BrokerPlanInfoApplyExcelVO> convertList02(List<BrokerPlanInfoApplyDO> list);

}
