package com.yitong.octopus.module.broker.controller.admin.user.convert.memberuser;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserExcelVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserRespVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserUpdateReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 经纪人用户 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerMemberUserConvert {

    BrokerMemberUserConvert INSTANCE = Mappers.getMapper(BrokerMemberUserConvert.class);

    BrokerMemberUserDO convert(BrokerMemberUserCreateReqVO bean);

    BrokerMemberUserDO convert(BrokerMemberUserUpdateReqVO bean);

    BrokerMemberUserRespVO convert(BrokerMemberUserDO bean);

    List<BrokerMemberUserRespVO> convertList(List<BrokerMemberUserDO> list);

    PageResult<BrokerMemberUserRespVO> convertPage(PageResult<BrokerMemberUserDO> page);

    List<BrokerMemberUserExcelVO> convertList02(List<BrokerMemberUserDO> list);

}
