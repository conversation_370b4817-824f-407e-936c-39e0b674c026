package com.yitong.octopus.module.broker.dal.mysql.plancoopdetail;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划合作细则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanCoopDetailMapper extends BaseMapperX<BrokerPlanCoopDetailDO> {

    default PageResult<BrokerPlanCoopDetailDO> selectPage(BrokerPlanCoopDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanCoopDetailDO>()
                .eqIfPresent(BrokerPlanCoopDetailDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanCoopDetailDO::getCooperationInfo, reqVO.getCooperationInfo())
                .betweenIfPresent(BrokerPlanCoopDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanCoopDetailDO::getId));
    }

    default List<BrokerPlanCoopDetailDO> selectList(BrokerPlanCoopDetailExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanCoopDetailDO>()
                .eqIfPresent(BrokerPlanCoopDetailDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanCoopDetailDO::getCooperationInfo, reqVO.getCooperationInfo())
                .betweenIfPresent(BrokerPlanCoopDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanCoopDetailDO::getId));
    }

}
