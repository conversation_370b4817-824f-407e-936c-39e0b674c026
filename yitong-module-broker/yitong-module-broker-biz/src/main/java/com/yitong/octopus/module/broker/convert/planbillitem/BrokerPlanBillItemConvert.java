package com.yitong.octopus.module.broker.convert.planbillitem;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;

/**
 * 经纪人计划结算信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanBillItemConvert {

    BrokerPlanBillItemConvert INSTANCE = Mappers.getMapper(BrokerPlanBillItemConvert.class);

    BrokerPlanBillItemDO convert(BrokerPlanBillItemReqVO bean);

    BrokerPlanBillItemRespVO convert(BrokerPlanBillItemDO bean);

    List<BrokerPlanBillItemRespVO> convertList(List<BrokerPlanBillItemDO> list);

    PageResult<BrokerPlanBillItemRespVO> convertPage(PageResult<BrokerPlanBillItemDO> page);

    List<BrokerPlanBillItemExcelVO> convertList02(List<BrokerPlanBillItemDO> list);

}
