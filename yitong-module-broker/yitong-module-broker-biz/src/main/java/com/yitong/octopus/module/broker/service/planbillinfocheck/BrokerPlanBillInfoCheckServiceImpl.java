package com.yitong.octopus.module.broker.service.planbillinfocheck;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planbillinfocheck.BrokerPlanBillInfoCheckConvert;
import com.yitong.octopus.module.broker.dal.mysql.planbillinfocheck.BrokerPlanBillInfoCheckMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划账单确认记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanBillInfoCheckServiceImpl implements BrokerPlanBillInfoCheckService {

    @Resource
    private BrokerPlanBillInfoCheckMapper planBillInfoCheckMapper;

    @Override
    public Long createPlanBillInfoCheck(BrokerPlanBillInfoCheckCreateReqVO createReqVO) {
        // 插入
        BrokerPlanBillInfoCheckDO planBillInfoCheck = BrokerPlanBillInfoCheckConvert.INSTANCE.convert(createReqVO);
        planBillInfoCheckMapper.insert(planBillInfoCheck);
        // 返回
        return planBillInfoCheck.getId();
    }

    @Override
    public void updatePlanBillInfoCheck(BrokerPlanBillInfoCheckUpdateReqVO updateReqVO) {
        // 校验存在
        validatePlanBillInfoCheckExists(updateReqVO.getId());
        // 更新
        BrokerPlanBillInfoCheckDO updateObj = BrokerPlanBillInfoCheckConvert.INSTANCE.convert(updateReqVO);
        planBillInfoCheckMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanBillInfoCheck(Long id) {
        // 校验存在
        validatePlanBillInfoCheckExists(id);
        // 删除
        planBillInfoCheckMapper.deleteById(id);
    }

    private void validatePlanBillInfoCheckExists(Long id) {
        if (planBillInfoCheckMapper.selectById(id) == null) {
            throw exception(PLAN_BILL_INFO_CHECK_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanBillInfoCheckDO getPlanBillInfoCheck(Long id) {
        return planBillInfoCheckMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckList(Collection<Long> ids) {
        return planBillInfoCheckMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckPage(BrokerPlanBillInfoCheckPageReqVO pageReqVO) {
        return planBillInfoCheckMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanBillInfoCheckDO> getPlanBillInfoCheckList(BrokerPlanBillInfoCheckExportReqVO exportReqVO) {
        return planBillInfoCheckMapper.selectList(exportReqVO);
    }

}
