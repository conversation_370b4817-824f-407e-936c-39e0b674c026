package com.yitong.octopus.module.broker.service.planinfo;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanInfoService {

    /**
     * 创建经纪人计划信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanInfo(@Valid BrokerPlanInfoCreateReqVO createReqVO);

    /**
     * 更新经纪人计划信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanInfo(@Valid BrokerPlanInfoUpdateReqVO updateReqVO);

    /**
     * 删除经纪人计划信息
     *
     * @param id 编号
     */
    void deletePlanInfo(Long id);

    /**
     * 获得经纪人计划信息
     *
     * @param id 编号
     * @return 经纪人计划信息
     */
    BrokerPlanInfoDO getPlanInfo(Long id);

    /**
     * 获得经纪人计划信息
     *
     * @param id 编号
     * @return 经纪人计划信息
     */
    BrokerPlanInfoVO getPlanInfoVoById(Long id);

    /**
     * 获得经纪人计划信息列表
     *
     * @param ids 编号
     * @return 经纪人计划信息列表
     */
    List<BrokerPlanInfoDO> getPlanInfoList(Collection<Long> ids);

    /**
     * 获得经纪人计划信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划信息分页
     */
    PageResult<BrokerPlanInfoDO> getPlanInfoPage(BrokerPlanInfoPageReqVO pageReqVO);

    /**
     * 获得经纪人计划信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划信息列表
     */
    List<BrokerPlanInfoDO> getPlanInfoList(BrokerPlanInfoExportReqVO exportReqVO);

}
