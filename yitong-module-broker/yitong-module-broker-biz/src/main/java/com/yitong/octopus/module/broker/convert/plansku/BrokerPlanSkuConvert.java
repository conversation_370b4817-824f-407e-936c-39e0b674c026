package com.yitong.octopus.module.broker.convert.plansku;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;

/**
 * 经纪人计划sku信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanSkuConvert {

    BrokerPlanSkuConvert INSTANCE = Mappers.getMapper(BrokerPlanSkuConvert.class);

    BrokerPlanSkuDO convert(BrokerPlanSkuReqVO bean);

    BrokerPlanSkuRespVO convert(BrokerPlanSkuDO bean);

    List<BrokerPlanSkuRespVO> convertList(List<BrokerPlanSkuDO> list);

    PageResult<BrokerPlanSkuRespVO> convertPage(PageResult<BrokerPlanSkuDO> page);

    List<BrokerPlanSkuExcelVO> convertList02(List<BrokerPlanSkuDO> list);

}
