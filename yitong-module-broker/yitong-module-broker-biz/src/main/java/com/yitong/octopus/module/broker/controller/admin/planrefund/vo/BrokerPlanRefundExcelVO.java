package com.yitong.octopus.module.broker.controller.admin.planrefund.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划取消 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanRefundExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("状态: 0 待审核，1 已通过，2已拒绝")
    private Byte status;

    @ExcelProperty("计划渠道备注")
    private String refundMsg;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
