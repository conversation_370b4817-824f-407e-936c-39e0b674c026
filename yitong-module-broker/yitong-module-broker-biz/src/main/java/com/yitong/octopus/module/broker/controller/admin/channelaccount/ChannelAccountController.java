package com.yitong.octopus.module.broker.controller.admin.channelaccount;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.module.broker.convert.channelaccount.MemberUserChannelAccountConvert;
import com.yitong.octopus.module.broker.service.channelaccount.MemberUserChannelAccountService;

@Tag(name = "管理后台 - 经纪人渠道账户信息")
@RestController
@RequestMapping("/broker/member-user-channel-account")
@Validated
public class ChannelAccountController {

    @Resource
    private MemberUserChannelAccountService memberUserChannelAccountService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人渠道账户信息")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:create')")
    public CommonResult<Long> createMemberUserChannelAccount(@Valid @RequestBody MemberUserChannelAccountCreateReqVO createReqVO) {
        return success(memberUserChannelAccountService.createMemberUserChannelAccount(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人渠道账户信息")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:update')")
    public CommonResult<Boolean> updateMemberUserChannelAccount(@Valid @RequestBody MemberUserChannelAccountUpdateReqVO updateReqVO) {
        memberUserChannelAccountService.updateMemberUserChannelAccount(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人渠道账户信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:delete')")
    public CommonResult<Boolean> deleteMemberUserChannelAccount(@RequestParam("id") Long id) {
        memberUserChannelAccountService.deleteMemberUserChannelAccount(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人渠道账户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:query')")
    public CommonResult<MemberUserChannelAccountRespVO> getMemberUserChannelAccount(@RequestParam("id") Long id) {
        MemberUserChannelAccountDO memberUserChannelAccount = memberUserChannelAccountService.getMemberUserChannelAccount(id,null);
        return success(MemberUserChannelAccountConvert.INSTANCE.convert(memberUserChannelAccount));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人渠道账户信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:query')")
    public CommonResult<List<MemberUserChannelAccountRespVO>> getMemberUserChannelAccountList(@RequestParam("ids") Collection<Long> ids) {
        List<MemberUserChannelAccountDO> list = memberUserChannelAccountService.getMemberUserChannelAccountList(ids);
        return success(MemberUserChannelAccountConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人渠道账户信息分页")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:query')")
    public CommonResult<PageResult<MemberUserChannelAccountRespVO>> getMemberUserChannelAccountPage(@Valid MemberUserChannelAccountPageReqVO pageVO) {
        PageResult<MemberUserChannelAccountDO> pageResult = memberUserChannelAccountService.getMemberUserChannelAccountPage(pageVO);
        return success(MemberUserChannelAccountConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人渠道账户信息 Excel")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:export')")
    @OperateLog(type = EXPORT)
    public void exportMemberUserChannelAccountExcel(@Valid MemberUserChannelAccountExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<MemberUserChannelAccountDO> list = memberUserChannelAccountService.getMemberUserChannelAccountList(exportReqVO);
        // 导出 Excel
        List<MemberUserChannelAccountExcelVO> datas = MemberUserChannelAccountConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人渠道账户信息.xls", "数据", MemberUserChannelAccountExcelVO.class, datas);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改用户状态")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody MemberUserChannelAccountUpdateStatusReqVO reqVO) {
        memberUserChannelAccountService.updateUserStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核状态")
    @PreAuthorize("@ss.hasPermission('broker:member-user-channel-account:audit')")
    public CommonResult<Boolean> updateUserAuditStatus(@Valid @RequestBody MemberUserChannelAccountAuditStatusReqVO reqVO) {
        memberUserChannelAccountService.updateUserAuditStatus(reqVO.getId(), reqVO.getAuditStatus(),reqVO.getRemark());
        return success(true);
    }

}
