package com.yitong.octopus.module.broker.controller.admin.planinfo;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;
import com.yitong.octopus.module.broker.convert.planinfo.BrokerPlanInfoConvert;
import com.yitong.octopus.module.broker.service.planinfo.BrokerPlanInfoService;

@Tag(name = "管理后台 - 经纪人计划信息")
@RestController
@RequestMapping("/broker/plan-info")
@Validated
public class BrokerPlanInfoController {

    @Resource
    private BrokerPlanInfoService planInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人计划信息")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:create')")
    public CommonResult<Long> createPlanInfo(@Valid @RequestBody BrokerPlanInfoCreateReqVO createReqVO) {
        return success(planInfoService.createPlanInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人计划信息")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:update')")
    public CommonResult<Boolean> updatePlanInfo(@Valid @RequestBody BrokerPlanInfoUpdateReqVO updateReqVO) {
        planInfoService.updatePlanInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人计划信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:plan-info:delete')")
    public CommonResult<Boolean> deletePlanInfo(@RequestParam("id") Long id) {
        planInfoService.deletePlanInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人计划信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:query')")
    public CommonResult<BrokerPlanInfoVO> getPlanInfo(@RequestParam("id") Long id) {
        BrokerPlanInfoVO planInfo = planInfoService.getPlanInfoVoById(id);
        return success(planInfo);
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人计划信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:query')")
    public CommonResult<List<BrokerPlanInfoRespVO>> getPlanInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerPlanInfoDO> list = planInfoService.getPlanInfoList(ids);
        return success(BrokerPlanInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人计划信息分页")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:query')")
    public CommonResult<PageResult<BrokerPlanInfoRespVO>> getPlanInfoPage(@Valid BrokerPlanInfoPageReqVO pageVO) {
        PageResult<BrokerPlanInfoDO> pageResult = planInfoService.getPlanInfoPage(pageVO);
        return success(BrokerPlanInfoConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人计划信息 Excel")
    @PreAuthorize("@ss.hasPermission('broker:plan-info:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanInfoExcel(@Valid BrokerPlanInfoExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerPlanInfoDO> list = planInfoService.getPlanInfoList(exportReqVO);
        // 导出 Excel
        List<BrokerPlanInfoExcelVO> datas = BrokerPlanInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人计划信息.xls", "数据", BrokerPlanInfoExcelVO.class, datas);
    }

}
