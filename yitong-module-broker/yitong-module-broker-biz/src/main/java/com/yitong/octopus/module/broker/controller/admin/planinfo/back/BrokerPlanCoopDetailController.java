//package com.yitong.octopus.module.broker.controller.admin.planinfo.back;
//
//import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.controller.admin.plancoopdetail.vo.*;
//import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;
//import com.yitong.octopus.module.broker.convert.plancoopdetail.BrokerPlanCoopDetailConvert;
//import com.yitong.octopus.module.broker.service.plancoopdetail.BrokerPlanCoopDetailService;
//
//@Tag(name = "管理后台 - 经纪人计划合作细则")
//@RestController
//@RequestMapping("/broker/plan-coop-detail")
//@Validated
//public class BrokerPlanCoopDetailController {
//
//    @Resource
//    private BrokerPlanCoopDetailService planCoopDetailService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建经纪人计划合作细则")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:create')")
//    public CommonResult<Long> createPlanCoopDetail(@Valid @RequestBody BrokerPlanCoopDetailCreateReqVO createReqVO) {
//        return success(planCoopDetailService.createPlanCoopDetail(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新经纪人计划合作细则")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:update')")
//    public CommonResult<Boolean> updatePlanCoopDetail(@Valid @RequestBody BrokerPlanCoopDetailUpdateReqVO updateReqVO) {
//        planCoopDetailService.updatePlanCoopDetail(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除经纪人计划合作细则")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:delete')")
//    public CommonResult<Boolean> deletePlanCoopDetail(@RequestParam("id") Long id) {
//        planCoopDetailService.deletePlanCoopDetail(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得经纪人计划合作细则")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:query')")
//    public CommonResult<BrokerPlanCoopDetailRespVO> getPlanCoopDetail(@RequestParam("id") Long id) {
//        BrokerPlanCoopDetailDO planCoopDetail = planCoopDetailService.getPlanCoopDetail(id);
//        return success(BrokerPlanCoopDetailConvert.INSTANCE.convert(planCoopDetail));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得经纪人计划合作细则列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:query')")
//    public CommonResult<List<BrokerPlanCoopDetailRespVO>> getPlanCoopDetailList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanCoopDetailDO> list = planCoopDetailService.getPlanCoopDetailList(ids);
//        return success(BrokerPlanCoopDetailConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得经纪人计划合作细则分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:query')")
//    public CommonResult<PageResult<BrokerPlanCoopDetailRespVO>> getPlanCoopDetailPage(@Valid BrokerPlanCoopDetailPageReqVO pageVO) {
//        PageResult<BrokerPlanCoopDetailDO> pageResult = planCoopDetailService.getPlanCoopDetailPage(pageVO);
//        return success(BrokerPlanCoopDetailConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出经纪人计划合作细则 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-coop-detail:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanCoopDetailExcel(@Valid BrokerPlanCoopDetailExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanCoopDetailDO> list = planCoopDetailService.getPlanCoopDetailList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanCoopDetailExcelVO> datas = BrokerPlanCoopDetailConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "经纪人计划合作细则.xls", "数据", BrokerPlanCoopDetailExcelVO.class, datas);
//    }
//
//}
