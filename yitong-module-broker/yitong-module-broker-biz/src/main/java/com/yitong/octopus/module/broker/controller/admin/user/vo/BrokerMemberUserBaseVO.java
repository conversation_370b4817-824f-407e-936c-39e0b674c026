package com.yitong.octopus.module.broker.controller.admin.user.vo;

import com.yitong.octopus.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 经纪人用户 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class BrokerMemberUserBaseVO {

    @Schema(description = "用户姓名", required = true, example = "张三")
    @NotNull(message = "用户姓名不能为空")
    private String name;

    @Schema(description = "手机号", required = true)
    @Mobile
    @NotNull(message = "手机号不能为空")
    private String phone;

    @Schema(description = "性别")
    private Integer sex;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "用户昵称", example = "一筒")
    private String nickName;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省/直辖市")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "地区")
    private String area;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "注册 IP", required = true)
    private String registerIp;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime loginDate;

    @Schema(description = "备注", example = "备注")
    private String remark;

}
