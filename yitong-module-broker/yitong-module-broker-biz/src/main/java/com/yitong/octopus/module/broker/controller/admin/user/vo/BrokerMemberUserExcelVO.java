package com.yitong.octopus.module.broker.controller.admin.user.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人用户 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerMemberUserExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("用户姓名")
    private String name;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("性别1男 0女")
    private Integer sex;

    @ExcelProperty("头像")
    private String avatar;

    @ExcelProperty("用户昵称")
    private String nickName;

    @ExcelProperty("国家")
    private String country;

    @ExcelProperty("省/直辖市")
    private String province;

    @ExcelProperty("城市")
    private String city;

    @ExcelProperty("地区")
    private String area;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("状态: 0 无效，1有效")
    private Byte status;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("注册 IP")
    private String registerIp;

    @ExcelProperty("最后登录IP")
    private String loginIp;

    @ExcelProperty("最后登录时间")
    private LocalDateTime loginDate;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
