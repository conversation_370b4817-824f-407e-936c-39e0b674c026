package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 经纪人计划账单确认记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanBillInfoCheckBaseVO {

    @Schema(description = "计划ID", example = "32702")
    private Long planId;

    @Schema(description = "计划结算项Id", example = "26947")
    private Long planBillId;

    @Schema(description = "经纪人Id", example = "18102")
    private Long talentId;

    @Schema(description = "最晚确认时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastCheckTime;

    @Schema(description = "实际确认时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime realCheckTime;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "驳回备注")
    private String refundMsg;

    @Schema(description = "作品连接", example = "https://www.iocoder.cn")
    private String worksUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
