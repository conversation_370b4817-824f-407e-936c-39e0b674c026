package com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划结算信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanBillItemRespVO extends BrokerPlanBillItemBaseVO {

    @Schema(description = "编号", required = true, example = "2555")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
