package com.yitong.octopus.module.broker.convert.social;

import com.yitong.octopus.module.broker.controller.app.social.vo.AppSocialUserBindReqVO;
import com.yitong.octopus.module.broker.controller.app.social.vo.AppSocialUserUnbindReqVO;
import com.yitong.octopus.module.system.api.social.dto.SocialUserBindReqDTO;
import com.yitong.octopus.module.system.api.social.dto.SocialUserUnbindReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SocialUserConvert {

    SocialUserConvert INSTANCE = Mappers.getMapper(SocialUserConvert.class);

    SocialUserBindReqDTO convert(Long userId, Integer userType, AppSocialUserBindReqVO reqVO);

    SocialUserUnbindReqDTO convert(Long userId, Integer userType, AppSocialUserUnbindReqVO reqVO);

}
