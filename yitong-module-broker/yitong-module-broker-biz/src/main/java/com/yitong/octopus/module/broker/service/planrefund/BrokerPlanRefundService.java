package com.yitong.octopus.module.broker.service.planrefund;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划取消 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanRefundService {

    /**
     * 创建经纪人计划取消
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanRefund(@Valid BrokerPlanRefundCreateReqVO createReqVO);

    /**
     * 更新经纪人计划取消
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanRefund(@Valid BrokerPlanRefundUpdateReqVO updateReqVO);

    /**
     * 删除经纪人计划取消
     *
     * @param id 编号
     */
    void deletePlanRefund(Long id);

    /**
     * 获得经纪人计划取消
     *
     * @param id 编号
     * @return 经纪人计划取消
     */
    BrokerPlanRefundDO getPlanRefund(Long id);

    /**
     * 获得经纪人计划取消列表
     *
     * @param ids 编号
     * @return 经纪人计划取消列表
     */
    List<BrokerPlanRefundDO> getPlanRefundList(Collection<Long> ids);

    /**
     * 获得经纪人计划取消分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划取消分页
     */
    PageResult<BrokerPlanRefundDO> getPlanRefundPage(BrokerPlanRefundPageReqVO pageReqVO);

    /**
     * 获得经纪人计划取消列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划取消列表
     */
    List<BrokerPlanRefundDO> getPlanRefundList(BrokerPlanRefundExportReqVO exportReqVO);

}
