package com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 计划门店信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanStoreExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("所属商家ID")
    private Long spId;

    @ExcelProperty("门店Id")
    private Long storeId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
