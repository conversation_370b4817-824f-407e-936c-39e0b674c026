package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;
import com.yitong.octopus.module.broker.convert.planbillinfocheck.BrokerPlanBillInfoCheckConvert;
import com.yitong.octopus.module.broker.service.planbillinfocheck.BrokerPlanBillInfoCheckService;

@Tag(name = "管理后台 - 经纪人计划账单确认记录")
@RestController
@RequestMapping("/broker/plan-bill-info-check")
@Validated
public class BrokerPlanBillInfoCheckController {

    @Resource
    private BrokerPlanBillInfoCheckService planBillInfoCheckService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人计划账单确认记录")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:create')")
    public CommonResult<Long> createPlanBillInfoCheck(@Valid @RequestBody BrokerPlanBillInfoCheckCreateReqVO createReqVO) {
        return success(planBillInfoCheckService.createPlanBillInfoCheck(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人计划账单确认记录")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:update')")
    public CommonResult<Boolean> updatePlanBillInfoCheck(@Valid @RequestBody BrokerPlanBillInfoCheckUpdateReqVO updateReqVO) {
        planBillInfoCheckService.updatePlanBillInfoCheck(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人计划账单确认记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:delete')")
    public CommonResult<Boolean> deletePlanBillInfoCheck(@RequestParam("id") Long id) {
        planBillInfoCheckService.deletePlanBillInfoCheck(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人计划账单确认记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:query')")
    public CommonResult<BrokerPlanBillInfoCheckRespVO> getPlanBillInfoCheck(@RequestParam("id") Long id) {
        BrokerPlanBillInfoCheckDO planBillInfoCheck = planBillInfoCheckService.getPlanBillInfoCheck(id);
        return success(BrokerPlanBillInfoCheckConvert.INSTANCE.convert(planBillInfoCheck));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人计划账单确认记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:query')")
    public CommonResult<List<BrokerPlanBillInfoCheckRespVO>> getPlanBillInfoCheckList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerPlanBillInfoCheckDO> list = planBillInfoCheckService.getPlanBillInfoCheckList(ids);
        return success(BrokerPlanBillInfoCheckConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人计划账单确认记录分页")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:query')")
    public CommonResult<PageResult<BrokerPlanBillInfoCheckRespVO>> getPlanBillInfoCheckPage(@Valid BrokerPlanBillInfoCheckPageReqVO pageVO) {
        PageResult<BrokerPlanBillInfoCheckDO> pageResult = planBillInfoCheckService.getPlanBillInfoCheckPage(pageVO);
        return success(BrokerPlanBillInfoCheckConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人计划账单确认记录 Excel")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-info-check:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanBillInfoCheckExcel(@Valid BrokerPlanBillInfoCheckExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerPlanBillInfoCheckDO> list = planBillInfoCheckService.getPlanBillInfoCheckList(exportReqVO);
        // 导出 Excel
        List<BrokerPlanBillInfoCheckExcelVO> datas = BrokerPlanBillInfoCheckConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人计划账单确认记录.xls", "数据", BrokerPlanBillInfoCheckExcelVO.class, datas);
    }

}
