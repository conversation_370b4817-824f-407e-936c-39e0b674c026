package com.yitong.octopus.module.broker.dal.mysql.plancontact;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划联系信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanContactMapper extends BaseMapperX<BrokerPlanContactDO> {

    default PageResult<BrokerPlanContactDO> selectPage(BrokerPlanContactPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanContactDO>()
                .eqIfPresent(BrokerPlanContactDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanContactDO::getType, reqVO.getType())
                .eqIfPresent(BrokerPlanContactDO::getWidgetType, reqVO.getWidgetType())
                .eqIfPresent(BrokerPlanContactDO::getContact, reqVO.getContact())
                .betweenIfPresent(BrokerPlanContactDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanContactDO::getId));
    }

    default List<BrokerPlanContactDO> selectList(BrokerPlanContactExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanContactDO>()
                .eqIfPresent(BrokerPlanContactDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanContactDO::getType, reqVO.getType())
                .eqIfPresent(BrokerPlanContactDO::getWidgetType, reqVO.getWidgetType())
                .eqIfPresent(BrokerPlanContactDO::getContact, reqVO.getContact())
                .betweenIfPresent(BrokerPlanContactDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanContactDO::getId));
    }

}
