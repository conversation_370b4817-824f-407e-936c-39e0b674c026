package com.yitong.octopus.module.broker.controller.app.channelaccount;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.MemberUserChannelAccountCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.MemberUserChannelAccountPageReqVO;
import com.yitong.octopus.module.broker.convert.channelaccount.AppMemberUserChannelAccountConvert;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.module.broker.service.channelaccount.MemberUserChannelAccountService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
import com.yitong.octopus.module.broker.controller.app.channelaccount.vo.*;

@Tag(name = "用户 APP - 经纪人渠道账户信息")
@RestController
@RequestMapping("/broker/member-user-channel-account")
@Validated
public class AppChannelAccountController {

    @Resource
    private MemberUserChannelAccountService memberUserChannelAccountService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人渠道账户信息")

    public CommonResult<Long> createMemberUserChannelAccount(@Valid @RequestBody AppMemberUserChannelAccountCreateReqVO createReqVO) {
        MemberUserChannelAccountCreateReqVO reqVo = BeanUtil.toBean(createReqVO,MemberUserChannelAccountCreateReqVO.class);
        reqVo.setUserId(SecurityFrameworkUtils.getLoginUserId());
        return success(memberUserChannelAccountService.createMemberUserChannelAccount(reqVo));
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人渠道账户信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppMemberUserChannelAccountRespVO> getMemberUserChannelAccount(@RequestParam("id") Long id) {
        MemberUserChannelAccountDO memberUserChannelAccount = memberUserChannelAccountService.getMemberUserChannelAccount(id,SecurityFrameworkUtils.getLoginUserId());
        return success(AppMemberUserChannelAccountConvert.INSTANCE.convert(memberUserChannelAccount));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人渠道账户信息分页")
    public CommonResult<PageResult<AppMemberUserChannelAccountRespVO>> getMemberUserChannelAccountPage(@Valid MemberUserChannelAccountPageReqVO pageVO) {
        pageVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        PageResult<MemberUserChannelAccountDO> pageResult = memberUserChannelAccountService.getMemberUserChannelAccountPage(pageVO);
        return success(AppMemberUserChannelAccountConvert.INSTANCE.convertPage(pageResult));
    }

}
