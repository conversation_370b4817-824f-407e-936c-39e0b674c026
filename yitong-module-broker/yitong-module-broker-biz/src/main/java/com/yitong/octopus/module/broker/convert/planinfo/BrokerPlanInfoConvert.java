package com.yitong.octopus.module.broker.convert.planinfo;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;

/**
 * 经纪人计划信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanInfoConvert {

    BrokerPlanInfoConvert INSTANCE = Mappers.getMapper(BrokerPlanInfoConvert.class);

    BrokerPlanInfoDO convert(BrokerPlanInfoCreateReqVO bean);

    BrokerPlanInfoDO convert(BrokerPlanInfoUpdateReqVO bean);

    BrokerPlanInfoRespVO convert(BrokerPlanInfoDO bean);

    List<BrokerPlanInfoRespVO> convertList(List<BrokerPlanInfoDO> list);

    PageResult<BrokerPlanInfoRespVO> convertPage(PageResult<BrokerPlanInfoDO> page);

    List<BrokerPlanInfoExcelVO> convertList02(List<BrokerPlanInfoDO> list);

    BrokerPlanInfoVO convertVo(BrokerPlanInfoDO bean);

}
