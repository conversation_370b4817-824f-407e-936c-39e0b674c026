package com.yitong.octopus.module.broker.controller.admin.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人用户 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerMemberUserRespVO extends BrokerMemberUserBaseVO {

    @Schema(description = "编号", required = true, example = "16161")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
