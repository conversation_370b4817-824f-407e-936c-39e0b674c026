package com.yitong.octopus.module.broker.dal.mysql.memberuserchannelaccount;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;

/**
 * 经纪人渠道账户信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MemberUserChannelAccountMapper extends BaseMapperX<MemberUserChannelAccountDO> {

    default PageResult<MemberUserChannelAccountDO> selectPage(MemberUserChannelAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MemberUserChannelAccountDO>()
                .eqIfPresent(MemberUserChannelAccountDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(MemberUserChannelAccountDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(MemberUserChannelAccountDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformId, reqVO.getPlatformId())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformNo, reqVO.getPlatformNo())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformLevel, reqVO.getPlatformLevel())
                .eqIfPresent(MemberUserChannelAccountDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(MemberUserChannelAccountDO::getStatus, reqVO.getStatus())
                .orderByDesc(MemberUserChannelAccountDO::getId));
    }

    default List<MemberUserChannelAccountDO> selectList(MemberUserChannelAccountExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<MemberUserChannelAccountDO>()
                .eqIfPresent(MemberUserChannelAccountDO::getChannelCode, reqVO.getChannelCode())
                .eqIfPresent(MemberUserChannelAccountDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(MemberUserChannelAccountDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformId, reqVO.getPlatformId())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformNo, reqVO.getPlatformNo())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformName, reqVO.getPlatformName())
                .eqIfPresent(MemberUserChannelAccountDO::getPlatformLevel, reqVO.getPlatformLevel())
                .eqIfPresent(MemberUserChannelAccountDO::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(MemberUserChannelAccountDO::getStatus, reqVO.getStatus())
                .orderByDesc(MemberUserChannelAccountDO::getId));
    }

}
