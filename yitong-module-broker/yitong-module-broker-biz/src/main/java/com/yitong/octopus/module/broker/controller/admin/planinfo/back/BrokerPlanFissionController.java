//package com.yitong.octopus.module.broker.controller.admin.planinfo.back;
//
//import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.controller.admin.planfission.vo.*;
//import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
//import com.yitong.octopus.module.broker.convert.planfission.BrokerPlanFissionConvert;
//import com.yitong.octopus.module.broker.service.planfission.BrokerPlanFissionService;
//
//@Tag(name = "管理后台 - 经纪人计划招募费用")
//@RestController
//@RequestMapping("/broker/plan-fission")
//@Validated
//public class BrokerPlanFissionController {
//
//    @Resource
//    private BrokerPlanFissionService planFissionService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建经纪人计划招募费用")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:create')")
//    public CommonResult<Long> createPlanFission(@Valid @RequestBody BrokerPlanFissionCreateReqVO createReqVO) {
//        return success(planFissionService.createPlanFission(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新经纪人计划招募费用")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:update')")
//    public CommonResult<Boolean> updatePlanFission(@Valid @RequestBody BrokerPlanFissionUpdateReqVO updateReqVO) {
//        planFissionService.updatePlanFission(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除经纪人计划招募费用")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:delete')")
//    public CommonResult<Boolean> deletePlanFission(@RequestParam("id") Long id) {
//        planFissionService.deletePlanFission(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得经纪人计划招募费用")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:query')")
//    public CommonResult<BrokerPlanFissionRespVO> getPlanFission(@RequestParam("id") Long id) {
//        BrokerPlanFissionDO planFission = planFissionService.getPlanFission(id);
//        return success(BrokerPlanFissionConvert.INSTANCE.convert(planFission));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得经纪人计划招募费用列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:query')")
//    public CommonResult<List<BrokerPlanFissionRespVO>> getPlanFissionList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanFissionDO> list = planFissionService.getPlanFissionList(ids);
//        return success(BrokerPlanFissionConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得经纪人计划招募费用分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:query')")
//    public CommonResult<PageResult<BrokerPlanFissionRespVO>> getPlanFissionPage(@Valid BrokerPlanFissionPageReqVO pageVO) {
//        PageResult<BrokerPlanFissionDO> pageResult = planFissionService.getPlanFissionPage(pageVO);
//        return success(BrokerPlanFissionConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出经纪人计划招募费用 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-fission:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanFissionExcel(@Valid BrokerPlanFissionExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanFissionDO> list = planFissionService.getPlanFissionList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanFissionExcelVO> datas = BrokerPlanFissionConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "经纪人计划招募费用.xls", "数据", BrokerPlanFissionExcelVO.class, datas);
//    }
//
//}
