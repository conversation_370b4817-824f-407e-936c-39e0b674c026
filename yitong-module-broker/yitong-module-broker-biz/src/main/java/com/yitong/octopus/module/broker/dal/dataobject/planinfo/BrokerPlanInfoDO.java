package com.yitong.octopus.module.broker.dal.dataobject.planinfo;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划信息 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_info")
@KeySequence("broker_plan_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanInfoDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划名称
     */
    private String name;
    /**
     * 计划类型:1.短视频，2直播
     */
    private Integer type;
    /**
     * 招募类型：1.招募，2.定向
     */
    private Integer planType;
    /**
     * 报名开始时间
     */
    private LocalDateTime applyStartTime;
    /**
     * 报名结束时间
     */
    private LocalDateTime applyEndTime;
    /**
     * 探店开始时间
     */
    private LocalDateTime storeStartTime;
    /**
     * 探店结束时间
     */
    private LocalDateTime storeEndTime;
    /**
     * 作品发布开始时间
     */
    private LocalDateTime publishStartTime;
    /**
     * 作品发布结束时间
     */
    private LocalDateTime publishEndTime;
    /**
     * 所属商家ID
     */
    private Long spId;
    /**
     * 商家确认最后截止时间
     */
    private LocalDateTime spCheckEndTime;
    /**
     * 内容类型
     */
    private Integer contentType;
    /**
     * 结算类型
     */
    private Integer billType;
    /**
     * cps佣金有效天数
     */
    private Integer cpsBillDays;
    /**
     * 招募的经纪人总人数
     */
    private Integer talentTotal;
    /**
     * 经纪人同行人数
     */
    private Integer talentFellowNo;
    /**
     * 是否需要商家确认
     */
    private Boolean merchantCheck;
    /**
     * 是否需要媒介确认
     */
    private Boolean brokerCheck;
    /**
     * 是否是长期招募
     */
    private Boolean planIsLong;
    /**
     * 支持周几探店:0 期望每周可探店时间，1 不限制每周可探店时间
     */
    private Boolean talentIsStoreDays;
    /**
     * mon(星期一)，tues(星期二)，wed(星期三)，thur(星期四)，fri(星期五)，sat(星期六)，sun(星期日)
     */
    private String talentStoreDays;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;

}
