//package com.yitong.octopus.module.broker.controller.admin.planchannel;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.controller.admin.planchannel.vo.*;
//import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;
//import com.yitong.octopus.module.broker.convert.planchannel.BrokerPlanChannelConvert;
//import com.yitong.octopus.module.broker.service.planchannel.BrokerPlanChannelService;
//
//@Tag(name = "管理后台 - 经纪人计划渠道信息")
//@RestController
//@RequestMapping("/broker/plan-channel")
//@Validated
//public class BrokerPlanChannelController {
//
//    @Resource
//    private BrokerPlanChannelService planChannelService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建经纪人计划渠道信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:create')")
//    public CommonResult<Long> createPlanChannel(@Valid @RequestBody BrokerPlanChannelCreateReqVO createReqVO) {
//        return success(planChannelService.createPlanChannel(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新经纪人计划渠道信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:update')")
//    public CommonResult<Boolean> updatePlanChannel(@Valid @RequestBody BrokerPlanChannelUpdateReqVO updateReqVO) {
//        planChannelService.updatePlanChannel(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除经纪人计划渠道信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:delete')")
//    public CommonResult<Boolean> deletePlanChannel(@RequestParam("id") Long id) {
//        planChannelService.deletePlanChannel(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得经纪人计划渠道信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:query')")
//    public CommonResult<BrokerPlanChannelRespVO> getPlanChannel(@RequestParam("id") Long id) {
//        BrokerPlanChannelDO planChannel = planChannelService.getPlanChannel(id);
//        return success(BrokerPlanChannelConvert.INSTANCE.convert(planChannel));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得经纪人计划渠道信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:query')")
//    public CommonResult<List<BrokerPlanChannelRespVO>> getPlanChannelList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanChannelDO> list = planChannelService.getPlanChannelList(ids);
//        return success(BrokerPlanChannelConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得经纪人计划渠道信息分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:query')")
//    public CommonResult<PageResult<BrokerPlanChannelRespVO>> getPlanChannelPage(@Valid BrokerPlanChannelPageReqVO pageVO) {
//        PageResult<BrokerPlanChannelDO> pageResult = planChannelService.getPlanChannelPage(pageVO);
//        return success(BrokerPlanChannelConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出经纪人计划渠道信息 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-channel:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanChannelExcel(@Valid BrokerPlanChannelExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanChannelDO> list = planChannelService.getPlanChannelList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanChannelExcelVO> datas = BrokerPlanChannelConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "经纪人计划渠道信息.xls", "数据", BrokerPlanChannelExcelVO.class, datas);
//    }
//
//}
