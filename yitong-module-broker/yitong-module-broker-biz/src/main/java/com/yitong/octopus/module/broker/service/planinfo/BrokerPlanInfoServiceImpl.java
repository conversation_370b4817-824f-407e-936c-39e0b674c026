package com.yitong.octopus.module.broker.service.planinfo;

import cn.hutool.core.collection.CollectionUtil;
import com.yitong.octopus.module.broker.convert.planbillitem.BrokerPlanBillItemConvert;
import com.yitong.octopus.module.broker.convert.plancontact.BrokerPlanContactConvert;
import com.yitong.octopus.module.broker.convert.planfission.BrokerPlanFissionConvert;
import com.yitong.octopus.module.broker.convert.plansku.BrokerPlanSkuConvert;
import com.yitong.octopus.module.broker.convert.planstore.BrokerPlanStoreConvert;
import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import com.yitong.octopus.module.broker.service.planbillitem.BrokerPlanBillItemService;
import com.yitong.octopus.module.broker.service.planchannel.BrokerPlanChannelService;
import com.yitong.octopus.module.broker.service.plancontact.BrokerPlanContactService;
import com.yitong.octopus.module.broker.service.planfission.BrokerPlanFissionService;
import com.yitong.octopus.module.broker.service.plansku.BrokerPlanSkuService;
import com.yitong.octopus.module.broker.service.planstore.BrokerPlanStoreService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planinfo.BrokerPlanInfoConvert;
import com.yitong.octopus.module.broker.dal.mysql.planinfo.BrokerPlanInfoMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanInfoServiceImpl implements BrokerPlanInfoService {

    @Resource
    private BrokerPlanInfoMapper planInfoMapper;
    @Resource
    private BrokerPlanFissionService brokerPlanFissionService;//经纪人计划招募费用
    @Resource
    private BrokerPlanContactService brokerPlanContactService; //经纪人计划联系信息
    @Resource
    private BrokerPlanSkuService brokerPlanSkuService; //经纪人计划商品信息
    @Resource
    private BrokerPlanStoreService brokerPlanStoreService; //经纪人计划商品信息
    @Resource
    private BrokerPlanBillItemService brokerPlanBillItemService; //经纪人计划结算信息
    @Resource
    private BrokerPlanChannelService brokerPlanChannelService; //经纪人计划结算信息

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createPlanInfo(BrokerPlanInfoCreateReqVO reqVO) {
        // 插入
        BrokerPlanInfoDO planInfo = BrokerPlanInfoConvert.INSTANCE.convert(reqVO);
        planInfoMapper.insert(planInfo);
        // 渠道信息
        if (CollectionUtil.isNotEmpty(reqVO.getChannelList())){
            brokerPlanChannelService.createOrUpdatePlanChannelList(planInfo.getId(),reqVO.getChannelList());
        }
        // 门店信息
        if (CollectionUtil.isNotEmpty(reqVO.getStoreList())){
            brokerPlanStoreService.createOrUpdatePlanStoreList(planInfo.getId(),reqVO.getStoreList());
        }
        // 商品信息
        if (CollectionUtil.isNotEmpty(reqVO.getSkuList())){
            brokerPlanSkuService.createOrUpdatePlanSkuList(planInfo.getId(),reqVO.getSkuList());
        }
        // 结算信息
        if (CollectionUtil.isNotEmpty(reqVO.getBillItem())){
            brokerPlanBillItemService.createOrUpdatePlanBillItemList(planInfo.getId(),reqVO.getBillItem());
        }
        // 联系信息
        if (CollectionUtil.isNotEmpty(reqVO.getContactList())){
            brokerPlanContactService.createOrUpdatePlanContactList(planInfo.getId(),reqVO.getContactList());
        }
        // 达人招募
        if (CollectionUtil.isNotEmpty(reqVO.getFissionList())){
            brokerPlanFissionService.createOrUpdatePlanFissionList(planInfo.getId(),reqVO.getFissionList());
        }
        // 返回
        return planInfo.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePlanInfo(BrokerPlanInfoUpdateReqVO reqVO) {
        // 校验存在
        validatePlanInfoExists(reqVO.getId());
        // 更新
        BrokerPlanInfoDO updateObj = BrokerPlanInfoConvert.INSTANCE.convert(reqVO);
        planInfoMapper.updateById(updateObj);
        // 渠道信息
        if (CollectionUtil.isNotEmpty(reqVO.getChannelList())){
            brokerPlanChannelService.createOrUpdatePlanChannelList(reqVO.getId(),reqVO.getChannelList());
        }
        // 门店信息
        if (CollectionUtil.isNotEmpty(reqVO.getStoreList())){
            brokerPlanStoreService.createOrUpdatePlanStoreList(reqVO.getId(),reqVO.getStoreList());
        }
        // 商品信息
        if (CollectionUtil.isNotEmpty(reqVO.getSkuList())){
            brokerPlanSkuService.createOrUpdatePlanSkuList(reqVO.getId(),reqVO.getSkuList());
        }
        // 结算信息
        if (CollectionUtil.isNotEmpty(reqVO.getBillItem())){
            brokerPlanBillItemService.createOrUpdatePlanBillItemList(reqVO.getId(),reqVO.getBillItem());
        }
        // 联系信息
        if (CollectionUtil.isNotEmpty(reqVO.getContactList())){
            brokerPlanContactService.createOrUpdatePlanContactList(reqVO.getId(),reqVO.getContactList());
        }
        // 达人招募
        if (CollectionUtil.isNotEmpty(reqVO.getFissionList())){
            brokerPlanFissionService.createOrUpdatePlanFissionList(reqVO.getId(),reqVO.getFissionList());
        }
    }

    @Override
    public void deletePlanInfo(Long id) {
        // 校验存在
        validatePlanInfoExists(id);
        // 删除
        planInfoMapper.deleteById(id);
    }

    private void validatePlanInfoExists(Long id) {
        if (planInfoMapper.selectById(id) == null) {
            throw exception(PLAN_INFO_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanInfoDO getPlanInfo(Long id) {
        return planInfoMapper.selectById(id);
    }

    @Override
    public BrokerPlanInfoVO getPlanInfoVoById(Long id) {
        BrokerPlanInfoDO infoDO = planInfoMapper.selectById(id);
        BrokerPlanInfoVO vo = BrokerPlanInfoConvert.INSTANCE.convertVo(infoDO);

        List<BrokerPlanStoreDO> storeList = brokerPlanStoreService.getPlanStoreListByPlanId(id);
        if (CollectionUtil.isNotEmpty(storeList)){
            vo.setStoreList(BrokerPlanStoreConvert.INSTANCE.convertList(storeList));
        }

        List<BrokerPlanSkuDO> skuList = brokerPlanSkuService.getPlanSkuListByPlanId(id);
        if (CollectionUtil.isNotEmpty(skuList)){
            vo.setSkuList(BrokerPlanSkuConvert.INSTANCE.convertList(skuList));
        }

        List<BrokerPlanContactDO> contactList = brokerPlanContactService.getPlanContactListByPlanId(id);
        if (CollectionUtil.isNotEmpty(contactList)){
            vo.setContactList(BrokerPlanContactConvert.INSTANCE.convertList(contactList));
        }

        List<BrokerPlanFissionDO> fissionList = brokerPlanFissionService.getPlanFissionListByPlanId(id);
        if (CollectionUtil.isNotEmpty(fissionList)){
            vo.setFissionList(BrokerPlanFissionConvert.INSTANCE.convertList(fissionList));
        }

        List<BrokerPlanBillItemDO> billItemList = brokerPlanBillItemService.getPlanBillItemListByPlanId(id);
        if (CollectionUtil.isNotEmpty(billItemList)){
            vo.setBillItemList(BrokerPlanBillItemConvert.INSTANCE.convertList(billItemList));
        }

        return vo;
    }

    @Override
    public List<BrokerPlanInfoDO> getPlanInfoList(Collection<Long> ids) {
        return planInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanInfoDO> getPlanInfoPage(BrokerPlanInfoPageReqVO pageReqVO) {
        return planInfoMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanInfoDO> getPlanInfoList(BrokerPlanInfoExportReqVO exportReqVO) {
        return planInfoMapper.selectList(exportReqVO);
    }

}
