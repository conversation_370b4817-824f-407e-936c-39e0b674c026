package com.yitong.octopus.module.broker.convert.planrefund;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;

/**
 * 经纪人计划取消 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanRefundConvert {

    BrokerPlanRefundConvert INSTANCE = Mappers.getMapper(BrokerPlanRefundConvert.class);

    BrokerPlanRefundDO convert(BrokerPlanRefundCreateReqVO bean);

    BrokerPlanRefundDO convert(BrokerPlanRefundUpdateReqVO bean);

    BrokerPlanRefundRespVO convert(BrokerPlanRefundDO bean);

    List<BrokerPlanRefundRespVO> convertList(List<BrokerPlanRefundDO> list);

    PageResult<BrokerPlanRefundRespVO> convertPage(PageResult<BrokerPlanRefundDO> page);

    List<BrokerPlanRefundExcelVO> convertList02(List<BrokerPlanRefundDO> list);

}
