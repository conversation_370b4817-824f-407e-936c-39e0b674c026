package com.yitong.octopus.module.broker.service.channelaccount;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人渠道账户信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberUserChannelAccountService {

    /**
     * 创建经纪人渠道账户信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberUserChannelAccount(@Valid MemberUserChannelAccountCreateReqVO createReqVO);

    /**
     * 更新经纪人渠道账户信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberUserChannelAccount(@Valid MemberUserChannelAccountUpdateReqVO updateReqVO);

    /**
     * 删除经纪人渠道账户信息
     *
     * @param id 编号
     */
    void deleteMemberUserChannelAccount(Long id);

    /**
     * 获得经纪人渠道账户信息
     *
     * @param id 编号
     * @param userId 用户ID
     * @return 经纪人渠道账户信息
     */
    MemberUserChannelAccountDO getMemberUserChannelAccount(Long id,Long userId);

    /**
     * 获得经纪人渠道账户信息列表
     *
     * @param ids 编号
     * @return 经纪人渠道账户信息列表
     */
    List<MemberUserChannelAccountDO> getMemberUserChannelAccountList(Collection<Long> ids);

    /**
     * 获得经纪人渠道账户信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人渠道账户信息分页
     */
    PageResult<MemberUserChannelAccountDO> getMemberUserChannelAccountPage(MemberUserChannelAccountPageReqVO pageReqVO);

    /**
     * 获得经纪人渠道账户信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人渠道账户信息列表
     */
    List<MemberUserChannelAccountDO> getMemberUserChannelAccountList(MemberUserChannelAccountExportReqVO exportReqVO);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, Boolean status);

    /**
     * 修改用户审核状态
     *
     * @param id     用户编号
     * @param auditStatus 审核状态
     * @param remark 备注
     */
    void updateUserAuditStatus(Long id, Integer auditStatus,String remark);
}
