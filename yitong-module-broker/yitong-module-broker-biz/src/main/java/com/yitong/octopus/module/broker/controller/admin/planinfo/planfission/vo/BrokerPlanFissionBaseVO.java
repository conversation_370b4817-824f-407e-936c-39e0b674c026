package com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划招募费用 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanFissionBaseVO {

    @Schema(description = "计划ID", example = "11378")
    private Long planId;

    @Schema(description = "经纪人等级")
    private Integer talentLevel;

    @Schema(description = "费用，招募经纪人的费用")
    private Long amount;

}
