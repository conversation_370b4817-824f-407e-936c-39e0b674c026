package com.yitong.octopus.module.broker.framework.web.config;

import com.yitong.octopus.framework.swagger.config.YitongSwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 经纪人member 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class BrokerMemberWebConfiguration {

    /**
     * member 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi brokerGroupedOpenApi() {
        return YitongSwaggerAutoConfiguration.buildGroupedOpenApi("broker");
    }

}
