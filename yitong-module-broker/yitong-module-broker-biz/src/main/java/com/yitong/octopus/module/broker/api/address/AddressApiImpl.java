package com.yitong.octopus.module.broker.api.address;

import com.yitong.octopus.module.broker.api.address.dto.AddressRespDTO;
import com.yitong.octopus.module.broker.service.address.AddressService;
import com.yitong.octopus.module.broker.convert.address.AddressConvert;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 用户收件地址 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AddressApiImpl implements AddressApi {

    @Resource
    private AddressService addressService;

    @Override
    public AddressRespDTO getAddress(Long id, Long userId) {
        return AddressConvert.INSTANCE.convert02(addressService.getAddress(userId, id));
    }

}
