package com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划合作细则 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanCoopDetailExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("合作细节")
    private String cooperationInfo;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
