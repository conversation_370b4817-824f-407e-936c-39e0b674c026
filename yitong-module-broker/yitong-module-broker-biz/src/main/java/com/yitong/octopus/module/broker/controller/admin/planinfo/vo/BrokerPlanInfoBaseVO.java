package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 经纪人计划信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanInfoBaseVO {

    @Schema(description = "计划名称", example = "芋艿")
    private String name;

    @Schema(description = "计划类型", required = true, example = "1")
    @NotNull(message = "计划类型不能为空")
    private Integer type;

    @Schema(description = "招募类型：1.招募，2.定向", required = true, example = "1")
    @NotNull(message = "招募类型不能为空")
    private Integer planType;

    @Schema(description = "报名开始时间", required = true)
    @NotNull(message = "报名开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyStartTime;

    @Schema(description = "报名结束时间", required = true)
    @NotNull(message = "报名结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime applyEndTime;

    @Schema(description = "探店开始时间", required = true)
    @NotNull(message = "探店开始时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime storeStartTime;

    @Schema(description = "探店结束时间", required = true)
    @NotNull(message = "探店结束时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime storeEndTime;

    @Schema(description = "作品发布开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime publishStartTime;

    @Schema(description = "作品发布结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime publishEndTime;

    @Schema(description = "商家确认最后截止时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime spCheckEndTime;

    @Schema(description = "所属商家ID", example = "11950")
    private Long spId;

    @Schema(description = "内容类型", example = "1")
    private Integer contentType;

    @Schema(description = "结算类型", example = "2")
    private Integer billType;

    @Schema(description = "cps佣金有效天数")
    private Integer cpsBillDays;

    @Schema(description = "招募的经纪人总人数")
    private Integer talentTotal;

    @Schema(description = "经纪人同行人数")
    private Integer talentFellowNo;

    @Schema(description = "是否需要商家确认")
    private Boolean merchantCheck;

    @Schema(description = "是否需要媒介确认")
    private Boolean brokerCheck;

    @Schema(description = "是否是长期招募")
    private Boolean planIsLong;

    @Schema(description = "支持周几探店")
    private Boolean talentIsStoreDays;

}
