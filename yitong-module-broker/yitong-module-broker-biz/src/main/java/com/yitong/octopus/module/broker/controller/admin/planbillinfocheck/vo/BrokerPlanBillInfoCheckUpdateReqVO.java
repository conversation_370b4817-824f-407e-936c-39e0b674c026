package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划账单确认记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanBillInfoCheckUpdateReqVO extends BrokerPlanBillInfoCheckBaseVO {

    @Schema(description = "编号", required = true, example = "13040")
    @NotNull(message = "编号不能为空")
    private Long id;

}
