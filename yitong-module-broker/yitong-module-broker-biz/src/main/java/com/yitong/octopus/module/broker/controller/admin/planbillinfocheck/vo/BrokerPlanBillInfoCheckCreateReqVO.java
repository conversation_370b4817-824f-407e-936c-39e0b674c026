package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划账单确认记录创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanBillInfoCheckCreateReqVO extends BrokerPlanBillInfoCheckBaseVO {

}
