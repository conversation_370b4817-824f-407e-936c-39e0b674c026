package com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划账单确认记录 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_bill_info_check")
@KeySequence("broker_plan_bill_info_check_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanBillInfoCheckDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 计划结算项Id
     */
    private Long planBillId;
    /**
     * 经纪人Id
     */
    private Long talentId;
    /**
     * 最晚确认时间
     */
    private LocalDateTime lastCheckTime;
    /**
     * 实际确认时间
     */
    private LocalDateTime realCheckTime;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 驳回备注
     */
    private String refundMsg;
    /**
     * 作品连接
     */
    private String worksUrl;
    /**
     * 备注
     */
    private String remark;

}
