package com.yitong.octopus.module.broker.dal.mysql.planfission;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionPageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 经纪人计划招募费用 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanFissionMapper extends BaseMapperX<BrokerPlanFissionDO> {

    default PageResult<BrokerPlanFissionDO> selectPage(BrokerPlanFissionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanFissionDO>()
                .eqIfPresent(BrokerPlanFissionDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanFissionDO::getTalentLevel, reqVO.getTalentLevel())
                .eqIfPresent(BrokerPlanFissionDO::getAmount, reqVO.getAmount())
                .betweenIfPresent(BrokerPlanFissionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanFissionDO::getId));
    }

    default List<BrokerPlanFissionDO> selectList(BrokerPlanFissionExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanFissionDO>()
                .eqIfPresent(BrokerPlanFissionDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanFissionDO::getTalentLevel, reqVO.getTalentLevel())
                .eqIfPresent(BrokerPlanFissionDO::getAmount, reqVO.getAmount())
                .betweenIfPresent(BrokerPlanFissionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanFissionDO::getId));
    }

}
