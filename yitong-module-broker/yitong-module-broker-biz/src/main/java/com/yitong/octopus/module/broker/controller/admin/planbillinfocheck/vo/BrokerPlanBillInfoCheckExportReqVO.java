package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划账单确认记录 Excel 导出 Request VO，参数和 BrokerPlanBillInfoCheckPageReqVO 是一致的")
@Data
public class BrokerPlanBillInfoCheckExportReqVO {

    @Schema(description = "计划ID", example = "32702")
    private Long planId;

    @Schema(description = "计划结算项Id", example = "26947")
    private Long planBillId;

    @Schema(description = "经纪人Id", example = "18102")
    private Long talentId;

    @Schema(description = "最晚确认时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastCheckTime;

    @Schema(description = "实际确认时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] realCheckTime;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "驳回备注")
    private String refundMsg;

    @Schema(description = "作品连接", example = "https://www.iocoder.cn")
    private String worksUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
