package com.yitong.octopus.module.broker.service.plancoopdetail;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailUpdateReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划合作细则 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanCoopDetailService {

    /**
     * 创建经纪人计划合作细则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanCoopDetail(@Valid BrokerPlanCoopDetailCreateReqVO createReqVO);

    /**
     * 更新经纪人计划合作细则
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanCoopDetail(@Valid BrokerPlanCoopDetailUpdateReqVO updateReqVO);

    /**
     * 删除经纪人计划合作细则
     *
     * @param id 编号
     */
    void deletePlanCoopDetail(Long id);

    /**
     * 获得经纪人计划合作细则
     *
     * @param id 编号
     * @return 经纪人计划合作细则
     */
    BrokerPlanCoopDetailDO getPlanCoopDetail(Long id);

    /**
     * 获得经纪人计划合作细则列表
     *
     * @param ids 编号
     * @return 经纪人计划合作细则列表
     */
    List<BrokerPlanCoopDetailDO> getPlanCoopDetailList(Collection<Long> ids);

    /**
     * 获得经纪人计划合作细则分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划合作细则分页
     */
    PageResult<BrokerPlanCoopDetailDO> getPlanCoopDetailPage(BrokerPlanCoopDetailPageReqVO pageReqVO);

    /**
     * 获得经纪人计划合作细则列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划合作细则列表
     */
    List<BrokerPlanCoopDetailDO> getPlanCoopDetailList(BrokerPlanCoopDetailExportReqVO exportReqVO);

    /**
     * 创建经纪人计划合作细则
     *
     * @param createReqVOList 创建信息
     * @return 编号
     */
    void createPlanCoopDetailList(List<BrokerPlanCoopDetailCreateReqVO> createReqVOList);

}
