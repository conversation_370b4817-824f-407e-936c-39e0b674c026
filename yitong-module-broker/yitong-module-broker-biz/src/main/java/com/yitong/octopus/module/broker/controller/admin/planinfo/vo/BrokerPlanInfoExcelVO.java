package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanInfoExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划名称")
    private String name;

    @ExcelProperty("计划类型")
    private Integer type;

    @ExcelProperty("招募类型")
    private Integer planType;

    @ExcelProperty("报名开始时间")
    private LocalDateTime applyStartTime;

    @ExcelProperty("报名结束时间")
    private LocalDateTime applyEndTime;

    @ExcelProperty("探店开始时间")
    private LocalDateTime storeStartTime;

    @ExcelProperty("探店结束时间")
    private LocalDateTime storeEndTime;

    @ExcelProperty("作品发布开始时间")
    private LocalDateTime publishStartTime;

    @ExcelProperty("作品发布结束时间")
    private LocalDateTime publishEndTime;

    @ExcelProperty("所属商家ID")
    private Long spId;

    @ExcelProperty("内容类型")
    private Integer contentType;

    @ExcelProperty("结算类型")
    private Integer billType;

    @ExcelProperty("cps佣金有效天数")
    private Integer cpsBillDays;

    @ExcelProperty("招募的经纪人总人数")
    private Integer talentTotal;

    @ExcelProperty("经纪人带货能力要求:0 否，1 是")
    private Boolean talentLevelNeed;

    @ExcelProperty("经纪人等级")
    private Integer talentLevel;

    @ExcelProperty("经纪人粉丝数要求:0 否，1 是")
    private Boolean talentFansNeed;

    @ExcelProperty("经纪人粉丝数")
    private Integer talentFans;

    @ExcelProperty("经纪人同行人数")
    private Integer talentFellowNo;

    @ExcelProperty("是否需要商家确认")
    private Boolean merchantCheck;

    @ExcelProperty("是否需要媒介确认")
    private Boolean brokerCheck;

    @ExcelProperty("是否是长期招募")
    private Boolean planIsLong;

    @ExcelProperty("支持周几探店:0 期望每周可探店时间，1 不限制每周可探店时间")
    private Boolean talentIsStoreDays;

    @ExcelProperty("状态")
    private Integer status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
