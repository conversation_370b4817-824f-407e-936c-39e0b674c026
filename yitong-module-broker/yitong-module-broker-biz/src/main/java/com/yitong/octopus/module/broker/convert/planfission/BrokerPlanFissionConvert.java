package com.yitong.octopus.module.broker.convert.planfission;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;

/**
 * 经纪人计划招募费用 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanFissionConvert {

    BrokerPlanFissionConvert INSTANCE = Mappers.getMapper(BrokerPlanFissionConvert.class);

    BrokerPlanFissionDO convert(BrokerPlanFissionReqVO bean);

    BrokerPlanFissionRespVO convert(BrokerPlanFissionDO bean);

    List<BrokerPlanFissionRespVO> convertList(List<BrokerPlanFissionDO> list);

    PageResult<BrokerPlanFissionRespVO> convertPage(PageResult<BrokerPlanFissionDO> page);

    List<BrokerPlanFissionExcelVO> convertList02(List<BrokerPlanFissionDO> list);

}
