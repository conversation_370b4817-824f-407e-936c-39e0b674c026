package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划申请创建 Request VO")
@Data
@ToString(callSuper = true)
public class BrokerPlanInfoApplyReqVO {

    @Schema(description = "计划ID", example = "5926")
    @NotNull(message = "计划不能为空")
    private Long planId;

    @Schema(description = "经纪人Id", example = "19797")
    @NotNull(message = "经纪人不能为空")
    private Long talentId;

    @Schema(description = "经纪人渠道Id", example = "30311")
    @NotNull(message = "经纪人渠道不能为空")
    private Long talentAccountId;

    @Schema(description = "渠道Id", example = "13473")
    @NotNull(message = "渠道不能为空")
    private Long channelId;

    @Schema(description = "渠道编码")
    @NotNull(message = "渠道编码不能为空")
    private String channelCode;

}
