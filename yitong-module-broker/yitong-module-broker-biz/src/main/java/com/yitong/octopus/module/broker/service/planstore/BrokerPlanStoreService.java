package com.yitong.octopus.module.broker.service.planstore;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStorePageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 计划门店信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanStoreService {

    /**
     * 创建计划门店信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanStore(@Valid BrokerPlanStoreReqVO createReqVO);

    /**
     * 更新计划门店信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanStore(@Valid BrokerPlanStoreReqVO updateReqVO);

    /**
     * 删除计划门店信息
     *
     * @param id 编号
     */
    void deletePlanStore(Long id);


    /**
     * 获得计划门店信息
     *
     * @param id 编号
     * @return 计划门店信息
     */
    BrokerPlanStoreDO getPlanStore(Long id);

    /**
     * 获得计划门店信息列表
     *
     * @param ids 编号
     * @return 计划门店信息列表
     */
    List<BrokerPlanStoreDO> getPlanStoreList(Collection<Long> ids);

    /**
     * 获得计划门店信息分页
     *
     * @param pageReqVO 分页查询
     * @return 计划门店信息分页
     */
    PageResult<BrokerPlanStoreDO> getPlanStorePage(BrokerPlanStorePageReqVO pageReqVO);

    /**
     * 获得计划门店信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 计划门店信息列表
     */
    List<BrokerPlanStoreDO> getPlanStoreList(BrokerPlanStoreExportReqVO exportReqVO);

    /**
     * 创建计划门店信息
     * @param planId 计划Id
     * @param createReqVOList 创建信息列表
     */
    void createOrUpdatePlanStoreList(Long planId, List<BrokerPlanStoreReqVO> createReqVOList);

    /**
     * 根据计划Id删除计划门店信息
     *
     * @param planId 计划编号
     */
    void deletePlanStoreByPlanId(Long planId);

    /**
     * 获得计划门店信息列表
     *
     * @param planId 计划Id
     * @return 计划门店信息列表
     */
    List<BrokerPlanStoreDO> getPlanStoreListByPlanId(Long planId);
}
