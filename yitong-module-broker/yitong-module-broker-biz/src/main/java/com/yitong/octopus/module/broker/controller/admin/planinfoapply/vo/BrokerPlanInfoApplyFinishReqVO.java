package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import static io.swagger.v3.oas.annotations.media.Schema.RequiredMode.REQUIRED;

@Schema(description = "管理后台 - 经纪人计划完成 Request VO")
@Data
@ToString(callSuper = true)
public class BrokerPlanInfoApplyFinishReqVO {

    @Schema(description = "编号",requiredMode = REQUIRED, example = "18294")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "计划ID", example = "5926")
    private Long planId;

    @Schema(description = "作品ID",requiredMode = REQUIRED, example = "24009")
    @NotEmpty(message = "作品Id不能为空")
    private String worksId;

    @Schema(description = "作品短连接",requiredMode = REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "短连接不能为空")
    private String worksShortUrl;

    @Schema(description = "作品连接",requiredMode = REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "连接不能为空")
    private String worksUrl;

    @Schema(description = "备注", example = "随便")
    private String remark;
}
