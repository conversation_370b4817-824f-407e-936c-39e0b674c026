//package com.yitong.octopus.module.broker.controller.admin.planinfo.back;
//
//import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.controller.admin.plansku.vo.*;
//import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
//import com.yitong.octopus.module.broker.convert.plansku.BrokerPlanSkuConvert;
//import com.yitong.octopus.module.broker.service.plansku.BrokerPlanSkuService;
//
//@Tag(name = "管理后台 - 经纪人计划sku信息")
//@RestController
//@RequestMapping("/broker/plan-sku")
//@Validated
//public class BrokerPlanSkuController {
//
//    @Resource
//    private BrokerPlanSkuService planSkuService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建经纪人计划sku信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:create')")
//    public CommonResult<Long> createPlanSku(@Valid @RequestBody BrokerPlanSkuCreateReqVO createReqVO) {
//        return success(planSkuService.createPlanSku(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新经纪人计划sku信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:update')")
//    public CommonResult<Boolean> updatePlanSku(@Valid @RequestBody BrokerPlanSkuUpdateReqVO updateReqVO) {
//        planSkuService.updatePlanSku(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除经纪人计划sku信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:delete')")
//    public CommonResult<Boolean> deletePlanSku(@RequestParam("id") Long id) {
//        planSkuService.deletePlanSku(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得经纪人计划sku信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:query')")
//    public CommonResult<BrokerPlanSkuRespVO> getPlanSku(@RequestParam("id") Long id) {
//        BrokerPlanSkuDO planSku = planSkuService.getPlanSku(id);
//        return success(BrokerPlanSkuConvert.INSTANCE.convert(planSku));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得经纪人计划sku信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:query')")
//    public CommonResult<List<BrokerPlanSkuRespVO>> getPlanSkuList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanSkuDO> list = planSkuService.getPlanSkuList(ids);
//        return success(BrokerPlanSkuConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得经纪人计划sku信息分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:query')")
//    public CommonResult<PageResult<BrokerPlanSkuRespVO>> getPlanSkuPage(@Valid BrokerPlanSkuPageReqVO pageVO) {
//        PageResult<BrokerPlanSkuDO> pageResult = planSkuService.getPlanSkuPage(pageVO);
//        return success(BrokerPlanSkuConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出经纪人计划sku信息 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-sku:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanSkuExcel(@Valid BrokerPlanSkuExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanSkuDO> list = planSkuService.getPlanSkuList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanSkuExcelVO> datas = BrokerPlanSkuConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "经纪人计划sku信息.xls", "数据", BrokerPlanSkuExcelVO.class, datas);
//    }
//
//}
