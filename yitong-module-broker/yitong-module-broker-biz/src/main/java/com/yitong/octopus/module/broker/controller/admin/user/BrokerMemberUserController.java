package com.yitong.octopus.module.broker.controller.admin.user;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.broker.controller.admin.user.convert.memberuser.BrokerMemberUserConvert;
import com.yitong.octopus.module.broker.controller.admin.user.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import com.yitong.octopus.module.broker.service.user.BrokerMemberUserService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;


import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 经纪人用户")
@RestController
@RequestMapping("/broker/member-user")
@Validated
public class BrokerMemberUserController {

    @Resource
    private BrokerMemberUserService memberUserService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人用户")
    @PreAuthorize("@ss.hasPermission('broker:member-user:create')")
    public CommonResult<Long> createMemberUser(@Valid @RequestBody BrokerMemberUserCreateReqVO createReqVO) {
        return success(memberUserService.createMemberUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人用户")
    @PreAuthorize("@ss.hasPermission('broker:member-user:update')")
    public CommonResult<Boolean> updateMemberUser(@Valid @RequestBody BrokerMemberUserUpdateReqVO updateReqVO) {
        memberUserService.updateMemberUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:member-user:delete')")
    public CommonResult<Boolean> deleteMemberUser(@RequestParam("id") Long id) {
        memberUserService.deleteMemberUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:member-user:query')")
    public CommonResult<BrokerMemberUserRespVO> getMemberUser(@RequestParam("id") Long id) {
        BrokerMemberUserDO memberUser = memberUserService.getMemberUser(id);
        return success(BrokerMemberUserConvert.INSTANCE.convert(memberUser));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人用户列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:member-user:query')")
    public CommonResult<List<BrokerMemberUserRespVO>> getMemberUserList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerMemberUserDO> list = memberUserService.getMemberUserList(ids);
        return success(BrokerMemberUserConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人用户分页")
    @PreAuthorize("@ss.hasPermission('broker:member-user:query')")
    public CommonResult<PageResult<BrokerMemberUserRespVO>> getMemberUserPage(@Valid BrokerMemberUserPageReqVO pageVO) {
        PageResult<BrokerMemberUserDO> pageResult = memberUserService.getMemberUserPage(pageVO);
        return success(BrokerMemberUserConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人用户 Excel")
    @PreAuthorize("@ss.hasPermission('broker:member-user:export')")
    @OperateLog(type = EXPORT)
    public void exportMemberUserExcel(@Valid BrokerMemberUserExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerMemberUserDO> list = memberUserService.getMemberUserList(exportReqVO);
        // 导出 Excel
        List<BrokerMemberUserExcelVO> datas = BrokerMemberUserConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人用户.xls", "数据", BrokerMemberUserExcelVO.class, datas);
    }

    @PutMapping("/update-password")
    @Operation(summary = "重置用户密码")
    @PreAuthorize("@ss.hasPermission('broker:member-user:update-password')")
    public CommonResult<Boolean> updateUserPassword(@Valid @RequestBody BrokerMemberUserUpdatePasswordReqVO reqVO) {
        memberUserService.updateUserPassword(reqVO.getId(), reqVO.getPassword());
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改用户状态")
    @PreAuthorize("@ss.hasPermission('broker:member-user:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody BrokerMemberUserUpdateStatusReqVO reqVO) {
        memberUserService.updateUserStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }
}
