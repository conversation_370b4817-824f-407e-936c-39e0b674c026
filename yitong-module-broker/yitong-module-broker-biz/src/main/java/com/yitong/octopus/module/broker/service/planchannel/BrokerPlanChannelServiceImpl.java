package com.yitong.octopus.module.broker.service.planchannel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import com.yitong.octopus.module.broker.convert.planstore.BrokerPlanStoreConvert;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planchannel.BrokerPlanChannelConvert;
import com.yitong.octopus.module.broker.dal.mysql.planchannel.BrokerPlanChannelMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划渠道信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanChannelServiceImpl implements BrokerPlanChannelService {

    @Resource
    private BrokerPlanChannelMapper planChannelMapper;

    @Override
    public Long createPlanChannel(BrokerPlanChannelReqVO createReqVO) {
        // 插入
        BrokerPlanChannelDO planChannel = BrokerPlanChannelConvert.INSTANCE.convert(createReqVO);
        planChannelMapper.insert(planChannel);
        // 返回
        return planChannel.getId();
    }

    @Override
    public void updatePlanChannel(BrokerPlanChannelReqVO updateReqVO) {
        // 校验存在
        validatePlanChannelExists(updateReqVO.getId());
        // 更新
        BrokerPlanChannelDO updateObj = BrokerPlanChannelConvert.INSTANCE.convert(updateReqVO);
        planChannelMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanChannel(Long id) {
        // 校验存在
        validatePlanChannelExists(id);
        // 删除
        planChannelMapper.deleteById(id);
    }

    private void validatePlanChannelExists(Long id) {
        if (planChannelMapper.selectById(id) == null) {
            throw exception(PLAN_CHANNEL_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanChannelDO getPlanChannel(Long id) {
        return planChannelMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanChannelDO> getPlanChannelList(Collection<Long> ids) {
        return planChannelMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanChannelDO> getPlanChannelPage(BrokerPlanChannelPageReqVO pageReqVO) {
        return planChannelMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanChannelDO> getPlanChannelList(BrokerPlanChannelExportReqVO exportReqVO) {
        return planChannelMapper.selectList(exportReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanChannelList(Long planId,List<BrokerPlanChannelReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanChannelDO> list = getPlanChannelListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanChannelDO> saveList =   Lists.newArrayList();
        List<BrokerPlanChannelDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanChannelDO so = BrokerPlanChannelConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanChannelDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planChannelMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planChannelMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planChannelMapper.updateBatchById(updateList);
        }
    }

    @Override
    public List<BrokerPlanChannelDO> getPlanChannelListByPlanId(Long planId) {
        return planChannelMapper.selectList(new LambdaQueryWrapperX<BrokerPlanChannelDO>()
                .eq(BrokerPlanChannelDO::getPlanId,planId));
    }

}
