package com.yitong.octopus.module.broker.controller.admin.user.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 经纪人用户创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerMemberUserCreateReqVO extends BrokerMemberUserBaseVO {

    @Schema(description = "密码", required = true)
    @NotNull(message = "密码不能为空")
    private String password;

}
