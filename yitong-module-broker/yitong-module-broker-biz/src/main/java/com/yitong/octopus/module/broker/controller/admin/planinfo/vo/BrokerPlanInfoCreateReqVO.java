package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 经纪人计划信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoCreateReqVO extends BrokerPlanInfoBaseVO {

    /**
     * mon(星期一)，tues(星期二)，wed(星期三)，thur(星期四)，fri(星期五)，sat(星期六)，sun(星期日)
     */
    @Schema(description = "期望周几,多个','分隔")
    private String talentStoreDays;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "渠道信息", required = true)
    @NotNull(message = "渠道不能为空")
    private List<BrokerPlanChannelReqVO> channelList;

    @Schema(description = "门店信息", required = true)
    @NotNull(message = "门店不能为空")
    private List<BrokerPlanStoreReqVO> storeList;

    @Schema(description = "商品信息", required = true)
    @NotNull(message = "商品不能为空")
    private List<BrokerPlanSkuReqVO> skuList;

    @Schema(description = "招募费用")
    private List<BrokerPlanFissionReqVO> fissionList;

    @Schema(description = "结算信息", required = true)
    @NotNull(message = "结算信息不能为空")
    private List<BrokerPlanBillItemReqVO> billItem;

    @Schema(description = "联系信息", required = true)
    @NotNull(message = "联系信息不能为空")
    private List<BrokerPlanContactReqVO> contactList;

}
