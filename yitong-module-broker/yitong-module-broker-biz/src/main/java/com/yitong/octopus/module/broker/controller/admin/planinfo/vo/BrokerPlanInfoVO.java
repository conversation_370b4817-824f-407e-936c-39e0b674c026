package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 经纪人计划信息【详情】 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanInfoVO extends BrokerPlanInfoBaseVO {

    @Schema(description = "编号", required = true, example = "27422")
    private Long id;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "门店信息")
    private List<BrokerPlanStoreRespVO> storeList;

    @Schema(description = "商品信息")
    private List<BrokerPlanSkuRespVO> skuList;

    @Schema(description = "招募费用")
    private List<BrokerPlanFissionRespVO> fissionList;

    @Schema(description = "结算信息")
    private List<BrokerPlanBillItemRespVO> billItemList;

    @Schema(description = "联系信息")
    private List<BrokerPlanContactRespVO> contactList;

    @Schema(description = "渠道信息")
    private List<BrokerPlanChannelRespVO> channelList;
}
