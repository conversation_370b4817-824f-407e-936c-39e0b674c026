package com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划申请 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanInfoApplyExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("经纪人Id")
    private Long talentId;

    @ExcelProperty("经纪人渠道Id")
    private Long talentAccountId;

    @ExcelProperty("渠道Id")
    private Long channelId;

    @ExcelProperty("渠道编码")
    private String channelCode;

    @ExcelProperty("状态: 0 待审核，1 已通过，2已拒绝，3进行中，4已完成，5已结算")
    private Byte status;

    @ExcelProperty("作品ID")
    private Long worksId;

    @ExcelProperty("作品短连接")
    private String worksShortUrl;

    @ExcelProperty("作品连接")
    private String worksUrl;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
