package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经纪人渠道账户信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserChannelAccountRespVO extends MemberUserChannelAccountBaseVO {

    @Schema(description = "编号", required = true, example = "11988")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "状态", example = "1")
    private Boolean status;
}
