package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划账单确认记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanBillInfoCheckExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("计划结算项Id")
    private Long planBillId;

    @ExcelProperty("经纪人Id")
    private Long talentId;

    @ExcelProperty("最晚确认时间")
    private LocalDateTime lastCheckTime;

    @ExcelProperty("实际确认时间")
    private LocalDateTime realCheckTime;

    @ExcelProperty("状态")
    private Integer status;

    @ExcelProperty("驳回备注")
    private String refundMsg;

    @ExcelProperty("作品连接")
    private String worksUrl;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
