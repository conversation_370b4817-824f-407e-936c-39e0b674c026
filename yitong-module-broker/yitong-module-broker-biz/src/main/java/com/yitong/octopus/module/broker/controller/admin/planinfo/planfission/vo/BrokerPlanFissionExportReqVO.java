package com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划招募费用 Excel 导出 Request VO，参数和 BrokerPlanFissionPageReqVO 是一致的")
@Data
public class BrokerPlanFissionExportReqVO {

    @Schema(description = "计划ID", example = "11378")
    private Long planId;

    @Schema(description = "经纪人等级")
    private Integer talentLevel;

    @Schema(description = "费用，招募经纪人的费用")
    private Long amount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
