package com.yitong.octopus.module.broker.service.planbillitem;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import com.yitong.octopus.module.broker.convert.plansku.BrokerPlanSkuConvert;
import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.planbillitem.BrokerPlanBillItemConvert;
import com.yitong.octopus.module.broker.dal.mysql.planbillitem.BrokerPlanBillItemMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.PLAN_BILL_ITEM_NOT_EXISTS;

/**
 * 经纪人计划结算信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanBillItemServiceImpl implements BrokerPlanBillItemService {

    @Resource
    private BrokerPlanBillItemMapper planBillItemMapper;

    @Override
    public Long createPlanBillItem(BrokerPlanBillItemReqVO createReqVO) {
        // 插入
        BrokerPlanBillItemDO planBillItem = BrokerPlanBillItemConvert.INSTANCE.convert(createReqVO);
        planBillItemMapper.insert(planBillItem);
        // 返回
        return planBillItem.getId();
    }

    @Override
    public void updatePlanBillItem(BrokerPlanBillItemReqVO updateReqVO) {
        // 校验存在
        validatePlanBillItemExists(updateReqVO.getId());
        // 更新
        BrokerPlanBillItemDO updateObj = BrokerPlanBillItemConvert.INSTANCE.convert(updateReqVO);
        planBillItemMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanBillItem(Long id) {
        // 校验存在
        validatePlanBillItemExists(id);
        // 删除
        planBillItemMapper.deleteById(id);
    }

    private void validatePlanBillItemExists(Long id) {
        if (planBillItemMapper.selectById(id) == null) {
            throw exception(PLAN_BILL_ITEM_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanBillItemDO getPlanBillItem(Long id) {
        return planBillItemMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanBillItemDO> getPlanBillItemList(Collection<Long> ids) {
        return planBillItemMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanBillItemDO> getPlanBillItemPage(BrokerPlanBillItemPageReqVO pageReqVO) {
        return planBillItemMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanBillItemDO> getPlanBillItemList(BrokerPlanBillItemExportReqVO exportReqVO) {
        return planBillItemMapper.selectList(exportReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanBillItemList(Long planId, List<BrokerPlanBillItemReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanBillItemDO> list = getPlanBillItemListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanBillItemDO> saveList =   Lists.newArrayList();
        List<BrokerPlanBillItemDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanBillItemDO so = BrokerPlanBillItemConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanBillItemDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planBillItemMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planBillItemMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planBillItemMapper.updateBatchById(updateList);
        }
    }

    @Override
    public void deletePlanBillItemByPlanId(Long planId) {
        planBillItemMapper.delete(new LambdaQueryWrapperX<BrokerPlanBillItemDO>()
                .eq(BrokerPlanBillItemDO::getPlanId,planId));
    }

    @Override
    public List<BrokerPlanBillItemDO> getPlanBillItemListByPlanId(Long planId) {
       return planBillItemMapper.selectList(new LambdaQueryWrapperX<BrokerPlanBillItemDO>()
                .eq(BrokerPlanBillItemDO::getPlanId,planId));
    }

}
