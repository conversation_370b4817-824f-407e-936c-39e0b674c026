//package com.yitong.octopus.module.broker.controller.admin.planinfo.back;
//
//import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
//import com.yitong.octopus.module.broker.convert.planstore.BrokerPlanStoreConvert;
//import com.yitong.octopus.module.broker.service.planstore.BrokerPlanStoreService;
//
//@Tag(name = "管理后台 - 计划门店信息")
//@RestController
//@RequestMapping("/broker/plan-store")
//@Validated
//public class BrokerPlanStoreController {
//
//    @Resource
//    private BrokerPlanStoreService planStoreService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建计划门店信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:create')")
//    public CommonResult<Long> createPlanStore(@Valid @RequestBody BrokerPlanStoreCreateReqVO createReqVO) {
//        return success(planStoreService.createPlanStore(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新计划门店信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:update')")
//    public CommonResult<Boolean> updatePlanStore(@Valid @RequestBody BrokerPlanStoreUpdateReqVO updateReqVO) {
//        planStoreService.updatePlanStore(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除计划门店信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:delete')")
//    public CommonResult<Boolean> deletePlanStore(@RequestParam("id") Long id) {
//        planStoreService.deletePlanStore(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得计划门店信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:query')")
//    public CommonResult<BrokerPlanStoreRespVO> getPlanStore(@RequestParam("id") Long id) {
//        BrokerPlanStoreDO planStore = planStoreService.getPlanStore(id);
//        return success(BrokerPlanStoreConvert.INSTANCE.convert(planStore));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得计划门店信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:query')")
//    public CommonResult<List<BrokerPlanStoreRespVO>> getPlanStoreList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanStoreDO> list = planStoreService.getPlanStoreList(ids);
//        return success(BrokerPlanStoreConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得计划门店信息分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:query')")
//    public CommonResult<PageResult<BrokerPlanStoreRespVO>> getPlanStorePage(@Valid BrokerPlanStorePageReqVO pageVO) {
//        PageResult<BrokerPlanStoreDO> pageResult = planStoreService.getPlanStorePage(pageVO);
//        return success(BrokerPlanStoreConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出计划门店信息 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-store:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanStoreExcel(@Valid BrokerPlanStoreExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanStoreDO> list = planStoreService.getPlanStoreList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanStoreExcelVO> datas = BrokerPlanStoreConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "计划门店信息.xls", "数据", BrokerPlanStoreExcelVO.class, datas);
//    }
//
//}
