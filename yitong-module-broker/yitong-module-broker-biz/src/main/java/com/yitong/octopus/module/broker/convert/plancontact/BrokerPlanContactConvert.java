package com.yitong.octopus.module.broker.convert.plancontact;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactRespVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;

/**
 * 经纪人计划联系信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanContactConvert {

    BrokerPlanContactConvert INSTANCE = Mappers.getMapper(BrokerPlanContactConvert.class);

    BrokerPlanContactDO convert(BrokerPlanContactReqVO bean);

    BrokerPlanContactRespVO convert(BrokerPlanContactDO bean);

    List<BrokerPlanContactRespVO> convertList(List<BrokerPlanContactDO> list);

    PageResult<BrokerPlanContactRespVO> convertPage(PageResult<BrokerPlanContactDO> page);

    List<BrokerPlanContactExcelVO> convertList02(List<BrokerPlanContactDO> list);

}
