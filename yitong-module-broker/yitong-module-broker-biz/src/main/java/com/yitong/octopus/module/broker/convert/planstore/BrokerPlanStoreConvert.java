package com.yitong.octopus.module.broker.convert.planstore;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreExcelVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;

/**
 * 计划门店信息 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanStoreConvert {

    BrokerPlanStoreConvert INSTANCE = Mappers.getMapper(BrokerPlanStoreConvert.class);

    BrokerPlanStoreDO convert(BrokerPlanStoreReqVO bean);

    BrokerPlanStoreRespVO convert(BrokerPlanStoreDO bean);

    List<BrokerPlanStoreRespVO> convertList(List<BrokerPlanStoreDO> list);

    PageResult<BrokerPlanStoreRespVO> convertPage(PageResult<BrokerPlanStoreDO> page);

    List<BrokerPlanStoreExcelVO> convertList02(List<BrokerPlanStoreDO> list);

}
