package com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 计划门店信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanStoreBaseVO {

    @Schema(description = "所属商家ID", example = "1052")
    private Long spId;

    @Schema(description = "门店Id", example = "27984")
    private Long storeId;

    @Schema(description = "计划ID", example = "32502")
    private Long planId;

}
