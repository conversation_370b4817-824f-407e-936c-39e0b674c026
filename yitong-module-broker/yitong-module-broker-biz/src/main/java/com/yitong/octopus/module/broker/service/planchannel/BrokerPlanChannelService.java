package com.yitong.octopus.module.broker.service.planchannel;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 经纪人计划渠道信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanChannelService {

    /**
     * 创建经纪人计划渠道信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanChannel(@Valid BrokerPlanChannelReqVO createReqVO);

    /**
     * 更新经纪人计划渠道信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanChannel(@Valid BrokerPlanChannelReqVO updateReqVO);

    /**
     * 删除经纪人计划渠道信息
     *
     * @param id 编号
     */
    void deletePlanChannel(Long id);

    /**
     * 获得经纪人计划渠道信息
     *
     * @param id 编号
     * @return 经纪人计划渠道信息
     */
    BrokerPlanChannelDO getPlanChannel(Long id);

    /**
     * 获得经纪人计划渠道信息列表
     *
     * @param ids 编号
     * @return 经纪人计划渠道信息列表
     */
    List<BrokerPlanChannelDO> getPlanChannelList(Collection<Long> ids);

    /**
     * 获得经纪人计划渠道信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划渠道信息分页
     */
    PageResult<BrokerPlanChannelDO> getPlanChannelPage(BrokerPlanChannelPageReqVO pageReqVO);

    /**
     * 获得经纪人计划渠道信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划渠道信息列表
     */
    List<BrokerPlanChannelDO> getPlanChannelList(BrokerPlanChannelExportReqVO exportReqVO);

    /**
     * 创建经纪人计划渠道信息
     *
     * @param planId 计划Id
     * @param createReqVOList 创建信息
     * @return 编号
     */
    void createOrUpdatePlanChannelList(Long planId,List<BrokerPlanChannelReqVO> createReqVOList);

    /**
     * 获得经纪人计划渠道信息列表
     *
     * @param planId 计划Id
     * @return 经纪人计划渠道信息列表
     */
    List<BrokerPlanChannelDO> getPlanChannelListByPlanId(Long planId);

}
