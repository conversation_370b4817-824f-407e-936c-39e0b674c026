package com.yitong.octopus.module.broker.controller.app.user;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.broker.controller.app.user.vo.AppUserInfoRespVO;
import com.yitong.octopus.module.broker.controller.app.user.vo.AppUserUpdateMobileReqVO;
import com.yitong.octopus.module.broker.service.user.BrokerMemberUserService;
import com.yitong.octopus.module.broker.convert.user.UserConvert;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
import static com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static com.yitong.octopus.module.infra.enums.ErrorCodeConstants.FILE_IS_EMPTY;

@Tag(name = "经纪人 APP - 用户个人中心")
@RestController
@RequestMapping("/broker-member/user")
@Validated
@Slf4j
public class AppUserController {

    @Resource
    private BrokerMemberUserService userService;

    @PutMapping("/update-nickname")
    @Operation(summary = "修改用户昵称")
    @PreAuthenticated
    public CommonResult<Boolean> updateUserNickname(@RequestParam("nickname") String nickname) {
        userService.updateUserNickname(getLoginUserId(), nickname);
        return success(true);
    }

    @PostMapping("/update-avatar")
    @Operation(summary = "修改用户头像")
    @PreAuthenticated
    public CommonResult<String> updateUserAvatar(@RequestParam("avatarFile") MultipartFile file) throws Exception {
        if (file.isEmpty()) {
            throw exception(FILE_IS_EMPTY);
        }
        String avatar = userService.updateUserAvatar(getLoginUserId(), file.getInputStream());
        return success(avatar);
    }

    @GetMapping("/get")
    @Operation(summary = "获得基本信息")
    @PreAuthenticated
    public CommonResult<AppUserInfoRespVO> getUserInfo() {
        BrokerMemberUserDO user = userService.getUser(getLoginUserId());
        return success(UserConvert.INSTANCE.convert(user));
    }

    @PostMapping("/update-mobile")
    @Operation(summary = "修改用户手机")
    @PreAuthenticated
    public CommonResult<Boolean> updateMobile(@RequestBody @Valid AppUserUpdateMobileReqVO reqVO) {
        userService.updateUserMobile(getLoginUserId(), reqVO);
        return success(true);
    }

}

