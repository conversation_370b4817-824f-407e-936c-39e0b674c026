package com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 计划门店信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanStoreReqVO extends BrokerPlanStoreBaseVO {

    @Schema(description = "编号", example = "2555")
    private Long id;

}
