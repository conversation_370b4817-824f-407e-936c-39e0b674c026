package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经纪人渠道账户信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberUserChannelAccountUpdateReqVO extends MemberUserChannelAccountBaseVO {

    @Schema(description = "编号", required = true, example = "11988")
    @NotNull(message = "编号不能为空")
    private Long id;

}
