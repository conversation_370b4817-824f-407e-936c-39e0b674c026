package com.yitong.octopus.module.broker.controller.admin.planrefund.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划取消 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanRefundBaseVO {

    @Schema(description = "计划ID", example = "20071")
    private Long planId;

    @Schema(description = "状态: 0 待审核，1 已通过，2已拒绝", example = "2")
    private Byte status;

    @Schema(description = "计划渠道备注")
    private String refundMsg;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
