package com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划账单确认记录 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanBillInfoCheckRespVO extends BrokerPlanBillInfoCheckBaseVO {

    @Schema(description = "编号", required = true, example = "13040")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
