package com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 经纪人计划招募费用 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class BrokerPlanFissionExcelVO {

    @ExcelProperty("编号")
    private Long id;

    @ExcelProperty("计划ID")
    private Long planId;

    @ExcelProperty("经纪人等级")
    private Integer talentLevel;

    @ExcelProperty("费用，招募经纪人的费用")
    private Long amount;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
