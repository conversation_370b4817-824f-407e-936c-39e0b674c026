package com.yitong.octopus.module.broker.service.plancontact;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import com.yitong.octopus.module.broker.convert.planbillitem.BrokerPlanBillItemConvert;
import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.plancontact.BrokerPlanContactConvert;
import com.yitong.octopus.module.broker.dal.mysql.plancontact.BrokerPlanContactMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划联系信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanContactServiceImpl implements BrokerPlanContactService {

    @Resource
    private BrokerPlanContactMapper planContactMapper;

    @Override
    public Long createPlanContact(BrokerPlanContactReqVO createReqVO) {
        // 插入
        BrokerPlanContactDO planContact = BrokerPlanContactConvert.INSTANCE.convert(createReqVO);
        planContactMapper.insert(planContact);
        // 返回
        return planContact.getId();
    }

    @Override
    public void updatePlanContact(BrokerPlanContactReqVO updateReqVO) {
        // 校验存在
        validatePlanContactExists(updateReqVO.getId());
        // 更新
        BrokerPlanContactDO updateObj = BrokerPlanContactConvert.INSTANCE.convert(updateReqVO);
        planContactMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanContact(Long id) {
        // 校验存在
        validatePlanContactExists(id);
        // 删除
        planContactMapper.deleteById(id);
    }

    private void validatePlanContactExists(Long id) {
        if (planContactMapper.selectById(id) == null) {
            throw exception(PLAN_CONTACT_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanContactDO getPlanContact(Long id) {
        return planContactMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanContactDO> getPlanContactList(Collection<Long> ids) {
        return planContactMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanContactDO> getPlanContactPage(BrokerPlanContactPageReqVO pageReqVO) {
        return planContactMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanContactDO> getPlanContactList(BrokerPlanContactExportReqVO exportReqVO) {
        return planContactMapper.selectList(exportReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createOrUpdatePlanContactList(Long planId, List<BrokerPlanContactReqVO> createReqVOList) {
        if (CollectionUtil.isEmpty(createReqVOList)){
            return;
        }
        //查询属于该计划的信息
        List<BrokerPlanContactDO> list = getPlanContactListByPlanId(planId);
        //分类新增/修改/删除
        List<BrokerPlanContactDO> saveList =   Lists.newArrayList();
        List<BrokerPlanContactDO> updateList = Lists.newArrayList();
        List<Long> existsIds = Lists.newArrayList();
        createReqVOList.forEach(i->{
            BrokerPlanContactDO so = BrokerPlanContactConvert.INSTANCE.convert(i);
            so.setPlanId(planId);
            if (ObjectUtil.isNotNull(i.getId())){
                updateList.add(so);
                existsIds.add(i.getId());
            }else {
                saveList.add(so);
            }
        });
        List<Long> deleteIds = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(list)){
            //过滤出不存在的ID
            deleteIds = list.stream().map(BrokerPlanContactDO::getId).filter(id -> !existsIds.contains(id)).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(deleteIds)){
            planContactMapper.deleteBatchIds(deleteIds);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            planContactMapper.insertBatch(saveList);
        }
        if (CollectionUtil.isNotEmpty(updateList)){
            planContactMapper.updateBatchById(updateList);
        }
    }

    @Override
    public void deletePlanContactByPlanId(Long planId) {
        planContactMapper.delete(new LambdaQueryWrapperX<BrokerPlanContactDO>()
                .eq(BrokerPlanContactDO::getPlanId,planId));
    }

    @Override
    public List<BrokerPlanContactDO> getPlanContactListByPlanId(Long planId) {
        return planContactMapper.selectList(new LambdaQueryWrapperX<BrokerPlanContactDO>()
                .eq(BrokerPlanContactDO::getPlanId,planId));
    }

}
