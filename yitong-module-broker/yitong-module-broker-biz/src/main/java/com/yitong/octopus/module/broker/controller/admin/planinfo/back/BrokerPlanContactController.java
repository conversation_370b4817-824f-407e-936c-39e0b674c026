//package com.yitong.octopus.module.broker.controller.admin.planinfo.back;
//
//import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.*;
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
//import com.yitong.octopus.module.broker.convert.plancontact.BrokerPlanContactConvert;
//import com.yitong.octopus.module.broker.service.plancontact.BrokerPlanContactService;
//
//@Tag(name = "管理后台 - 经纪人计划联系信息")
//@RestController
//@RequestMapping("/broker/plan-contact")
//@Validated
//public class BrokerPlanContactController {
//
//    @Resource
//    private BrokerPlanContactService planContactService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建经纪人计划联系信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:create')")
//    public CommonResult<Long> createPlanContact(@Valid @RequestBody BrokerPlanContactCreateReqVO createReqVO) {
//        return success(planContactService.createPlanContact(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新经纪人计划联系信息")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:update')")
//    public CommonResult<Boolean> updatePlanContact(@Valid @RequestBody BrokerPlanContactUpdateReqVO updateReqVO) {
//        planContactService.updatePlanContact(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除经纪人计划联系信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:delete')")
//    public CommonResult<Boolean> deletePlanContact(@RequestParam("id") Long id) {
//        planContactService.deletePlanContact(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得经纪人计划联系信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:query')")
//    public CommonResult<BrokerPlanContactRespVO> getPlanContact(@RequestParam("id") Long id) {
//        BrokerPlanContactDO planContact = planContactService.getPlanContact(id);
//        return success(BrokerPlanContactConvert.INSTANCE.convert(planContact));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得经纪人计划联系信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:query')")
//    public CommonResult<List<BrokerPlanContactRespVO>> getPlanContactList(@RequestParam("ids") Collection<Long> ids) {
//        List<BrokerPlanContactDO> list = planContactService.getPlanContactList(ids);
//        return success(BrokerPlanContactConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得经纪人计划联系信息分页")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:query')")
//    public CommonResult<PageResult<BrokerPlanContactRespVO>> getPlanContactPage(@Valid BrokerPlanContactPageReqVO pageVO) {
//        PageResult<BrokerPlanContactDO> pageResult = planContactService.getPlanContactPage(pageVO);
//        return success(BrokerPlanContactConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出经纪人计划联系信息 Excel")
//    @PreAuthorize("@ss.hasPermission('broker:plan-contact:export')")
//    @OperateLog(type = EXPORT)
//    public void exportPlanContactExcel(@Valid BrokerPlanContactExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<BrokerPlanContactDO> list = planContactService.getPlanContactList(exportReqVO);
//        // 导出 Excel
//        List<BrokerPlanContactExcelVO> datas = BrokerPlanContactConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "经纪人计划联系信息.xls", "数据", BrokerPlanContactExcelVO.class, datas);
//    }
//
//}
