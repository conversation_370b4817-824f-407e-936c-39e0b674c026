package com.yitong.octopus.module.broker.controller.app.channelaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 经纪人渠道账户信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppMemberUserChannelAccountRespVO extends AppMemberUserChannelAccountBaseVO {

    @Schema(description = "编号", required = true, example = "11988")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
