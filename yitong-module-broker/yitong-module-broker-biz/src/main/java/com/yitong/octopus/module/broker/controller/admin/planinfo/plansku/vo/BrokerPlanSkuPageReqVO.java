package com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划sku信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanSkuPageReqVO extends PageParam {

    @Schema(description = "计划ID", example = "25755")
    private Long planId;

    @Schema(description = "所属商家ID", example = "18634")
    private Long spId;

    @Schema(description = "商品SpuId", example = "25834")
    private Long spuId;

    @Schema(description = "商品SkuId", example = "13400")
    private Long skuId;

    @Schema(description = "cps分佣比例(小数点4位)")
    private Integer cps;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
