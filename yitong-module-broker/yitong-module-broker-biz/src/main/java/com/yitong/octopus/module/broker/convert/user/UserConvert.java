package com.yitong.octopus.module.broker.convert.user;

import com.yitong.octopus.module.broker.api.user.dto.MemberUserRespDTO;
import com.yitong.octopus.module.broker.controller.app.user.vo.AppUserInfoRespVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface UserConvert {

    UserConvert INSTANCE = Mappers.getMapper(UserConvert.class);

    AppUserInfoRespVO convert(BrokerMemberUserDO bean);

    MemberUserRespDTO convert2(BrokerMemberUserDO bean);

    List<MemberUserRespDTO> convertList2(List<BrokerMemberUserDO> list);

}
