package com.yitong.octopus.module.broker.dal.mysql.planstore;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStorePageReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 计划门店信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanStoreMapper extends BaseMapperX<BrokerPlanStoreDO> {

    default PageResult<BrokerPlanStoreDO> selectPage(BrokerPlanStorePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanStoreDO>()
                .eqIfPresent(BrokerPlanStoreDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanStoreDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanStoreDO::getStoreId, reqVO.getStoreId())
                .betweenIfPresent(BrokerPlanStoreDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanStoreDO::getId));
    }

    default List<BrokerPlanStoreDO> selectList(BrokerPlanStoreExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanStoreDO>()
                .eqIfPresent(BrokerPlanStoreDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanStoreDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanStoreDO::getStoreId, reqVO.getStoreId())
                .betweenIfPresent(BrokerPlanStoreDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanStoreDO::getId));
    }

}
