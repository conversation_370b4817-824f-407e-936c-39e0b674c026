package com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划合作细则更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanCoopDetailUpdateReqVO extends BrokerPlanCoopDetailBaseVO {

    @Schema(description = "编号", required = true, example = "3509")
    @NotNull(message = "编号不能为空")
    private Long id;

    @Schema(description = "计划ID", example = "21218")
    private Long planId;

}
