package com.yitong.octopus.module.broker.dal.dataobject.channelaccount;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人渠道账户信息 DO
 *
 * <AUTHOR>
 */
@TableName("broker_member_user_channel_account")
@KeySequence("broker_member_user_channel_account_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberUserChannelAccountDO extends BaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 渠道编码
     */
    private String channelCode;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 国家
     */
    private String country;
    /**
     * 省/直辖市
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 地区
     */
    private String area;
    /**
     * 备注
     */
    private String remark;
    /**
     * 平台ID
     */
    private String platformId;
    /**
     * 平台号
     */
    private String platformNo;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 平台地址
     */
    private String platformUrl;
    /**
     * 平台等级
     */
    private Integer platformLevel;
    /**
     * 平台带货分
     */
    private Integer platformThrScore;
    /**
     * 平台粉丝数
     */
    private Integer platformFansCount;
    /**
     * 平台粉丝级别
     */
    private Integer platformFansLevel;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 状态
     */
    private Boolean status;

}
