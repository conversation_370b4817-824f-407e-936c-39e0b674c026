package com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划sku信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanSkuRespVO extends BrokerPlanSkuBaseVO {

    @Schema(description = "编号", required = true, example = "16041")
    private Long id;

    @Schema(description = "计划ID", example = "25755")
    private Long planId;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
