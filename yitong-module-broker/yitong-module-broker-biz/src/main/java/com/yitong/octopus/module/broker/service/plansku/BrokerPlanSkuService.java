package com.yitong.octopus.module.broker.service.plansku;

import java.util.*;
import javax.validation.*;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;

/**
 * 经纪人计划sku信息 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerPlanSkuService {

    /**
     * 创建经纪人计划sku信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPlanSku(@Valid BrokerPlanSkuReqVO createReqVO);

    /**
     * 更新经纪人计划sku信息
     *
     * @param updateReqVO 更新信息
     */
    void updatePlanSku(@Valid BrokerPlanSkuReqVO updateReqVO);

    /**
     * 删除经纪人计划sku信息
     *
     * @param id 编号
     */
    void deletePlanSku(Long id);

    /**
     * 获得经纪人计划sku信息
     *
     * @param id 编号
     * @return 经纪人计划sku信息
     */
    BrokerPlanSkuDO getPlanSku(Long id);

    /**
     * 获得经纪人计划sku信息列表
     *
     * @param ids 编号
     * @return 经纪人计划sku信息列表
     */
    List<BrokerPlanSkuDO> getPlanSkuList(Collection<Long> ids);

    /**
     * 获得经纪人计划sku信息分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人计划sku信息分页
     */
    PageResult<BrokerPlanSkuDO> getPlanSkuPage(BrokerPlanSkuPageReqVO pageReqVO);

    /**
     * 获得经纪人计划sku信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人计划sku信息列表
     */
    List<BrokerPlanSkuDO> getPlanSkuList(BrokerPlanSkuExportReqVO exportReqVO);

    /**
     * 创建经纪人计划sku信息
     *
     * @param planId 计划Id
     * @param createReqVOList 创建信息
     * @return 编号
     */
    void createOrUpdatePlanSkuList(Long planId, List<BrokerPlanSkuReqVO> createReqVOList);

    /**
     * 删除经纪人计划sku信息
     *
     * @param planId 计划编号
     */
    void deletePlanSkuByPlanId(Long planId);

    /**
     * 获得经纪人计划sku信息列表
     *
     * @param planId 计划Id
     * @return 计划门店信息列表
     */
    List<BrokerPlanSkuDO> getPlanSkuListByPlanId(Long planId);
}
