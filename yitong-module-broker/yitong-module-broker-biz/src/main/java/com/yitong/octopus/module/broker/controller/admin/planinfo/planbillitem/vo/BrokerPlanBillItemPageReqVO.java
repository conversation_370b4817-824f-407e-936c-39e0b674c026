package com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划结算信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanBillItemPageReqVO extends PageParam {

    @Schema(description = "计划ID", example = "17116")
    private Long planId;

    @Schema(description = "所属商家ID", example = "14674")
    private Long spId;

    @Schema(description = "商品SpuId", example = "1579")
    private Long spuId;

    @Schema(description = "商品SkuId", example = "16681")
    private Long skuId;

    @Schema(description = "结算项ID", example = "28790")
    private Long billItemId;

    @Schema(description = "结算项值")
    private String billItemValue;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
