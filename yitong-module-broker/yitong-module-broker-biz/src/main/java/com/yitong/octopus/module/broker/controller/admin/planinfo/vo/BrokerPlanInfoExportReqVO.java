package com.yitong.octopus.module.broker.controller.admin.planinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 经纪人计划信息 Excel 导出 Request VO，参数和 BrokerPlanInfoPageReqVO 是一致的")
@Data
public class BrokerPlanInfoExportReqVO {

    @Schema(description = "计划名称", example = "芋艿")
    private String name;

    @Schema(description = "计划类型", example = "1")
    private Integer type;

    @Schema(description = "招募类型", example = "1")
    private Integer planType;

    @Schema(description = "所属商家ID", example = "11950")
    private Long spId;

    @Schema(description = "内容类型", example = "1")
    private Integer contentType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
