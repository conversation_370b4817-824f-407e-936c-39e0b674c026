package com.yitong.octopus.module.broker.controller.admin.planinfo.back;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.*;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import com.yitong.octopus.module.broker.convert.planbillitem.BrokerPlanBillItemConvert;
import com.yitong.octopus.module.broker.service.planbillitem.BrokerPlanBillItemService;

@Tag(name = "管理后台 - 经纪人计划结算信息")
@RestController
@RequestMapping("/broker/plan-bill-item")
@Validated
public class BrokerPlanBillItemController {

    @Resource
    private BrokerPlanBillItemService planBillItemService;

    @PostMapping("/create")
    @Operation(summary = "创建经纪人计划结算信息")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:create')")
    public CommonResult<Long> createPlanBillItem(@Valid @RequestBody BrokerPlanBillItemReqVO createReqVO) {
        return success(planBillItemService.createPlanBillItem(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新经纪人计划结算信息")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:update')")
    public CommonResult<Boolean> updatePlanBillItem(@Valid @RequestBody BrokerPlanBillItemReqVO updateReqVO) {
        planBillItemService.updatePlanBillItem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除经纪人计划结算信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:delete')")
    public CommonResult<Boolean> deletePlanBillItem(@RequestParam("id") Long id) {
        planBillItemService.deletePlanBillItem(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得经纪人计划结算信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:query')")
    public CommonResult<BrokerPlanBillItemRespVO> getPlanBillItem(@RequestParam("id") Long id) {
        BrokerPlanBillItemDO planBillItem = planBillItemService.getPlanBillItem(id);
        return success(BrokerPlanBillItemConvert.INSTANCE.convert(planBillItem));
    }

    @GetMapping("/list")
    @Operation(summary = "获得经纪人计划结算信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:query')")
    public CommonResult<List<BrokerPlanBillItemRespVO>> getPlanBillItemList(@RequestParam("ids") Collection<Long> ids) {
        List<BrokerPlanBillItemDO> list = planBillItemService.getPlanBillItemList(ids);
        return success(BrokerPlanBillItemConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得经纪人计划结算信息分页")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:query')")
    public CommonResult<PageResult<BrokerPlanBillItemRespVO>> getPlanBillItemPage(@Valid BrokerPlanBillItemPageReqVO pageVO) {
        PageResult<BrokerPlanBillItemDO> pageResult = planBillItemService.getPlanBillItemPage(pageVO);
        return success(BrokerPlanBillItemConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出经纪人计划结算信息 Excel")
    @PreAuthorize("@ss.hasPermission('broker:plan-bill-item:export')")
    @OperateLog(type = EXPORT)
    public void exportPlanBillItemExcel(@Valid BrokerPlanBillItemExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<BrokerPlanBillItemDO> list = planBillItemService.getPlanBillItemList(exportReqVO);
        // 导出 Excel
        List<BrokerPlanBillItemExcelVO> datas = BrokerPlanBillItemConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "经纪人计划结算信息.xls", "数据", BrokerPlanBillItemExcelVO.class, datas);
    }

}
