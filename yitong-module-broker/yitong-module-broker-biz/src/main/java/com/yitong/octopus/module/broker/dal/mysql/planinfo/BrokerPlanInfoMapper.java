package com.yitong.octopus.module.broker.dal.mysql.planinfo;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;

/**
 * 经纪人计划信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanInfoMapper extends BaseMapperX<BrokerPlanInfoDO> {

    default PageResult<BrokerPlanInfoDO> selectPage(BrokerPlanInfoPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanInfoDO>()
                .likeIfPresent(BrokerPlanInfoDO::getName, reqVO.getName())
                .eqIfPresent(BrokerPlanInfoDO::getType, reqVO.getType())
                .eqIfPresent(BrokerPlanInfoDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanInfoDO::getContentType, reqVO.getContentType())
                .betweenIfPresent(BrokerPlanInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanInfoDO::getId));
    }

    default List<BrokerPlanInfoDO> selectList(BrokerPlanInfoExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanInfoDO>()
                .likeIfPresent(BrokerPlanInfoDO::getName, reqVO.getName())
                .eqIfPresent(BrokerPlanInfoDO::getType, reqVO.getType())
                .eqIfPresent(BrokerPlanInfoDO::getSpId, reqVO.getSpId())
                .eqIfPresent(BrokerPlanInfoDO::getContentType, reqVO.getContentType())
                .betweenIfPresent(BrokerPlanInfoDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanInfoDO::getId));
    }

}
