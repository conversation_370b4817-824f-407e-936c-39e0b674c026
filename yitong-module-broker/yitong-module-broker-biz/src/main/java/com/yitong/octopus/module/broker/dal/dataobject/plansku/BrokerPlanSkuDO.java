package com.yitong.octopus.module.broker.dal.dataobject.plansku;

import com.yitong.octopus.framework.mybatis.core.dataobject.BaseCuDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 经纪人计划sku信息 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_sku")
@KeySequence("broker_plan_sku_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanSkuDO extends BaseCuDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 所属商家ID
     */
    private Long spId;
    /**
     * 商品SpuId
     */
    private Long spuId;
    /**
     * 商品SkuId
     */
    private Long skuId;
    /**
     * cps分佣比例(小数点4位)
     */
    private Integer cps;

}
