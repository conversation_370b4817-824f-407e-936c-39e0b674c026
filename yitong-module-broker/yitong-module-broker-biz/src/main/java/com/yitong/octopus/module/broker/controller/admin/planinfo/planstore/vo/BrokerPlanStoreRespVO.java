package com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 计划门店信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanStoreRespVO extends BrokerPlanStoreBaseVO {

    @Schema(description = "编号", required = true, example = "30250")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
