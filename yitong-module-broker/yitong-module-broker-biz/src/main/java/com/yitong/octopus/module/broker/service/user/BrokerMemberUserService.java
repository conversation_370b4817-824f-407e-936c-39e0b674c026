package com.yitong.octopus.module.broker.service.user;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.validation.Mobile;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserUpdateReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserPageReqVO;
import com.yitong.octopus.module.broker.controller.app.user.vo.AppUserUpdateMobileReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;

import javax.validation.Valid;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;

/**
 * 会员用户 Service 接口
 *
 * <AUTHOR>
 */
public interface BrokerMemberUserService {

    /**
     * 通过手机查询用户
     *
     * @param mobile 手机
     * @return 用户对象
     */
    BrokerMemberUserDO getUserByMobile(String mobile);

    /**
     * 基于用户昵称，模糊匹配用户列表
     *
     * @param nickname 用户昵称，模糊匹配
     * @return 用户信息的列表
     */
    List<BrokerMemberUserDO> getUserListByNickname(String nickname);

    /**
     * 基于手机号创建用户。
     * 如果用户已经存在，则直接进行返回
     *
     * @param mobile 手机号
     * @param registerIp 注册 IP
     * @return 用户对象
     */
    BrokerMemberUserDO createUserIfAbsent(@Mobile String mobile, String registerIp);

    /**
     * 更新用户的最后登陆信息
     *
     * @param id 用户编号
     * @param loginIp 登陆 IP
     */
    void updateUserLogin(Long id, String loginIp);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    BrokerMemberUserDO getUser(Long id);

    /**
     * 通过用户 ID 查询用户们
     *
     * @param ids 用户 ID
     * @return 用户对象信息数组
     */
    List<BrokerMemberUserDO> getUserList(Collection<Long> ids);

    /**
     * 修改用户昵称
     * @param userId 用户id
     * @param nickname 用户新昵称
     */
    void updateUserNickname(Long userId, String nickname);

    /**
     * 修改用户头像
     * @param userId 用户id
     * @param inputStream 头像文件
     * @return 头像url
     */
    String updateUserAvatar(Long userId, InputStream inputStream) throws Exception;

    /**
     * 修改手机
     * @param userId 用户id
     * @param reqVO 请求实体
     */
    void updateUserMobile(Long userId, AppUserUpdateMobileReqVO reqVO);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword 未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 创建经纪人用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMemberUser(@Valid BrokerMemberUserCreateReqVO createReqVO);

    /**
     * 更新经纪人用户
     *
     * @param updateReqVO 更新信息
     */
    void updateMemberUser(@Valid BrokerMemberUserUpdateReqVO updateReqVO);

    /**
     * 删除经纪人用户
     *
     * @param id 编号
     */
    void deleteMemberUser(Long id);

    /**
     * 获得经纪人用户
     *
     * @param id 编号
     * @return 经纪人用户
     */
    BrokerMemberUserDO getMemberUser(Long id);

    /**
     * 获得经纪人用户列表
     *
     * @param ids 编号
     * @return 经纪人用户列表
     */
    List<BrokerMemberUserDO> getMemberUserList(Collection<Long> ids);

    /**
     * 获得经纪人用户分页
     *
     * @param pageReqVO 分页查询
     * @return 经纪人用户分页
     */
    PageResult<BrokerMemberUserDO> getMemberUserPage(BrokerMemberUserPageReqVO pageReqVO);

    /**
     * 获得经纪人用户列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 经纪人用户列表
     */
    List<BrokerMemberUserDO> getMemberUserList(BrokerMemberUserExportReqVO exportReqVO);

    /**
     * 修改密码
     *
     * @param id       用户编号
     * @param password 密码
     */
    void updateUserPassword(Long id, String password);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatus(Long id, Integer status);
}
