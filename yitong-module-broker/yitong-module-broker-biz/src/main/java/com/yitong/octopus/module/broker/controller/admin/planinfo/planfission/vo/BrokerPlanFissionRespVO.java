package com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经纪人计划招募费用 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanFissionRespVO extends BrokerPlanFissionBaseVO {

    @Schema(description = "编号", required = true, example = "22177")
    private Long id;

    @Schema(description = "计划ID", example = "11378")
    private Long planId;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
