package com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 经纪人计划合作细则 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class BrokerPlanCoopDetailBaseVO {

    @Schema(description = "合作细节")
    private String cooperationInfo;

}
