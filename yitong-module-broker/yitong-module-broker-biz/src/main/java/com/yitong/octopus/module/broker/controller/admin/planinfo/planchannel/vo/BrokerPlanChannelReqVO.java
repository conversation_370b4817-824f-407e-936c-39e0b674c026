package com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 经纪人计划渠道信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BrokerPlanChannelReqVO extends BrokerPlanChannelBaseVO {

    @Schema(description = "编号", required = true, example = "20633")
    @NotNull(message = "编号不能为空")
    private Long id;

}
