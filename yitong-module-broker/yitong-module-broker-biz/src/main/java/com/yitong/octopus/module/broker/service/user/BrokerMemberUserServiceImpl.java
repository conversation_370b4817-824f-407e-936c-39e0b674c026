package com.yitong.octopus.module.broker.service.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.common.enums.CommonStatusEnum;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.broker.controller.admin.user.convert.memberuser.BrokerMemberUserConvert;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserUpdateReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import com.yitong.octopus.module.infra.api.file.FileApi;
import com.yitong.octopus.module.broker.controller.app.user.vo.AppUserUpdateMobileReqVO;
import com.yitong.octopus.module.broker.dal.mysql.user.BrokerMemberUserMapper;
import com.yitong.octopus.module.system.api.sms.SmsCodeApi;
import com.yitong.octopus.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import com.yitong.octopus.module.system.enums.sms.SmsSceneEnum;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.framework.common.util.servlet.ServletUtils.getClientIP;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.USER_NOT_EXISTS;

/**
 * 会员 User Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Valid
@Slf4j
public class BrokerMemberUserServiceImpl implements BrokerMemberUserService {

    @Resource
    private BrokerMemberUserMapper brokerMemberUserMapper;

    @Resource
    private FileApi fileApi;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Override
    public BrokerMemberUserDO getUserByMobile(String mobile) {
        return brokerMemberUserMapper.selectByMobile(mobile);
    }

    @Override
    public List<BrokerMemberUserDO> getUserListByNickname(String nickname) {
        return brokerMemberUserMapper.selectListByNicknameLike(nickname);
    }

    public BrokerMemberUserDO createUserWithPasswordIfAbsent(String mobile, String registerIp, String password) {
        // 用户已经存在
        BrokerMemberUserDO user = brokerMemberUserMapper.selectByMobile(mobile);
        if (user != null) {
            return user;
        }
        // 用户不存在，则进行创建
        return this.createUser(mobile, registerIp,password);
    }

    @Override
    public BrokerMemberUserDO createUserIfAbsent(String mobile, String registerIp) {
        return createUserWithPasswordIfAbsent(mobile,registerIp,null);
    }

    private BrokerMemberUserDO createUser(String mobile, String registerIp, String password ) {
        // 生成密码
        if (ObjectUtil.isEmpty(password)){
            password = IdUtil.fastSimpleUUID();
        }

        // 插入用户
        BrokerMemberUserDO user = new BrokerMemberUserDO();
        user.setPhone(mobile);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus());
        user.setPassword(encodePassword(password));
        user.setRegisterIp(registerIp);
        brokerMemberUserMapper.insert(user);
        return user;
    }

    @Override
    public void updateUserLogin(Long id, String loginIp) {
        brokerMemberUserMapper.updateById(new BrokerMemberUserDO().setId(id)
                .setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public BrokerMemberUserDO getUser(Long id) {
        return brokerMemberUserMapper.selectById(id);
    }

    @Override
    public List<BrokerMemberUserDO> getUserList(Collection<Long> ids) {
        return brokerMemberUserMapper.selectBatchIds(ids);
    }

    @Override
    public void updateUserNickname(Long userId, String nickname) {
        BrokerMemberUserDO user = this.checkUserExists(userId);
        // 仅当新昵称不等于旧昵称时进行修改
        if (nickname.equals(user.getNickName())){
            return;
        }
        BrokerMemberUserDO userDO = new BrokerMemberUserDO();
        userDO.setId(user.getId());
        userDO.setNickName(nickname);
        brokerMemberUserMapper.updateById(userDO);
    }

    @Override
    public String updateUserAvatar(Long userId, InputStream avatarFile) throws Exception {
        this.checkUserExists(userId);
        // 创建文件
        String avatar = fileApi.createFile(IoUtil.readBytes(avatarFile));
        // 更新头像路径
        brokerMemberUserMapper.updateById(BrokerMemberUserDO.builder().id(userId).avatar(avatar).build());
        return avatar;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserMobile(Long userId, AppUserUpdateMobileReqVO reqVO) {
        // 检测用户是否存在
        checkUserExists(userId);
        // 校验旧手机和旧验证码
        smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(reqVO.getOldMobile()).setCode(reqVO.getOldCode())
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));
        // 使用新验证码
        smsCodeApi.useSmsCode(new SmsCodeUseReqDTO().setMobile(reqVO.getPhone()).setCode(reqVO.getCode())
                .setScene(SmsSceneEnum.MEMBER_UPDATE_MOBILE.getScene()).setUsedIp(getClientIP()));

        // 更新用户手机
        brokerMemberUserMapper.updateById(BrokerMemberUserDO.builder().id(userId).phone(reqVO.getPhone()).build());
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public Long createMemberUser(BrokerMemberUserCreateReqVO createReqVO) {
        // 插入
        BrokerMemberUserDO memberUser = createUserIfAbsent(createReqVO.getPhone(),getClientIP());
        // 更新其他的属性
        BeanUtil.copyProperties(createReqVO,memberUser,"password");
        brokerMemberUserMapper.updateById(memberUser);
        // 返回
        return memberUser.getId();
    }

    @Override
    public void updateMemberUser(BrokerMemberUserUpdateReqVO updateReqVO) {
        // 校验存在
        validateMemberUserExists(updateReqVO.getId());
        // 更新
        BrokerMemberUserDO updateObj = BrokerMemberUserConvert.INSTANCE.convert(updateReqVO);
        brokerMemberUserMapper.updateById(updateObj);
    }

    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    @VisibleForTesting
    public BrokerMemberUserDO checkUserExists(Long id) {
        if (id == null) {
            return null;
        }
        BrokerMemberUserDO user = brokerMemberUserMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @Override
    public void deleteMemberUser(Long id) {
        // 校验存在
        validateMemberUserExists(id);
        // 删除
        brokerMemberUserMapper.deleteById(id);
    }

    private void validateMemberUserExists(Long id) {
        if (brokerMemberUserMapper.selectById(id) == null) {
            throw exception(USER_NOT_EXISTS);
        }
    }

    @Override
    public BrokerMemberUserDO getMemberUser(Long id) {
        return brokerMemberUserMapper.selectById(id);
    }

    @Override
    public List<BrokerMemberUserDO> getMemberUserList(Collection<Long> ids) {
        return brokerMemberUserMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerMemberUserDO> getMemberUserPage(BrokerMemberUserPageReqVO pageReqVO) {
        return brokerMemberUserMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerMemberUserDO> getMemberUserList(BrokerMemberUserExportReqVO exportReqVO) {
        return brokerMemberUserMapper.selectList(exportReqVO);
    }

    @Override
    public void updateUserPassword(Long id, String password) {
        // 校验用户存在
        validateMemberUserExists(id);
        // 更新密码
        BrokerMemberUserDO updateObj = new BrokerMemberUserDO();
        updateObj.setId(id);
        // 加密密码
        updateObj.setPassword(encodePassword(password));
        brokerMemberUserMapper.updateById(updateObj);
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateMemberUserExists(id);
        // 更新状态
        BrokerMemberUserDO updateObj = new BrokerMemberUserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        brokerMemberUserMapper.updateById(updateObj);
    }

}
