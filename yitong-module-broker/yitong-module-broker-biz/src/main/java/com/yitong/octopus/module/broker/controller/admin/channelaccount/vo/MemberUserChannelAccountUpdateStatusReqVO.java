package com.yitong.octopus.module.broker.controller.admin.channelaccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经纪人渠道账户更新状态 Request VO")
@Data
public class MemberUserChannelAccountUpdateStatusReqVO {

    @Schema(description = "用户编号", required = true, example = "1024")
    @NotNull(message = "用户编号不能为空")
    private Long id;

    @Schema(description = "状态,见 CommonStatusEnum 枚举", required = true, example = "false")
    @NotNull(message = "状态不能为空")
    private Boolean status;

}
