package com.yitong.octopus.module.broker.dal.dataobject.planchannel;

import com.yitong.octopus.framework.mybatis.core.dataobject.BaseCuDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 经纪人计划渠道信息 DO
 *
 * <AUTHOR>
 */
@TableName("broker_plan_channel")
@KeySequence("broker_plan_channel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BrokerPlanChannelDO extends BaseCuDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    /**
     * 计划ID
     */
    private Long planId;
    /**
     * 渠道Id
     */
    private Long channelId;
    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 经纪人带货能力要求
     */
    private Boolean talentLevelNeed;

    /**
     * 经纪人等级
     */
    private Integer talentLevel;

    /**
     * 经纪人能力要求
     */
    private Boolean talentMeritNeed;

    /**
     * 经纪人能力等级
     */
    private Integer talentMeritLevel;

    /**
     * 经纪人粉丝数要求
     */
    private Boolean talentFansNeed;

    /**
     * 经纪人粉丝数
     */
    private Integer talentFans;
}
