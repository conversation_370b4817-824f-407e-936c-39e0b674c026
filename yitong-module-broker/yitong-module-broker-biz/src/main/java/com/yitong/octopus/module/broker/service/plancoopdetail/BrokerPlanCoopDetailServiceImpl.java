package com.yitong.octopus.module.broker.service.plancoopdetail;

import cn.hutool.core.collection.CollectionUtil;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailUpdateReqVO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.broker.convert.plancoopdetail.BrokerPlanCoopDetailConvert;
import com.yitong.octopus.module.broker.dal.mysql.plancoopdetail.BrokerPlanCoopDetailMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;

/**
 * 经纪人计划合作细则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BrokerPlanCoopDetailServiceImpl implements BrokerPlanCoopDetailService {

    @Resource
    private BrokerPlanCoopDetailMapper planCoopDetailMapper;

    @Override
    public Long createPlanCoopDetail(BrokerPlanCoopDetailCreateReqVO createReqVO) {
        // 插入
        BrokerPlanCoopDetailDO planCoopDetail = BrokerPlanCoopDetailConvert.INSTANCE.convert(createReqVO);
        planCoopDetailMapper.insert(planCoopDetail);
        // 返回
        return planCoopDetail.getId();
    }

    @Override
    public void updatePlanCoopDetail(BrokerPlanCoopDetailUpdateReqVO updateReqVO) {
        // 校验存在
        validatePlanCoopDetailExists(updateReqVO.getId());
        // 更新
        BrokerPlanCoopDetailDO updateObj = BrokerPlanCoopDetailConvert.INSTANCE.convert(updateReqVO);
        planCoopDetailMapper.updateById(updateObj);
    }

    @Override
    public void deletePlanCoopDetail(Long id) {
        // 校验存在
        validatePlanCoopDetailExists(id);
        // 删除
        planCoopDetailMapper.deleteById(id);
    }

    private void validatePlanCoopDetailExists(Long id) {
        if (planCoopDetailMapper.selectById(id) == null) {
            throw exception(PLAN_COOP_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public BrokerPlanCoopDetailDO getPlanCoopDetail(Long id) {
        return planCoopDetailMapper.selectById(id);
    }

    @Override
    public List<BrokerPlanCoopDetailDO> getPlanCoopDetailList(Collection<Long> ids) {
        return planCoopDetailMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<BrokerPlanCoopDetailDO> getPlanCoopDetailPage(BrokerPlanCoopDetailPageReqVO pageReqVO) {
        return planCoopDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BrokerPlanCoopDetailDO> getPlanCoopDetailList(BrokerPlanCoopDetailExportReqVO exportReqVO) {
        return planCoopDetailMapper.selectList(exportReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createPlanCoopDetailList(List<BrokerPlanCoopDetailCreateReqVO> createReqVOList) {
        if (CollectionUtil.isNotEmpty(createReqVOList)){
            createReqVOList.forEach(this::createPlanCoopDetail);
        }
    }

}
