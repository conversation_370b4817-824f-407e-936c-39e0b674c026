package com.yitong.octopus.module.broker.dal.mysql.planbillinfocheck;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;

/**
 * 经纪人计划账单确认记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BrokerPlanBillInfoCheckMapper extends BaseMapperX<BrokerPlanBillInfoCheckDO> {

    default PageResult<BrokerPlanBillInfoCheckDO> selectPage(BrokerPlanBillInfoCheckPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<BrokerPlanBillInfoCheckDO>()
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getPlanBillId, reqVO.getPlanBillId())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getTalentId, reqVO.getTalentId())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getLastCheckTime, reqVO.getLastCheckTime())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getRealCheckTime, reqVO.getRealCheckTime())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getRefundMsg, reqVO.getRefundMsg())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getWorksUrl, reqVO.getWorksUrl())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanBillInfoCheckDO::getId));
    }

    default List<BrokerPlanBillInfoCheckDO> selectList(BrokerPlanBillInfoCheckExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BrokerPlanBillInfoCheckDO>()
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getPlanId, reqVO.getPlanId())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getPlanBillId, reqVO.getPlanBillId())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getTalentId, reqVO.getTalentId())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getLastCheckTime, reqVO.getLastCheckTime())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getRealCheckTime, reqVO.getRealCheckTime())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getStatus, reqVO.getStatus())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getRefundMsg, reqVO.getRefundMsg())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getWorksUrl, reqVO.getWorksUrl())
                .eqIfPresent(BrokerPlanBillInfoCheckDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(BrokerPlanBillInfoCheckDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(BrokerPlanBillInfoCheckDO::getId));
    }

}
