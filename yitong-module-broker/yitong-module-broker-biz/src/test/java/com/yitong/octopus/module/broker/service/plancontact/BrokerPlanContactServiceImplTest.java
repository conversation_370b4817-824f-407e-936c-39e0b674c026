package com.yitong.octopus.module.broker.service.plancontact;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancontact.vo.BrokerPlanContactReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.plancontact.BrokerPlanContactDO;
import com.yitong.octopus.module.broker.dal.mysql.plancontact.BrokerPlanContactMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanContactServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanContactServiceImpl.class)
public class BrokerPlanContactServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanContactServiceImpl planContactService;

    @Resource
    private BrokerPlanContactMapper planContactMapper;

    @Test
    public void testUpdatePlanContact_success() {
        // mock 数据
        BrokerPlanContactDO dbPlanContact = randomPojo(BrokerPlanContactDO.class);
        planContactMapper.insert(dbPlanContact);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanContactReqVO reqVO = randomPojo(BrokerPlanContactReqVO.class, o -> {
            o.setId(dbPlanContact.getId()); // 设置更新的 ID
        });

        // 调用
        planContactService.updatePlanContact(reqVO);
        // 校验是否更新正确
        BrokerPlanContactDO planContact = planContactMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planContact);
    }

    @Test
    public void testUpdatePlanContact_notExists() {
        // 准备参数
        BrokerPlanContactReqVO reqVO = randomPojo(BrokerPlanContactReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planContactService.updatePlanContact(reqVO), PLAN_CONTACT_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanContact_success() {
        // mock 数据
        BrokerPlanContactDO dbPlanContact = randomPojo(BrokerPlanContactDO.class);
        planContactMapper.insert(dbPlanContact);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanContact.getId();

        // 调用
        planContactService.deletePlanContact(id);
       // 校验数据不存在了
       assertNull(planContactMapper.selectById(id));
    }

    @Test
    public void testDeletePlanContact_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planContactService.deletePlanContact(id), PLAN_CONTACT_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanContactPage() {
       // mock 数据
       BrokerPlanContactDO dbPlanContact = randomPojo(BrokerPlanContactDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setType(null);
           o.setWidgetType(null);
           o.setContact(null);
           o.setCreateTime(null);
       });
       planContactMapper.insert(dbPlanContact);
       // 测试 planId 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setPlanId(null)));
       // 测试 type 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setType(null)));
       // 测试 widgetType 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setWidgetType(null)));
       // 测试 contact 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setContact(null)));
       // 测试 createTime 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanContactPageReqVO reqVO = new BrokerPlanContactPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setType(null);
       reqVO.setWidgetType(null);
       reqVO.setContact(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanContactDO> pageResult = planContactService.getPlanContactPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanContact, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanContactList() {
       // mock 数据
       BrokerPlanContactDO dbPlanContact = randomPojo(BrokerPlanContactDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setType(null);
           o.setWidgetType(null);
           o.setContact(null);
           o.setCreateTime(null);
       });
       planContactMapper.insert(dbPlanContact);
       // 测试 planId 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setPlanId(null)));
       // 测试 type 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setType(null)));
       // 测试 widgetType 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setWidgetType(null)));
       // 测试 contact 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setContact(null)));
       // 测试 createTime 不匹配
       planContactMapper.insert(cloneIgnoreId(dbPlanContact, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanContactExportReqVO reqVO = new BrokerPlanContactExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setType(null);
       reqVO.setWidgetType(null);
       reqVO.setContact(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanContactDO> list = planContactService.getPlanContactList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanContact, list.get(0));
    }

}
