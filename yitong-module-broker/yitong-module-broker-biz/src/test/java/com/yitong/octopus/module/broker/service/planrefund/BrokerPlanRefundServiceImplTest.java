package com.yitong.octopus.module.broker.service.planrefund;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.controller.admin.planrefund.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planrefund.BrokerPlanRefundDO;
import com.yitong.octopus.module.broker.dal.mysql.planrefund.BrokerPlanRefundMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static com.yitong.octopus.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link BrokerPlanRefundServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanRefundServiceImpl.class)
public class BrokerPlanRefundServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanRefundServiceImpl planRefundService;

    @Resource
    private BrokerPlanRefundMapper planRefundMapper;

    @Test
    public void testCreatePlanRefund_success() {
        // 准备参数
        BrokerPlanRefundCreateReqVO reqVO = randomPojo(BrokerPlanRefundCreateReqVO.class);

        // 调用
        Long planRefundId = planRefundService.createPlanRefund(reqVO);
        // 断言
        assertNotNull(planRefundId);
        // 校验记录的属性是否正确
        BrokerPlanRefundDO planRefund = planRefundMapper.selectById(planRefundId);
        assertPojoEquals(reqVO, planRefund);
    }

    @Test
    public void testUpdatePlanRefund_success() {
        // mock 数据
        BrokerPlanRefundDO dbPlanRefund = randomPojo(BrokerPlanRefundDO.class);
        planRefundMapper.insert(dbPlanRefund);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanRefundUpdateReqVO reqVO = randomPojo(BrokerPlanRefundUpdateReqVO.class, o -> {
            o.setId(dbPlanRefund.getId()); // 设置更新的 ID
        });

        // 调用
        planRefundService.updatePlanRefund(reqVO);
        // 校验是否更新正确
        BrokerPlanRefundDO planRefund = planRefundMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planRefund);
    }

    @Test
    public void testUpdatePlanRefund_notExists() {
        // 准备参数
        BrokerPlanRefundUpdateReqVO reqVO = randomPojo(BrokerPlanRefundUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planRefundService.updatePlanRefund(reqVO), PLAN_REFUND_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanRefund_success() {
        // mock 数据
        BrokerPlanRefundDO dbPlanRefund = randomPojo(BrokerPlanRefundDO.class);
        planRefundMapper.insert(dbPlanRefund);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanRefund.getId();

        // 调用
        planRefundService.deletePlanRefund(id);
       // 校验数据不存在了
       assertNull(planRefundMapper.selectById(id));
    }

    @Test
    public void testDeletePlanRefund_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planRefundService.deletePlanRefund(id), PLAN_REFUND_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanRefundPage() {
       // mock 数据
       BrokerPlanRefundDO dbPlanRefund = randomPojo(BrokerPlanRefundDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setStatus(null);
           o.setRefundMsg(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planRefundMapper.insert(dbPlanRefund);
       // 测试 planId 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setPlanId(null)));
       // 测试 status 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setStatus(null)));
       // 测试 refundMsg 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setRefundMsg(null)));
       // 测试 remark 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanRefundPageReqVO reqVO = new BrokerPlanRefundPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setStatus(null);
       reqVO.setRefundMsg(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanRefundDO> pageResult = planRefundService.getPlanRefundPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanRefund, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanRefundList() {
       // mock 数据
       BrokerPlanRefundDO dbPlanRefund = randomPojo(BrokerPlanRefundDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setStatus(null);
           o.setRefundMsg(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planRefundMapper.insert(dbPlanRefund);
       // 测试 planId 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setPlanId(null)));
       // 测试 status 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setStatus(null)));
       // 测试 refundMsg 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setRefundMsg(null)));
       // 测试 remark 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planRefundMapper.insert(cloneIgnoreId(dbPlanRefund, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanRefundExportReqVO reqVO = new BrokerPlanRefundExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setStatus(null);
       reqVO.setRefundMsg(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanRefundDO> list = planRefundService.getPlanRefundList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanRefund, list.get(0));
    }

}
