package com.yitong.octopus.module.broker.service.planbillinfocheck;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.controller.admin.planbillinfocheck.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planbillinfocheck.BrokerPlanBillInfoCheckDO;
import com.yitong.octopus.module.broker.dal.mysql.planbillinfocheck.BrokerPlanBillInfoCheckMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static com.yitong.octopus.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link BrokerPlanBillInfoCheckServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanBillInfoCheckServiceImpl.class)
public class BrokerPlanBillInfoCheckServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanBillInfoCheckServiceImpl planBillInfoCheckService;

    @Resource
    private BrokerPlanBillInfoCheckMapper planBillInfoCheckMapper;

    @Test
    public void testCreatePlanBillInfoCheck_success() {
        // 准备参数
        BrokerPlanBillInfoCheckCreateReqVO reqVO = randomPojo(BrokerPlanBillInfoCheckCreateReqVO.class);

        // 调用
        Long planBillInfoCheckId = planBillInfoCheckService.createPlanBillInfoCheck(reqVO);
        // 断言
        assertNotNull(planBillInfoCheckId);
        // 校验记录的属性是否正确
        BrokerPlanBillInfoCheckDO planBillInfoCheck = planBillInfoCheckMapper.selectById(planBillInfoCheckId);
        assertPojoEquals(reqVO, planBillInfoCheck);
    }

    @Test
    public void testUpdatePlanBillInfoCheck_success() {
        // mock 数据
        BrokerPlanBillInfoCheckDO dbPlanBillInfoCheck = randomPojo(BrokerPlanBillInfoCheckDO.class);
        planBillInfoCheckMapper.insert(dbPlanBillInfoCheck);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanBillInfoCheckUpdateReqVO reqVO = randomPojo(BrokerPlanBillInfoCheckUpdateReqVO.class, o -> {
            o.setId(dbPlanBillInfoCheck.getId()); // 设置更新的 ID
        });

        // 调用
        planBillInfoCheckService.updatePlanBillInfoCheck(reqVO);
        // 校验是否更新正确
        BrokerPlanBillInfoCheckDO planBillInfoCheck = planBillInfoCheckMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planBillInfoCheck);
    }

    @Test
    public void testUpdatePlanBillInfoCheck_notExists() {
        // 准备参数
        BrokerPlanBillInfoCheckUpdateReqVO reqVO = randomPojo(BrokerPlanBillInfoCheckUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planBillInfoCheckService.updatePlanBillInfoCheck(reqVO), PLAN_BILL_INFO_CHECK_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanBillInfoCheck_success() {
        // mock 数据
        BrokerPlanBillInfoCheckDO dbPlanBillInfoCheck = randomPojo(BrokerPlanBillInfoCheckDO.class);
        planBillInfoCheckMapper.insert(dbPlanBillInfoCheck);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanBillInfoCheck.getId();

        // 调用
        planBillInfoCheckService.deletePlanBillInfoCheck(id);
       // 校验数据不存在了
       assertNull(planBillInfoCheckMapper.selectById(id));
    }

    @Test
    public void testDeletePlanBillInfoCheck_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planBillInfoCheckService.deletePlanBillInfoCheck(id), PLAN_BILL_INFO_CHECK_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanBillInfoCheckPage() {
       // mock 数据
       BrokerPlanBillInfoCheckDO dbPlanBillInfoCheck = randomPojo(BrokerPlanBillInfoCheckDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setPlanBillId(null);
           o.setTalentId(null);
           o.setLastCheckTime(null);
           o.setRealCheckTime(null);
           o.setStatus(null);
           o.setRefundMsg(null);
           o.setWorksUrl(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planBillInfoCheckMapper.insert(dbPlanBillInfoCheck);
       // 测试 planId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setPlanId(null)));
       // 测试 planBillId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setPlanBillId(null)));
       // 测试 talentId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setTalentId(null)));
       // 测试 lastCheckTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setLastCheckTime(null)));
       // 测试 realCheckTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRealCheckTime(null)));
       // 测试 status 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setStatus(null)));
       // 测试 refundMsg 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRefundMsg(null)));
       // 测试 worksUrl 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setWorksUrl(null)));
       // 测试 remark 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanBillInfoCheckPageReqVO reqVO = new BrokerPlanBillInfoCheckPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setPlanBillId(null);
       reqVO.setTalentId(null);
       reqVO.setLastCheckTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setRealCheckTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setStatus(null);
       reqVO.setRefundMsg(null);
       reqVO.setWorksUrl(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanBillInfoCheckDO> pageResult = planBillInfoCheckService.getPlanBillInfoCheckPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanBillInfoCheck, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanBillInfoCheckList() {
       // mock 数据
       BrokerPlanBillInfoCheckDO dbPlanBillInfoCheck = randomPojo(BrokerPlanBillInfoCheckDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setPlanBillId(null);
           o.setTalentId(null);
           o.setLastCheckTime(null);
           o.setRealCheckTime(null);
           o.setStatus(null);
           o.setRefundMsg(null);
           o.setWorksUrl(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planBillInfoCheckMapper.insert(dbPlanBillInfoCheck);
       // 测试 planId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setPlanId(null)));
       // 测试 planBillId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setPlanBillId(null)));
       // 测试 talentId 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setTalentId(null)));
       // 测试 lastCheckTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setLastCheckTime(null)));
       // 测试 realCheckTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRealCheckTime(null)));
       // 测试 status 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setStatus(null)));
       // 测试 refundMsg 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRefundMsg(null)));
       // 测试 worksUrl 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setWorksUrl(null)));
       // 测试 remark 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planBillInfoCheckMapper.insert(cloneIgnoreId(dbPlanBillInfoCheck, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanBillInfoCheckExportReqVO reqVO = new BrokerPlanBillInfoCheckExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setPlanBillId(null);
       reqVO.setTalentId(null);
       reqVO.setLastCheckTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setRealCheckTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setStatus(null);
       reqVO.setRefundMsg(null);
       reqVO.setWorksUrl(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanBillInfoCheckDO> list = planBillInfoCheckService.getPlanBillInfoCheckList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanBillInfoCheck, list.get(0));
    }

}
