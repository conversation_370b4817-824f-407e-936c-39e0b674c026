package com.yitong.octopus.module.broker.service.planchannel;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planchannel.vo.BrokerPlanChannelReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.planchannel.BrokerPlanChannelDO;
import com.yitong.octopus.module.broker.dal.mysql.planchannel.BrokerPlanChannelMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanChannelServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanChannelServiceImpl.class)
public class BrokerPlanChannelServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanChannelServiceImpl planChannelService;

    @Resource
    private BrokerPlanChannelMapper planChannelMapper;

    @Test
    public void testUpdatePlanChannel_success() {
        // mock 数据
        BrokerPlanChannelDO dbPlanChannel = randomPojo(BrokerPlanChannelDO.class);
        planChannelMapper.insert(dbPlanChannel);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanChannelReqVO reqVO = randomPojo(BrokerPlanChannelReqVO.class, o -> {
            o.setId(dbPlanChannel.getId()); // 设置更新的 ID
        });

        // 调用
        planChannelService.updatePlanChannel(reqVO);
        // 校验是否更新正确
        BrokerPlanChannelDO planChannel = planChannelMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planChannel);
    }

    @Test
    public void testUpdatePlanChannel_notExists() {
        // 准备参数
        BrokerPlanChannelReqVO reqVO = randomPojo(BrokerPlanChannelReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planChannelService.updatePlanChannel(reqVO), PLAN_CHANNEL_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanChannel_success() {
        // mock 数据
        BrokerPlanChannelDO dbPlanChannel = randomPojo(BrokerPlanChannelDO.class);
        planChannelMapper.insert(dbPlanChannel);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanChannel.getId();

        // 调用
        planChannelService.deletePlanChannel(id);
       // 校验数据不存在了
       assertNull(planChannelMapper.selectById(id));
    }

    @Test
    public void testDeletePlanChannel_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planChannelService.deletePlanChannel(id), PLAN_CHANNEL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanChannelPage() {
       // mock 数据
       BrokerPlanChannelDO dbPlanChannel = randomPojo(BrokerPlanChannelDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setChannelId(null);
           o.setChannelCode(null);
           o.setCreateTime(null);
       });
       planChannelMapper.insert(dbPlanChannel);
       // 测试 planId 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setPlanId(null)));
       // 测试 channelId 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setChannelId(null)));
       // 测试 channelCode 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setChannelCode(null)));
       // 测试 createTime 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanChannelPageReqVO reqVO = new BrokerPlanChannelPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setChannelId(null);
       reqVO.setChannelCode(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanChannelDO> pageResult = planChannelService.getPlanChannelPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanChannel, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanChannelList() {
       // mock 数据
       BrokerPlanChannelDO dbPlanChannel = randomPojo(BrokerPlanChannelDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setChannelId(null);
           o.setChannelCode(null);
           o.setCreateTime(null);
       });
       planChannelMapper.insert(dbPlanChannel);
       // 测试 planId 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setPlanId(null)));
       // 测试 channelId 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setChannelId(null)));
       // 测试 channelCode 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setChannelCode(null)));
       // 测试 createTime 不匹配
       planChannelMapper.insert(cloneIgnoreId(dbPlanChannel, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanChannelExportReqVO reqVO = new BrokerPlanChannelExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setChannelId(null);
       reqVO.setChannelCode(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanChannelDO> list = planChannelService.getPlanChannelList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanChannel, list.get(0));
    }

}
