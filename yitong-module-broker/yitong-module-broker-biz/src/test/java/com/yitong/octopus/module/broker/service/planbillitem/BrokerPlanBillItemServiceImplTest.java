package com.yitong.octopus.module.broker.service.planbillitem;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planbillitem.vo.BrokerPlanBillItemReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.planbillitem.BrokerPlanBillItemDO;
import com.yitong.octopus.module.broker.dal.mysql.planbillitem.BrokerPlanBillItemMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.PLAN_BILL_ITEM_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanBillItemServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanBillItemServiceImpl.class)
public class BrokerPlanBillItemServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanBillItemServiceImpl planBillItemService;

    @Resource
    private BrokerPlanBillItemMapper planBillItemMapper;

    @Test
    public void testUpdatePlanBillItem_success() {
        // mock 数据
        BrokerPlanBillItemDO dbPlanBillItem = randomPojo(BrokerPlanBillItemDO.class);
        planBillItemMapper.insert(dbPlanBillItem);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanBillItemReqVO reqVO = randomPojo(BrokerPlanBillItemReqVO.class, o -> {
            o.setId(dbPlanBillItem.getId()); // 设置更新的 ID
        });

        // 调用
        planBillItemService.updatePlanBillItem(reqVO);
        // 校验是否更新正确
        BrokerPlanBillItemDO planBillItem = planBillItemMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planBillItem);
    }

    @Test
    public void testUpdatePlanBillItem_notExists() {
        // 准备参数
        BrokerPlanBillItemReqVO reqVO = randomPojo(BrokerPlanBillItemReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planBillItemService.updatePlanBillItem(reqVO), PLAN_BILL_ITEM_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanBillItem_success() {
        // mock 数据
        BrokerPlanBillItemDO dbPlanBillItem = randomPojo(BrokerPlanBillItemDO.class);
        planBillItemMapper.insert(dbPlanBillItem);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanBillItem.getId();

        // 调用
        planBillItemService.deletePlanBillItem(id);
       // 校验数据不存在了
       assertNull(planBillItemMapper.selectById(id));
    }

    @Test
    public void testDeletePlanBillItem_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planBillItemService.deletePlanBillItem(id), PLAN_BILL_ITEM_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanBillItemPage() {
       // mock 数据
       BrokerPlanBillItemDO dbPlanBillItem = randomPojo(BrokerPlanBillItemDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setSpuId(null);
           o.setSkuId(null);
           o.setBillItemId(null);
           o.setBillItemValue(null);
           o.setCreateTime(null);
       });
       planBillItemMapper.insert(dbPlanBillItem);
       // 测试 planId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSpId(null)));
       // 测试 spuId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSpuId(null)));
       // 测试 skuId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSkuId(null)));
       // 测试 billItemId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setBillItemId(null)));
       // 测试 billItemValue 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setBillItemValue(null)));
       // 测试 createTime 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanBillItemPageReqVO reqVO = new BrokerPlanBillItemPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setSpuId(null);
       reqVO.setSkuId(null);
       reqVO.setBillItemId(null);
       reqVO.setBillItemValue(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanBillItemDO> pageResult = planBillItemService.getPlanBillItemPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanBillItem, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanBillItemList() {
       // mock 数据
       BrokerPlanBillItemDO dbPlanBillItem = randomPojo(BrokerPlanBillItemDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setSpuId(null);
           o.setSkuId(null);
           o.setBillItemId(null);
           o.setBillItemValue(null);
           o.setCreateTime(null);
       });
       planBillItemMapper.insert(dbPlanBillItem);
       // 测试 planId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSpId(null)));
       // 测试 spuId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSpuId(null)));
       // 测试 skuId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setSkuId(null)));
       // 测试 billItemId 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setBillItemId(null)));
       // 测试 billItemValue 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setBillItemValue(null)));
       // 测试 createTime 不匹配
       planBillItemMapper.insert(cloneIgnoreId(dbPlanBillItem, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanBillItemExportReqVO reqVO = new BrokerPlanBillItemExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setSpuId(null);
       reqVO.setSkuId(null);
       reqVO.setBillItemId(null);
       reqVO.setBillItemValue(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanBillItemDO> list = planBillItemService.getPlanBillItemList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanBillItem, list.get(0));
    }

}
