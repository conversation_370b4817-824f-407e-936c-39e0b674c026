package com.yitong.octopus.module.broker.service.planstore;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStorePageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planstore.vo.BrokerPlanStoreReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.planstore.BrokerPlanStoreDO;
import com.yitong.octopus.module.broker.dal.mysql.planstore.BrokerPlanStoreMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanStoreServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanStoreServiceImpl.class)
public class BrokerPlanStoreServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanStoreServiceImpl planStoreService;

    @Resource
    private BrokerPlanStoreMapper planStoreMapper;

    @Test
    public void testUpdatePlanStore_success() {
        // mock 数据
        BrokerPlanStoreDO dbPlanStore = randomPojo(BrokerPlanStoreDO.class);
        planStoreMapper.insert(dbPlanStore);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanStoreReqVO reqVO = randomPojo(BrokerPlanStoreReqVO.class, o -> {
            o.setId(dbPlanStore.getId()); // 设置更新的 ID
        });

        // 调用
        planStoreService.updatePlanStore(reqVO);
        // 校验是否更新正确
        BrokerPlanStoreDO planStore = planStoreMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planStore);
    }

    @Test
    public void testUpdatePlanStore_notExists() {
        // 准备参数
        BrokerPlanStoreReqVO reqVO = randomPojo(BrokerPlanStoreReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planStoreService.updatePlanStore(reqVO), PLAN_STORE_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanStore_success() {
        // mock 数据
        BrokerPlanStoreDO dbPlanStore = randomPojo(BrokerPlanStoreDO.class);
        planStoreMapper.insert(dbPlanStore);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanStore.getId();

        // 调用
        planStoreService.deletePlanStore(id);
       // 校验数据不存在了
       assertNull(planStoreMapper.selectById(id));
    }

    @Test
    public void testDeletePlanStore_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planStoreService.deletePlanStore(id), PLAN_STORE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanStorePage() {
       // mock 数据
       BrokerPlanStoreDO dbPlanStore = randomPojo(BrokerPlanStoreDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setStoreId(null);
           o.setCreateTime(null);
       });
       planStoreMapper.insert(dbPlanStore);
       // 测试 planId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setSpId(null)));
       // 测试 storeId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setStoreId(null)));
       // 测试 createTime 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanStorePageReqVO reqVO = new BrokerPlanStorePageReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setStoreId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanStoreDO> pageResult = planStoreService.getPlanStorePage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanStore, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanStoreList() {
       // mock 数据
       BrokerPlanStoreDO dbPlanStore = randomPojo(BrokerPlanStoreDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setStoreId(null);
           o.setCreateTime(null);
       });
       planStoreMapper.insert(dbPlanStore);
       // 测试 planId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setSpId(null)));
       // 测试 storeId 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setStoreId(null)));
       // 测试 createTime 不匹配
       planStoreMapper.insert(cloneIgnoreId(dbPlanStore, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanStoreExportReqVO reqVO = new BrokerPlanStoreExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setStoreId(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanStoreDO> list = planStoreService.getPlanStoreList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanStore, list.get(0));
    }

}
