package com.yitong.octopus.module.broker.service.plansku;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plansku.vo.BrokerPlanSkuReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.plansku.BrokerPlanSkuDO;
import com.yitong.octopus.module.broker.dal.mysql.plansku.BrokerPlanSkuMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanSkuServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanSkuServiceImpl.class)
public class BrokerPlanSkuServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanSkuServiceImpl planSkuService;

    @Resource
    private BrokerPlanSkuMapper planSkuMapper;

    @Test
    public void testUpdatePlanSku_success() {
        // mock 数据
        BrokerPlanSkuDO dbPlanSku = randomPojo(BrokerPlanSkuDO.class);
        planSkuMapper.insert(dbPlanSku);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanSkuReqVO reqVO = randomPojo(BrokerPlanSkuReqVO.class, o -> {
            o.setId(dbPlanSku.getId()); // 设置更新的 ID
        });

        // 调用
        planSkuService.updatePlanSku(reqVO);
        // 校验是否更新正确
        BrokerPlanSkuDO planSku = planSkuMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planSku);
    }

    @Test
    public void testUpdatePlanSku_notExists() {
        // 准备参数
        BrokerPlanSkuReqVO reqVO = randomPojo(BrokerPlanSkuReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planSkuService.updatePlanSku(reqVO), PLAN_SKU_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanSku_success() {
        // mock 数据
        BrokerPlanSkuDO dbPlanSku = randomPojo(BrokerPlanSkuDO.class);
        planSkuMapper.insert(dbPlanSku);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanSku.getId();

        // 调用
        planSkuService.deletePlanSku(id);
       // 校验数据不存在了
       assertNull(planSkuMapper.selectById(id));
    }

    @Test
    public void testDeletePlanSku_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planSkuService.deletePlanSku(id), PLAN_SKU_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanSkuPage() {
       // mock 数据
       BrokerPlanSkuDO dbPlanSku = randomPojo(BrokerPlanSkuDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setSpuId(null);
           o.setSkuId(null);
           o.setCps(null);
           o.setCreateTime(null);
       });
       planSkuMapper.insert(dbPlanSku);
       // 测试 planId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSpId(null)));
       // 测试 spuId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSpuId(null)));
       // 测试 skuId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSkuId(null)));
       // 测试 cps 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setCps(null)));
       // 测试 createTime 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanSkuPageReqVO reqVO = new BrokerPlanSkuPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setSpuId(null);
       reqVO.setSkuId(null);
       reqVO.setCps(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanSkuDO> pageResult = planSkuService.getPlanSkuPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanSku, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanSkuList() {
       // mock 数据
       BrokerPlanSkuDO dbPlanSku = randomPojo(BrokerPlanSkuDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setSpId(null);
           o.setSpuId(null);
           o.setSkuId(null);
           o.setCps(null);
           o.setCreateTime(null);
       });
       planSkuMapper.insert(dbPlanSku);
       // 测试 planId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setPlanId(null)));
       // 测试 spId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSpId(null)));
       // 测试 spuId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSpuId(null)));
       // 测试 skuId 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setSkuId(null)));
       // 测试 cps 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setCps(null)));
       // 测试 createTime 不匹配
       planSkuMapper.insert(cloneIgnoreId(dbPlanSku, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanSkuExportReqVO reqVO = new BrokerPlanSkuExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setSpId(null);
       reqVO.setSpuId(null);
       reqVO.setSkuId(null);
       reqVO.setCps(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanSkuDO> list = planSkuService.getPlanSkuList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanSku, list.get(0));
    }

}
