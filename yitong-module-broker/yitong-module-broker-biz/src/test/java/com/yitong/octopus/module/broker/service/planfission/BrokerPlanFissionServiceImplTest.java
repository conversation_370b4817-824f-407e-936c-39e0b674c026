package com.yitong.octopus.module.broker.service.planfission;

import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.planfission.vo.BrokerPlanFissionReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.planfission.BrokerPlanFissionDO;
import com.yitong.octopus.module.broker.dal.mysql.planfission.BrokerPlanFissionMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanFissionServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanFissionServiceImpl.class)
public class BrokerPlanFissionServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanFissionServiceImpl planFissionService;

    @Resource
    private BrokerPlanFissionMapper planFissionMapper;

    @Test
    public void testUpdatePlanFission_success() {
        // mock 数据
        BrokerPlanFissionDO dbPlanFission = randomPojo(BrokerPlanFissionDO.class);
        planFissionMapper.insert(dbPlanFission);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanFissionReqVO reqVO = randomPojo(BrokerPlanFissionReqVO.class, o -> {
            o.setId(dbPlanFission.getId()); // 设置更新的 ID
        });

        // 调用
        planFissionService.updatePlanFission(reqVO);
        // 校验是否更新正确
        BrokerPlanFissionDO planFission = planFissionMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planFission);
    }

    @Test
    public void testUpdatePlanFission_notExists() {
        // 准备参数
        BrokerPlanFissionReqVO reqVO = randomPojo(BrokerPlanFissionReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planFissionService.updatePlanFission(reqVO), PLAN_FISSION_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanFission_success() {
        // mock 数据
        BrokerPlanFissionDO dbPlanFission = randomPojo(BrokerPlanFissionDO.class);
        planFissionMapper.insert(dbPlanFission);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanFission.getId();

        // 调用
        planFissionService.deletePlanFission(id);
       // 校验数据不存在了
       assertNull(planFissionMapper.selectById(id));
    }

    @Test
    public void testDeletePlanFission_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planFissionService.deletePlanFission(id), PLAN_FISSION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanFissionPage() {
       // mock 数据
       BrokerPlanFissionDO dbPlanFission = randomPojo(BrokerPlanFissionDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setTalentLevel(null);
           o.setAmount(null);
           o.setCreateTime(null);
       });
       planFissionMapper.insert(dbPlanFission);
       // 测试 planId 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setPlanId(null)));
       // 测试 talentLevel 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setTalentLevel(null)));
       // 测试 amount 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setAmount(null)));
       // 测试 createTime 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanFissionPageReqVO reqVO = new BrokerPlanFissionPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setTalentLevel(null);
       reqVO.setAmount(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanFissionDO> pageResult = planFissionService.getPlanFissionPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanFission, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanFissionList() {
       // mock 数据
       BrokerPlanFissionDO dbPlanFission = randomPojo(BrokerPlanFissionDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setTalentLevel(null);
           o.setAmount(null);
           o.setCreateTime(null);
       });
       planFissionMapper.insert(dbPlanFission);
       // 测试 planId 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setPlanId(null)));
       // 测试 talentLevel 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setTalentLevel(null)));
       // 测试 amount 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setAmount(null)));
       // 测试 createTime 不匹配
       planFissionMapper.insert(cloneIgnoreId(dbPlanFission, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanFissionExportReqVO reqVO = new BrokerPlanFissionExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setTalentLevel(null);
       reqVO.setAmount(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanFissionDO> list = planFissionService.getPlanFissionList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanFission, list.get(0));
    }

}
