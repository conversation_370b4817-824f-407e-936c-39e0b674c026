package com.yitong.octopus.module.broker.service.plancoopdetail;

import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.planinfo.plancoopdetail.vo.BrokerPlanCoopDetailUpdateReqVO;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.dal.dataobject.plancoopdetail.BrokerPlanCoopDetailDO;
import com.yitong.octopus.module.broker.dal.mysql.plancoopdetail.BrokerPlanCoopDetailMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanCoopDetailServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanCoopDetailServiceImpl.class)
public class BrokerPlanCoopDetailServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanCoopDetailServiceImpl planCoopDetailService;

    @Resource
    private BrokerPlanCoopDetailMapper planCoopDetailMapper;

    @Test
    public void testCreatePlanCoopDetail_success() {
        // 准备参数
        BrokerPlanCoopDetailCreateReqVO reqVO = randomPojo(BrokerPlanCoopDetailCreateReqVO.class);

        // 调用
        Long planCoopDetailId = planCoopDetailService.createPlanCoopDetail(reqVO);
        // 断言
        assertNotNull(planCoopDetailId);
        // 校验记录的属性是否正确
        BrokerPlanCoopDetailDO planCoopDetail = planCoopDetailMapper.selectById(planCoopDetailId);
        assertPojoEquals(reqVO, planCoopDetail);
    }

    @Test
    public void testUpdatePlanCoopDetail_success() {
        // mock 数据
        BrokerPlanCoopDetailDO dbPlanCoopDetail = randomPojo(BrokerPlanCoopDetailDO.class);
        planCoopDetailMapper.insert(dbPlanCoopDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanCoopDetailUpdateReqVO reqVO = randomPojo(BrokerPlanCoopDetailUpdateReqVO.class, o -> {
            o.setId(dbPlanCoopDetail.getId()); // 设置更新的 ID
        });

        // 调用
        planCoopDetailService.updatePlanCoopDetail(reqVO);
        // 校验是否更新正确
        BrokerPlanCoopDetailDO planCoopDetail = planCoopDetailMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planCoopDetail);
    }

    @Test
    public void testUpdatePlanCoopDetail_notExists() {
        // 准备参数
        BrokerPlanCoopDetailUpdateReqVO reqVO = randomPojo(BrokerPlanCoopDetailUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planCoopDetailService.updatePlanCoopDetail(reqVO), PLAN_COOP_DETAIL_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanCoopDetail_success() {
        // mock 数据
        BrokerPlanCoopDetailDO dbPlanCoopDetail = randomPojo(BrokerPlanCoopDetailDO.class);
        planCoopDetailMapper.insert(dbPlanCoopDetail);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanCoopDetail.getId();

        // 调用
        planCoopDetailService.deletePlanCoopDetail(id);
       // 校验数据不存在了
       assertNull(planCoopDetailMapper.selectById(id));
    }

    @Test
    public void testDeletePlanCoopDetail_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planCoopDetailService.deletePlanCoopDetail(id), PLAN_COOP_DETAIL_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanCoopDetailPage() {
       // mock 数据
       BrokerPlanCoopDetailDO dbPlanCoopDetail = randomPojo(BrokerPlanCoopDetailDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setCooperationInfo(null);
           o.setCreateTime(null);
       });
       planCoopDetailMapper.insert(dbPlanCoopDetail);
       // 测试 planId 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setPlanId(null)));
       // 测试 cooperationInfo 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setCooperationInfo(null)));
       // 测试 createTime 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanCoopDetailPageReqVO reqVO = new BrokerPlanCoopDetailPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setCooperationInfo(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanCoopDetailDO> pageResult = planCoopDetailService.getPlanCoopDetailPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanCoopDetail, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanCoopDetailList() {
       // mock 数据
       BrokerPlanCoopDetailDO dbPlanCoopDetail = randomPojo(BrokerPlanCoopDetailDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setCooperationInfo(null);
           o.setCreateTime(null);
       });
       planCoopDetailMapper.insert(dbPlanCoopDetail);
       // 测试 planId 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setPlanId(null)));
       // 测试 cooperationInfo 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setCooperationInfo(null)));
       // 测试 createTime 不匹配
       planCoopDetailMapper.insert(cloneIgnoreId(dbPlanCoopDetail, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanCoopDetailExportReqVO reqVO = new BrokerPlanCoopDetailExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setCooperationInfo(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanCoopDetailDO> list = planCoopDetailService.getPlanCoopDetailList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanCoopDetail, list.get(0));
    }

}
