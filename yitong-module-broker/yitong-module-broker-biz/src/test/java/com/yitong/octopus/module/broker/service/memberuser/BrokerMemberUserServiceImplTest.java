package com.yitong.octopus.module.broker.service.memberuser;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserCreateReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserExportReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserPageReqVO;
import com.yitong.octopus.module.broker.controller.admin.user.vo.BrokerMemberUserUpdateReqVO;
import com.yitong.octopus.module.broker.dal.dataobject.user.BrokerMemberUserDO;
import com.yitong.octopus.module.broker.dal.mysql.user.BrokerMemberUserMapper;
import com.yitong.octopus.module.broker.service.user.BrokerMemberUserServiceImpl;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;


import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerMemberUserServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerMemberUserServiceImpl.class)
public class BrokerMemberUserServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerMemberUserServiceImpl memberUserService;

    @Resource
    private BrokerMemberUserMapper memberUserMapper;

    @Test
    public void testCreateMemberUser_success() {
        // 准备参数
        BrokerMemberUserCreateReqVO reqVO = randomPojo(BrokerMemberUserCreateReqVO.class);

        // 调用
        Long memberUserId = memberUserService.createMemberUser(reqVO);
        // 断言
        assertNotNull(memberUserId);
        // 校验记录的属性是否正确
        BrokerMemberUserDO memberUser = memberUserMapper.selectById(memberUserId);
        assertPojoEquals(reqVO, memberUser);
    }

    @Test
    public void testUpdateMemberUser_success() {
        // mock 数据
        BrokerMemberUserDO dbMemberUser = randomPojo(BrokerMemberUserDO.class);
        memberUserMapper.insert(dbMemberUser);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerMemberUserUpdateReqVO reqVO = randomPojo(BrokerMemberUserUpdateReqVO.class, o -> {
            o.setId(dbMemberUser.getId()); // 设置更新的 ID
        });

        // 调用
        memberUserService.updateMemberUser(reqVO);
        // 校验是否更新正确
        BrokerMemberUserDO memberUser = memberUserMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, memberUser);
    }

    @Test
    public void testUpdateMemberUser_notExists() {
        // 准备参数
        BrokerMemberUserUpdateReqVO reqVO = randomPojo(BrokerMemberUserUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> memberUserService.updateMemberUser(reqVO), USER_NOT_EXISTS);
    }

    @Test
    public void testDeleteMemberUser_success() {
        // mock 数据
        BrokerMemberUserDO dbMemberUser = randomPojo(BrokerMemberUserDO.class);
        memberUserMapper.insert(dbMemberUser);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbMemberUser.getId();

        // 调用
        memberUserService.deleteMemberUser(id);
       // 校验数据不存在了
       assertNull(memberUserMapper.selectById(id));
    }

    @Test
    public void testDeleteMemberUser_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> memberUserService.deleteMemberUser(id), USER_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMemberUserPage() {
       // mock 数据
       BrokerMemberUserDO dbMemberUser = randomPojo(BrokerMemberUserDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setPhone(null);
           o.setSex(null);
           o.setAvatar(null);
           o.setNickName(null);
           o.setCountry(null);
           o.setProvince(null);
           o.setCity(null);
           o.setArea(null);
           o.setAddress(null);
           o.setStatus(null);
           o.setPassword(null);
           o.setRegisterIp(null);
           o.setLoginIp(null);
           o.setLoginDate(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       memberUserMapper.insert(dbMemberUser);
       // 测试 name 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setName(null)));
       // 测试 phone 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setPhone(null)));
       // 测试 sex 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setSex(null)));
       // 测试 avatar 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setAvatar(null)));
       // 测试 nickName 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setNickName(null)));
       // 测试 country 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCountry(null)));
       // 测试 province 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setProvince(null)));
       // 测试 city 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCity(null)));
       // 测试 area 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setArea(null)));
       // 测试 address 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setAddress(null)));
       // 测试 status 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setStatus(null)));
       // 测试 password 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setPassword(null)));
       // 测试 registerIp 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setRegisterIp(null)));
       // 测试 loginIp 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setLoginIp(null)));
       // 测试 loginDate 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setLoginDate(null)));
       // 测试 remark 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerMemberUserPageReqVO reqVO = new BrokerMemberUserPageReqVO();
       reqVO.setName(null);
       reqVO.setPhone(null);
       reqVO.setSex(null);
       reqVO.setAvatar(null);
       reqVO.setNickName(null);
       reqVO.setCountry(null);
       reqVO.setProvince(null);
       reqVO.setCity(null);
       reqVO.setArea(null);
       reqVO.setAddress(null);
       reqVO.setStatus(null);
       reqVO.setPassword(null);
       reqVO.setRegisterIp(null);
       reqVO.setLoginIp(null);
       reqVO.setLoginDate(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerMemberUserDO> pageResult = memberUserService.getMemberUserPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbMemberUser, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMemberUserList() {
       // mock 数据
       BrokerMemberUserDO dbMemberUser = randomPojo(BrokerMemberUserDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setPhone(null);
           o.setSex(null);
           o.setAvatar(null);
           o.setNickName(null);
           o.setCountry(null);
           o.setProvince(null);
           o.setCity(null);
           o.setArea(null);
           o.setAddress(null);
           o.setStatus(null);
           o.setPassword(null);
           o.setRegisterIp(null);
           o.setLoginIp(null);
           o.setLoginDate(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       memberUserMapper.insert(dbMemberUser);
       // 测试 name 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setName(null)));
       // 测试 phone 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setPhone(null)));
       // 测试 sex 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setSex(null)));
       // 测试 avatar 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setAvatar(null)));
       // 测试 nickName 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setNickName(null)));
       // 测试 country 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCountry(null)));
       // 测试 province 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setProvince(null)));
       // 测试 city 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCity(null)));
       // 测试 area 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setArea(null)));
       // 测试 address 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setAddress(null)));
       // 测试 status 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setStatus(null)));
       // 测试 password 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setPassword(null)));
       // 测试 registerIp 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setRegisterIp(null)));
       // 测试 loginIp 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setLoginIp(null)));
       // 测试 loginDate 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setLoginDate(null)));
       // 测试 remark 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       memberUserMapper.insert(cloneIgnoreId(dbMemberUser, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerMemberUserExportReqVO reqVO = new BrokerMemberUserExportReqVO();
       reqVO.setName(null);
       reqVO.setPhone(null);
       reqVO.setSex(null);
       reqVO.setAvatar(null);
       reqVO.setNickName(null);
       reqVO.setCountry(null);
       reqVO.setProvince(null);
       reqVO.setCity(null);
       reqVO.setArea(null);
       reqVO.setAddress(null);
       reqVO.setStatus(null);
       reqVO.setPassword(null);
       reqVO.setRegisterIp(null);
       reqVO.setLoginIp(null);
       reqVO.setLoginDate(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerMemberUserDO> list = memberUserService.getMemberUserList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbMemberUser, list.get(0));
    }

}
