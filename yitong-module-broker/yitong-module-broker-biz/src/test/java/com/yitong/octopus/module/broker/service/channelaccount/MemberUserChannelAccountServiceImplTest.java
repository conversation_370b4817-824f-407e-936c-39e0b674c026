package com.yitong.octopus.module.broker.service.channelaccount;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.controller.admin.channelaccount.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.channelaccount.MemberUserChannelAccountDO;
import com.yitong.octopus.module.broker.dal.mysql.memberuserchannelaccount.MemberUserChannelAccountMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link MemberUserChannelAccountServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(MemberUserChannelAccountServiceImpl.class)
public class MemberUserChannelAccountServiceImplTest extends BaseDbUnitTest {

    @Resource
    private MemberUserChannelAccountServiceImpl memberUserChannelAccountService;

    @Resource
    private MemberUserChannelAccountMapper memberUserChannelAccountMapper;

    @Test
    public void testCreateMemberUserChannelAccount_success() {
        // 准备参数
        MemberUserChannelAccountCreateReqVO reqVO = randomPojo(MemberUserChannelAccountCreateReqVO.class);

        // 调用
        Long memberUserChannelAccountId = memberUserChannelAccountService.createMemberUserChannelAccount(reqVO);
        // 断言
        assertNotNull(memberUserChannelAccountId);
        // 校验记录的属性是否正确
        MemberUserChannelAccountDO memberUserChannelAccount = memberUserChannelAccountMapper.selectById(memberUserChannelAccountId);
        assertPojoEquals(reqVO, memberUserChannelAccount);
    }

    @Test
    public void testUpdateMemberUserChannelAccount_success() {
        // mock 数据
        MemberUserChannelAccountDO dbMemberUserChannelAccount = randomPojo(MemberUserChannelAccountDO.class);
        memberUserChannelAccountMapper.insert(dbMemberUserChannelAccount);// @Sql: 先插入出一条存在的数据
        // 准备参数
        MemberUserChannelAccountUpdateReqVO reqVO = randomPojo(MemberUserChannelAccountUpdateReqVO.class, o -> {
            o.setId(dbMemberUserChannelAccount.getId()); // 设置更新的 ID
        });

        // 调用
        memberUserChannelAccountService.updateMemberUserChannelAccount(reqVO);
        // 校验是否更新正确
        MemberUserChannelAccountDO memberUserChannelAccount = memberUserChannelAccountMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, memberUserChannelAccount);
    }

    @Test
    public void testUpdateMemberUserChannelAccount_notExists() {
        // 准备参数
        MemberUserChannelAccountUpdateReqVO reqVO = randomPojo(MemberUserChannelAccountUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> memberUserChannelAccountService.updateMemberUserChannelAccount(reqVO), MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS);
    }

    @Test
    public void testDeleteMemberUserChannelAccount_success() {
        // mock 数据
        MemberUserChannelAccountDO dbMemberUserChannelAccount = randomPojo(MemberUserChannelAccountDO.class);
        memberUserChannelAccountMapper.insert(dbMemberUserChannelAccount);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbMemberUserChannelAccount.getId();

        // 调用
        memberUserChannelAccountService.deleteMemberUserChannelAccount(id);
       // 校验数据不存在了
       assertNull(memberUserChannelAccountMapper.selectById(id));
    }

    @Test
    public void testDeleteMemberUserChannelAccount_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> memberUserChannelAccountService.deleteMemberUserChannelAccount(id), MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMemberUserChannelAccountPage() {
       // mock 数据
       MemberUserChannelAccountDO dbMemberUserChannelAccount = randomPojo(MemberUserChannelAccountDO.class, o -> { // 等会查询到
           o.setChannelCode(null);
           o.setRemark(null);
           o.setCreateTime(null);
           o.setPlatformId(null);
           o.setPlatformNo(null);
           o.setPlatformName(null);
           o.setPlatformLevel(null);
           o.setAuditStatus(null);
           o.setStatus(null);
       });
       memberUserChannelAccountMapper.insert(dbMemberUserChannelAccount);
       // 测试 channelCode 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setChannelCode(null)));
       // 测试 remark 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setCreateTime(null)));
       // 测试 platformId 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformId(null)));
       // 测试 platformNo 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformNo(null)));
       // 测试 platformNanme 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformName(null)));
       // 测试 platformLevel 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformLevel(null)));
       // 测试 auditStatus 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setAuditStatus(null)));
       // 测试 status 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setStatus(null)));
       // 准备参数
       MemberUserChannelAccountPageReqVO reqVO = new MemberUserChannelAccountPageReqVO();
       reqVO.setChannelCode(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setPlatformId(null);
       reqVO.setPlatformNo(null);
       reqVO.setPlatformName(null);
       reqVO.setPlatformLevel(null);
       reqVO.setAuditStatus(null);
       reqVO.setStatus(null);

       // 调用
       PageResult<MemberUserChannelAccountDO> pageResult = memberUserChannelAccountService.getMemberUserChannelAccountPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbMemberUserChannelAccount, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetMemberUserChannelAccountList() {
       // mock 数据
       MemberUserChannelAccountDO dbMemberUserChannelAccount = randomPojo(MemberUserChannelAccountDO.class, o -> { // 等会查询到
           o.setChannelCode(null);
           o.setRemark(null);
           o.setCreateTime(null);
           o.setPlatformId(null);
           o.setPlatformNo(null);
           o.setPlatformName(null);
           o.setPlatformLevel(null);
           o.setAuditStatus(null);
           o.setStatus(null);
       });
       memberUserChannelAccountMapper.insert(dbMemberUserChannelAccount);
       // 测试 channelCode 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setChannelCode(null)));
       // 测试 remark 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setCreateTime(null)));
       // 测试 platformId 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformId(null)));
       // 测试 platformNo 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformNo(null)));
       // 测试 platformNanme 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformName(null)));
       // 测试 platformLevel 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setPlatformLevel(null)));
       // 测试 auditStatus 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setAuditStatus(null)));
       // 测试 status 不匹配
       memberUserChannelAccountMapper.insert(cloneIgnoreId(dbMemberUserChannelAccount, o -> o.setStatus(null)));
       // 准备参数
       MemberUserChannelAccountExportReqVO reqVO = new MemberUserChannelAccountExportReqVO();
       reqVO.setChannelCode(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
       reqVO.setPlatformId(null);
       reqVO.setPlatformNo(null);
       reqVO.setPlatformName(null);
       reqVO.setPlatformLevel(null);
       reqVO.setAuditStatus(null);
       reqVO.setStatus(null);

       // 调用
       List<MemberUserChannelAccountDO> list = memberUserChannelAccountService.getMemberUserChannelAccountList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbMemberUserChannelAccount, list.get(0));
    }

}
