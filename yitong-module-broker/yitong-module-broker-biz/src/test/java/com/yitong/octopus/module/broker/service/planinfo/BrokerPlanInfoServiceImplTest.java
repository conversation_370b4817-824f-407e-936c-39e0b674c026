package com.yitong.octopus.module.broker.service.planinfo;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.controller.admin.planinfo.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfo.BrokerPlanInfoDO;
import com.yitong.octopus.module.broker.dal.mysql.planinfo.BrokerPlanInfoMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static com.yitong.octopus.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link BrokerPlanInfoServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanInfoServiceImpl.class)
public class BrokerPlanInfoServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanInfoServiceImpl planInfoService;

    @Resource
    private BrokerPlanInfoMapper planInfoMapper;

    @Test
    public void testCreatePlanInfo_success() {
        // 准备参数
        BrokerPlanInfoCreateReqVO reqVO = randomPojo(BrokerPlanInfoCreateReqVO.class);

        // 调用
        Long planInfoId = planInfoService.createPlanInfo(reqVO);
        // 断言
        assertNotNull(planInfoId);
        // 校验记录的属性是否正确
        BrokerPlanInfoDO planInfo = planInfoMapper.selectById(planInfoId);
        assertPojoEquals(reqVO, planInfo);
    }

    @Test
    public void testUpdatePlanInfo_success() {
        // mock 数据
        BrokerPlanInfoDO dbPlanInfo = randomPojo(BrokerPlanInfoDO.class);
        planInfoMapper.insert(dbPlanInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanInfoUpdateReqVO reqVO = randomPojo(BrokerPlanInfoUpdateReqVO.class, o -> {
            o.setId(dbPlanInfo.getId()); // 设置更新的 ID
        });

        // 调用
        planInfoService.updatePlanInfo(reqVO);
        // 校验是否更新正确
        BrokerPlanInfoDO planInfo = planInfoMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planInfo);
    }

    @Test
    public void testUpdatePlanInfo_notExists() {
        // 准备参数
        BrokerPlanInfoUpdateReqVO reqVO = randomPojo(BrokerPlanInfoUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> planInfoService.updatePlanInfo(reqVO), PLAN_INFO_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanInfo_success() {
        // mock 数据
        BrokerPlanInfoDO dbPlanInfo = randomPojo(BrokerPlanInfoDO.class);
        planInfoMapper.insert(dbPlanInfo);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanInfo.getId();

        // 调用
        planInfoService.deletePlanInfo(id);
       // 校验数据不存在了
       assertNull(planInfoMapper.selectById(id));
    }

    @Test
    public void testDeletePlanInfo_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planInfoService.deletePlanInfo(id), PLAN_INFO_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanInfoPage() {
       // mock 数据
       BrokerPlanInfoDO dbPlanInfo = randomPojo(BrokerPlanInfoDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setType(null);
           o.setSpId(null);
           o.setContentType(null);
           o.setCreateTime(null);
       });
       planInfoMapper.insert(dbPlanInfo);
       // 测试 name 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setName(null)));
       // 测试 type 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setType(null)));
       // 测试 spId 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setSpId(null)));
       // 测试 contentType 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setContentType(null)));
       // 测试 createTime 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanInfoPageReqVO reqVO = new BrokerPlanInfoPageReqVO();
       reqVO.setName(null);
       reqVO.setType(null);
       reqVO.setSpId(null);
       reqVO.setContentType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanInfoDO> pageResult = planInfoService.getPlanInfoPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanInfo, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanInfoList() {
       // mock 数据
       BrokerPlanInfoDO dbPlanInfo = randomPojo(BrokerPlanInfoDO.class, o -> { // 等会查询到
           o.setName(null);
           o.setType(null);
           o.setSpId(null);
           o.setContentType(null);
           o.setCreateTime(null);
       });
       planInfoMapper.insert(dbPlanInfo);
       // 测试 name 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setName(null)));
       // 测试 type 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setType(null)));
       // 测试 spId 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setSpId(null)));
       // 测试 contentType 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setContentType(null)));
       // 测试 createTime 不匹配
       planInfoMapper.insert(cloneIgnoreId(dbPlanInfo, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanInfoExportReqVO reqVO = new BrokerPlanInfoExportReqVO();
       reqVO.setName(null);
       reqVO.setType(null);
       reqVO.setSpId(null);
       reqVO.setContentType(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanInfoDO> list = planInfoService.getPlanInfoList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanInfo, list.get(0));
    }

}
