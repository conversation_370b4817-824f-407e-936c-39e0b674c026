package com.yitong.octopus.module.broker.service.planinfoapply;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.broker.controller.admin.planinfoapply.vo.*;
import com.yitong.octopus.module.broker.dal.dataobject.planinfoapply.BrokerPlanInfoApplyDO;
import com.yitong.octopus.module.broker.dal.mysql.planinfoapply.BrokerPlanInfoApplyMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import org.springframework.context.annotation.Import;
import java.util.*;

import static com.yitong.octopus.module.broker.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static org.junit.jupiter.api.Assertions.*;

/**
* {@link BrokerPlanInfoApplyServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(BrokerPlanInfoApplyServiceImpl.class)
public class BrokerPlanInfoApplyServiceImplTest extends BaseDbUnitTest {

    @Resource
    private BrokerPlanInfoApplyServiceImpl planInfoApplyService;

    @Resource
    private BrokerPlanInfoApplyMapper planInfoApplyMapper;

    @Test
    public void testCreatePlanInfoApply_success() {
        // 准备参数
        BrokerPlanInfoApplyReqVO reqVO = randomPojo(BrokerPlanInfoApplyReqVO.class);

        // 调用
        Long planInfoApplyId = planInfoApplyService.applyPlanInfo(reqVO);
        // 断言
        assertNotNull(planInfoApplyId);
        // 校验记录的属性是否正确
        BrokerPlanInfoApplyDO planInfoApply = planInfoApplyMapper.selectById(planInfoApplyId);
        assertPojoEquals(reqVO, planInfoApply);
    }

    @Test
    public void testUpdatePlanInfoApply_success() {
        // mock 数据
        BrokerPlanInfoApplyDO dbPlanInfoApply = randomPojo(BrokerPlanInfoApplyDO.class);
        planInfoApplyMapper.insert(dbPlanInfoApply);// @Sql: 先插入出一条存在的数据
        // 准备参数
        BrokerPlanInfoApplyUpdateReqVO reqVO = randomPojo(BrokerPlanInfoApplyUpdateReqVO.class, o -> {
            o.setId(dbPlanInfoApply.getId()); // 设置更新的 ID
        });

        // 调用
//        planInfoApplyService.confirmPlanInfoApply(reqVO);
        // 校验是否更新正确
        BrokerPlanInfoApplyDO planInfoApply = planInfoApplyMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, planInfoApply);
    }

    @Test
    public void testUpdatePlanInfoApply_notExists() {
        // 准备参数
        BrokerPlanInfoApplyUpdateReqVO reqVO = randomPojo(BrokerPlanInfoApplyUpdateReqVO.class);

        // 调用, 并断言异常
//        assertServiceException(() -> planInfoApplyService.confirmPlanInfoApply(reqVO), PLAN_INFO_APPLY_NOT_EXISTS);
    }

    @Test
    public void testDeletePlanInfoApply_success() {
        // mock 数据
        BrokerPlanInfoApplyDO dbPlanInfoApply = randomPojo(BrokerPlanInfoApplyDO.class);
        planInfoApplyMapper.insert(dbPlanInfoApply);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbPlanInfoApply.getId();

        // 调用
        planInfoApplyService.deletePlanInfoApply(id);
       // 校验数据不存在了
       assertNull(planInfoApplyMapper.selectById(id));
    }

    @Test
    public void testDeletePlanInfoApply_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> planInfoApplyService.deletePlanInfoApply(id), PLAN_INFO_APPLY_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanInfoApplyPage() {
       // mock 数据
       BrokerPlanInfoApplyDO dbPlanInfoApply = randomPojo(BrokerPlanInfoApplyDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setTalentId(null);
           o.setTalentAccountId(null);
           o.setChannelId(null);
           o.setChannelCode(null);
           o.setStatus(null);
           o.setWorksId(null);
           o.setWorksShortUrl(null);
           o.setWorksUrl(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planInfoApplyMapper.insert(dbPlanInfoApply);
       // 测试 planId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setPlanId(null)));
       // 测试 talentId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setTalentId(null)));
       // 测试 talentAccountId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setTalentAccountId(null)));
       // 测试 channleId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setChannelId(null)));
       // 测试 channleCode 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setChannelCode(null)));
       // 测试 status 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setStatus(null)));
       // 测试 worksId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksId(null)));
       // 测试 worksShortUrl 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksShortUrl(null)));
       // 测试 worksUrl 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksUrl(null)));
       // 测试 remark 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanInfoApplyPageReqVO reqVO = new BrokerPlanInfoApplyPageReqVO();
       reqVO.setPlanId(null);
       reqVO.setTalentId(null);
       reqVO.setTalentAccountId(null);
       reqVO.setChannelId(null);
       reqVO.setChannelCode(null);
       reqVO.setStatus(null);
       reqVO.setWorksId(null);
       reqVO.setWorksShortUrl(null);
       reqVO.setWorksUrl(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<BrokerPlanInfoApplyDO> pageResult = planInfoApplyService.getPlanInfoApplyPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbPlanInfoApply, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetPlanInfoApplyList() {
       // mock 数据
       BrokerPlanInfoApplyDO dbPlanInfoApply = randomPojo(BrokerPlanInfoApplyDO.class, o -> { // 等会查询到
           o.setPlanId(null);
           o.setTalentId(null);
           o.setTalentAccountId(null);
           o.setChannelId(null);
           o.setChannelCode(null);
           o.setStatus(null);
           o.setWorksId(null);
           o.setWorksShortUrl(null);
           o.setWorksUrl(null);
           o.setRemark(null);
           o.setCreateTime(null);
       });
       planInfoApplyMapper.insert(dbPlanInfoApply);
       // 测试 planId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setPlanId(null)));
       // 测试 talentId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setTalentId(null)));
       // 测试 talentAccountId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setTalentAccountId(null)));
       // 测试 channleId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setChannelId(null)));
       // 测试 channleCode 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setChannelCode(null)));
       // 测试 status 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setStatus(null)));
       // 测试 worksId 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksId(null)));
       // 测试 worksShortUrl 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksShortUrl(null)));
       // 测试 worksUrl 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setWorksUrl(null)));
       // 测试 remark 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setRemark(null)));
       // 测试 createTime 不匹配
       planInfoApplyMapper.insert(cloneIgnoreId(dbPlanInfoApply, o -> o.setCreateTime(null)));
       // 准备参数
       BrokerPlanInfoApplyExportReqVO reqVO = new BrokerPlanInfoApplyExportReqVO();
       reqVO.setPlanId(null);
       reqVO.setTalentId(null);
       reqVO.setTalentAccountId(null);
       reqVO.setChannelId(null);
       reqVO.setChannelCode(null);
       reqVO.setStatus(null);
       reqVO.setWorksId(null);
       reqVO.setWorksShortUrl(null);
       reqVO.setWorksUrl(null);
       reqVO.setRemark(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<BrokerPlanInfoApplyDO> list = planInfoApplyService.getPlanInfoApplyList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbPlanInfoApply, list.get(0));
    }

}
