package com.yitong.octopus.module.broker.enums;

import com.yitong.octopus.framework.common.exception.ErrorCode;

/**
 * Member 错误码枚举类
 *
 * member 系统，使用 1-004-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 用户相关  1004001000============
    ErrorCode USER_NOT_EXISTS = new ErrorCode(1004001000, "用户不存在");
    ErrorCode USER_PASSWORD_FAILED = new ErrorCode(1004001001, "密码校验失败");

    // ========== AUTH 模块 1004003000 ==========
    ErrorCode AUTH_LOGIN_BAD_CREDENTIALS = new ErrorCode(1004003000, "登录失败，账号密码不正确");
    ErrorCode AUTH_LOGIN_USER_DISABLED = new ErrorCode(1004003001, "登录失败，账号被禁用");
    ErrorCode AUTH_TOKEN_EXPIRED = new ErrorCode(**********, "Token 已经过期");
    ErrorCode AUTH_THIRD_LOGIN_NOT_BIND = new ErrorCode(**********, "未绑定账号，需要进行绑定");
    ErrorCode AUTH_WEIXIN_MINI_APP_PHONE_CODE_ERROR = new ErrorCode(**********, "获得手机号失败");

    // ========== 用户收件地址 ********** ==========
    ErrorCode ADDRESS_NOT_EXISTS = new ErrorCode(**********, "用户收件地址不存在");
    ErrorCode MEMBER_USER_CHANNEL_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "经纪人渠道账户信息不存在");
    ErrorCode MEMBER_USER_CHANNEL_ACCOUNT_AUDIT_STATUS_ERROR = new ErrorCode(**********, "经纪人渠道账户信息不能重复审核");
    ErrorCode PLAN_INFO_NOT_EXISTS = new ErrorCode(**********, "经纪人计划信息不存在");
    ErrorCode PLAN_BILL_INFO_CHECK_NOT_EXISTS = new ErrorCode(**********, "经纪人计划账单确认记录不存在");
    ErrorCode PLAN_CHANNEL_NOT_EXISTS = new ErrorCode(**********, "经纪人计划渠道信息不存在");
    ErrorCode PLAN_CONTACT_NOT_EXISTS = new ErrorCode(**********, "经纪人计划联系信息不存在");
    ErrorCode PLAN_COOP_DETAIL_NOT_EXISTS = new ErrorCode(**********, "经纪人计划合作细则不存在");
    ErrorCode PLAN_FISSION_NOT_EXISTS = new ErrorCode(**********, "经纪人计划招募费用不存在");
    ErrorCode PLAN_INFO_APPLY_NOT_EXISTS = new ErrorCode(**********, "经纪人计划申请不存在");
    ErrorCode PLAN_REFUND_NOT_EXISTS = new ErrorCode(**********, "经纪人计划取消不存在");
    ErrorCode PLAN_SKU_NOT_EXISTS = new ErrorCode(1004014000, "经纪人计划sku信息不存在");
    ErrorCode PLAN_STORE_NOT_EXISTS = new ErrorCode(1004015000, "计划门店信息不存在");
    // ========== 经纪人计划结算信息 TODO 补充编号 ==========
    ErrorCode PLAN_BILL_ITEM_NOT_EXISTS = new ErrorCode(1005015000, "经纪人计划结算信息不存在");
    ErrorCode PLAN_APPLY_EXISTS = new ErrorCode(1006001000, "已经预约，请勿重复申请");
}
