package com.yitong.octopus.module.broker.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * TODO 审核状态统一
 * 审核状态 0 待审核 1 未通过 2 已通过
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {

    CREATE(0, "待审核"),
    FAIL(1, "未通过"),
    SUCCESS(2, "已通过");

    private final Integer status;
    private final String name;

    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, SUCCESS.getStatus());
    }

}
