package com.yitong.octopus.module.broker.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 状态:1 待审核，2已拒绝，3进行中，4已完成，5已结算
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PlanApplyStatusEnum {

    APPLY(1, "待审核"),
    REJECT(2, "已拒绝"),
    PROGRESS(3, "进行中"),
    FINISH(4, "已完成"),
    SETTLEMENT(5, "已结算");

    private final Integer status;
    private final String name;

    public static boolean isSuccess(Integer status) {
        return Objects.equals(status, PROGRESS.getStatus());
    }

}
