package com.yitong.octopus.module.infra.api.config;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.infra.api.config.dto.ConfigDto;
import com.yitong.octopus.module.infra.dal.dataobject.config.ConfigDO;
import com.yitong.octopus.module.infra.service.config.ConfigService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

@Service
@Validated
public class ConfigApiImpl implements ConfigApi{

    @Resource
    private ConfigService configService;

    @Override
    public ConfigDto getConfigByKey(String key) {
        ConfigDO configDO = configService.getConfigByKey(key);
        return BeanUtil.toBean(configDO,ConfigDto.class);
    }
}
