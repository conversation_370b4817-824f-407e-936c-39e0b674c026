package com.yitong.octopus.module.contract.controller.admin.contractcreditplan;

import java.time.LocalDate;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yitong.octopus.module.contract.enums.ContractCreditPlanStatus;
import com.yitong.octopus.module.contract.service.entry.ContractCreditPlanDO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContractCreditPlanDTO {

	@Schema(title = "回款计划ID", example = "1000000")
	private String id;

	@Schema(title = "合同ID", example = "TG001-20240101001")
	private String contractId;

	@Schema(title = "第X期", example = "1")
	private Integer seq;

	@Schema(title = "回款状态 ", description = "PENDING-待付款 PAYED-已付款 LATE-逾期", example = "1")
	private ContractCreditPlanStatus status;

	@Schema(title = "计划回款金额", description = "单位：分", example = "1000000")
	private Long planAmount;

	@Schema(title = "计划最后回款日期", example = "2024-01-01")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private LocalDate planDate;

	@Schema(title = "已回款金额", description = "单位：分", example = "500000")
	private Long payedAmount;

	public ContractCreditPlanDTO(ContractCreditPlanDO e) {
		this.id = e.getId();
		this.contractId = e.getContractId();
		this.seq = e.getSeq();
		this.status = e.getStatus();
		this.planAmount = e.getPlanAmount();
		this.planDate = e.getPlanDate();
		this.payedAmount = e.getPayedAmount();
	}
}
