# 分销代理系统详细设计文档 v1.0

## 1. 概述

本文档基于现有需求，对分销代理系统进行详细设计，重点关注以下三个核心功能：
1. 自定义分销等级体系
2. 分销员申请与绑定机制
3. 分润计算与结算方案

## 2. 核心需求分析

### 2.1 自定义分销
- **需求描述**：支持自定义分销等级，通过等级级别（level_grade）区分高低。
- **设计要点**：
  - 等级可灵活配置（数量、名称、条件）
  - 通过level_grade数值大小判断等级高低
  - 支持升降级条件配置
  - 等级变更自动触发相关业务流程

### 2.2 分销员申请与绑定
- **需求描述**：人员申请成为分销员后，有自己的分销码。其他人员根据分销码，申请或者注册就成功绑定其为分销员的子分销员。
- **设计要点**：
  - 每个分销员拥有唯一的分销码
  - 区分三种关系：
    - **上级**：管理关系，决定组织架构和团队归属
    - **等级**：决定分销员的分润比例和权益
    - **介绍人**：推荐关系，申请时谁介绍的（可有可无）
  - 支持通过分销码建立关系
  - **新注册用户**：通过分销码注册时，分销码拥有者同时成为其上级和介绍人
  - **已注册用户**：申请时可灵活选择绑定关系类型

### 2.3 分润计算方案
- **需求描述**：用户购买分销员分销的商品，根据分销员等级确定分润比例。分销员分销之后是否要分润，分润给上级或者介绍人，都可以灵活配置。
- **设计要点**：
  - 区分销售等级方案（直接销售者奖励）和分润等级方案（上级/介绍人分润）
  - 销售等级方案：基于当前分销员等级，确定其销售商品的佣金
  - 分润等级方案：
    - 支持配置分润对象：仅上级、仅介绍人、两者都有
    - 可为上级和介绍人设置不同的分润比例
    - 支持按照管理关系或推荐关系追溯
  - 支持多级分润（向上追溯）
  - 灵活的分润规则配置
  - 实时计算与结算

## 3. 数据库设计

### 3.1 分销等级表（yt_dist_level）
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 3.2 分销员信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID（管理关系）',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID（推荐关系）',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `referrer_time` datetime DEFAULT NULL COMMENT '介绍时间',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：1,2,3',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `referral_count` int(11) DEFAULT '0' COMMENT '介绍人数',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(10,2) DEFAULT '0.00' COMMENT '本月销售额',
  `join_time` datetime NOT NULL COMMENT '成为分销员时间',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referrer_code` (`referrer_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_depth` (`depth`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 3.3 销售等级方案表（yt_dist_sales_scheme）
```sql
CREATE TABLE `yt_dist_sales_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `level_id` bigint(20) NOT NULL COMMENT '适用等级ID',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定商品',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID（apply_scope=2时使用）',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（apply_scope=2时使用，可选）',
  `commission_mode` tinyint(4) NOT NULL DEFAULT '2' COMMENT '佣金模式：1-固定金额，2-百分比',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例（%）',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '固定佣金金额',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-订单收货后，3-订单完成后，4-券码核销后',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='销售等级方案表';
```

### 3.4 分润等级方案表（yt_dist_profit_scheme）
```sql
CREATE TABLE `yt_dist_profit_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `level_id` bigint(20) NOT NULL COMMENT '受益等级ID（上级等级）',
  `source_level_id` bigint(20) DEFAULT NULL COMMENT '来源等级ID（下级等级），NULL表示所有等级',
  `profit_target` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分润对象：1-上级，2-介绍人，3-两者都有',
  `profit_config` json DEFAULT NULL COMMENT '分润配置（JSON格式，支持配置上级和介绍人的不同比例）',
  `trace_level` int(11) NOT NULL DEFAULT '1' COMMENT '追溯层级：1-直接关系，2-二级关系，3-三级关系',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定商品',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID（apply_scope=2时使用）',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（apply_scope=2时使用，可选）',
  `commission_mode` tinyint(4) NOT NULL DEFAULT '2' COMMENT '佣金模式：1-固定金额，2-百分比',
  `commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '佣金比例（%）',
  `commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '固定佣金金额',
  `condition_type` tinyint(4) DEFAULT '1' COMMENT '条件类型：1-无条件，2-基于下级销售额，3-基于下级佣金',
  `condition_config` json DEFAULT NULL COMMENT '条件配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_source_level_id` (`source_level_id`),
  KEY `idx_profit_target` (`profit_target`),
  KEY `idx_trace_level` (`trace_level`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分润等级方案表';
```

### 3.5 分销员申请记录表（yt_dist_agent_apply_record）
```sql
CREATE TABLE `yt_dist_agent_apply_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `apply_no` varchar(64) NOT NULL COMMENT '申请编号',
  `member_id` bigint(20) NOT NULL COMMENT '申请人会员ID',
  `member_name` varchar(64) DEFAULT NULL COMMENT '会员名称',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '使用的邀请码',
  `invite_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '邀请码类型：1-分销员邀请码，2-等级邀请码',
  `initial_level_id` bigint(20) DEFAULT NULL COMMENT '初始等级ID',
  `parent_agent_id` bigint(20) DEFAULT NULL COMMENT '上级分销员ID',
  `referrer_agent_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID',
  `bind_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '绑定类型：1-同时绑定，2-仅上级，3-仅介绍人',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核，1-审核通过，2-审核拒绝',
  `agent_id` bigint(20) DEFAULT NULL COMMENT '分销员ID（审核通过后生成）',
  `agent_code` varchar(32) DEFAULT NULL COMMENT '分销码（审核通过后生成）',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `auto_audit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否自动审核',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_apply_no` (`apply_no`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_invite_code` (`invite_code`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员申请记录表';
```

### 3.6 佣金账单记录表（yt_dist_agent_bill_record）
```sql
CREATE TABLE `yt_dist_agent_bill_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID（快照）',
  `bill_type` tinyint(4) NOT NULL COMMENT '账单类型：1-分销奖励，2-分润收益',
  `biz_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '业务类型：1-商品订单，2-优惠券核销',
  `biz_id` bigint(20) NOT NULL COMMENT '业务ID（订单ID或优惠券ID）',
  `biz_no` varchar(64) NOT NULL COMMENT '业务编号（订单号或券码）',
  `order_id` bigint(20) DEFAULT NULL COMMENT '订单ID',
  `order_amount` decimal(10,2) DEFAULT NULL COMMENT '订单金额',
  `channel_order_id` varchar(64) DEFAULT NULL COMMENT '渠道订单号',
  `coupon_id` bigint(20) DEFAULT NULL COMMENT '券码ID',
  `coupon_code` varchar(64) DEFAULT NULL COMMENT '券码编号',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `spu_name` varchar(128) DEFAULT NULL COMMENT 'SPU名称',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID',
  `sku_name` varchar(128) DEFAULT NULL COMMENT 'SKU名称',
  `source_agent_id` bigint(20) DEFAULT NULL COMMENT '来源分销员ID（产生业绩的分销员）',
  `source_agent_name` varchar(64) DEFAULT NULL COMMENT '来源分销员名称',
  `trace_level` int(11) DEFAULT '0' COMMENT '追溯层级：0-直接销售，1-一级，2-二级，3-三级',
  `amount` decimal(10,2) NOT NULL COMMENT '账单金额（正数为收入，负数为退款）',
  `base_amount` decimal(10,2) NOT NULL COMMENT '计算基数金额',
  `rate` decimal(5,2) DEFAULT NULL COMMENT '佣金比例（%）',
  `scheme_id` bigint(20) NOT NULL COMMENT '使用的方案ID',
  `scheme_type` tinyint(4) NOT NULL COMMENT '方案类型：1-销售方案，2-分润方案',
  `scheme_snapshot` json DEFAULT NULL COMMENT '方案快照（JSON格式）',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-订单收货后，3-订单完成后，4-券码核销后',
  `freeze_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '冻结状态：0-未冻结，1-冻结中',
  `freeze_days` int(11) DEFAULT '0' COMMENT '冻结天数',
  `freeze_end_time` datetime DEFAULT NULL COMMENT '冻结结束时间',
  `unfreeze_time` datetime DEFAULT NULL COMMENT '实际解冻时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待结算，1-已结算，2-已冻结，3-已取消',
  `settle_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算，1-部分结算，2-已结算',
  `settled_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已结算金额',
  `frozen_reason` varchar(256) DEFAULT NULL COMMENT '冻结原因',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `bill_time` datetime NOT NULL COMMENT '账单时间',
  `settle_time` datetime DEFAULT NULL COMMENT '结算时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_no` (`bill_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_source_agent_id` (`source_agent_id`),
  KEY `idx_biz_id_type` (`biz_id`, `biz_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_coupon_id` (`coupon_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bill_time` (`bill_time`),
  KEY `idx_freeze_status` (`freeze_status`),
  KEY `idx_freeze_end_time` (`freeze_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='佣金账单记录表';
```

### 3.7 分销员账户余额表（yt_dist_agent_balance）
```sql
CREATE TABLE `yt_dist_agent_balance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '余额ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `total_commission` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计佣金',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结金额',
  `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可提现余额',
  `withdrawing_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '提现中金额',
  `withdrawn_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现金额',
  `balance_version` bigint(20) NOT NULL DEFAULT '0' COMMENT '余额版本号（乐观锁）',
  `last_bill_id` bigint(20) DEFAULT NULL COMMENT '最后处理的账单ID',
  `last_calculate_time` datetime DEFAULT NULL COMMENT '最后计算时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_id` (`agent_id`),
  KEY `idx_balance_version` (`balance_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员账户余额表';
```

### 3.8 分销员提现申请表（yt_dist_agent_withdraw_apply）
```sql
CREATE TABLE `yt_dist_agent_withdraw_apply` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提现申请ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现申请编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `agent_level_id` bigint(20) NOT NULL COMMENT '分销员等级ID',
  `apply_amount` decimal(10,2) NOT NULL COMMENT '申请提现金额',
  `withdraw_type` tinyint(4) NOT NULL COMMENT '提现方式：1-银行卡，2-微信，3-支付宝',
  `account_info` json NOT NULL COMMENT '收款账户信息（JSON格式）',
  `invoice_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '发票类型：1-无发票（代扣申报），2-有发票',
  `invoice_amount` decimal(10,2) DEFAULT NULL COMMENT '发票金额',
  `invoice_urls` json DEFAULT NULL COMMENT '发票图片URL列表',
  `tax_rate` decimal(5,2) DEFAULT '0.00' COMMENT '税率（%）',
  `tax_amount` decimal(10,2) DEFAULT '0.00' COMMENT '税费金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际打款金额',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待财务审核，1-审核通过，2-审核拒绝，3-已打款，4-已完成',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(64) DEFAULT NULL COMMENT '审核人',
  `audit_remark` varchar(256) DEFAULT NULL COMMENT '审核备注',
  `payment_time` datetime DEFAULT NULL COMMENT '打款时间',
  `payment_user` varchar(64) DEFAULT NULL COMMENT '打款人',
  `payment_voucher` varchar(256) DEFAULT NULL COMMENT '打款凭证',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `remark` varchar(512) DEFAULT NULL COMMENT '备注',
  `apply_time` datetime NOT NULL COMMENT '申请时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员提现申请表';
```

### 3.9 分销员收款账户表（yt_dist_agent_account）
```sql
CREATE TABLE `yt_dist_agent_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `account_type` tinyint(4) NOT NULL COMMENT '账户类型：1-银行卡，2-微信，3-支付宝',
  `account_name` varchar(64) NOT NULL COMMENT '账户名称',
  `account_number` varchar(64) NOT NULL COMMENT '账户号码',
  `bank_name` varchar(64) DEFAULT NULL COMMENT '银行名称（银行卡专用）',
  `bank_branch` varchar(128) DEFAULT NULL COMMENT '开户行支行（银行卡专用）',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认账户',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_account_type` (`account_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员收款账户表';
```

### 3.10 分销员提现明细表（yt_dist_agent_withdraw_detail）
```sql
CREATE TABLE `yt_dist_agent_withdraw_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `withdraw_id` bigint(20) NOT NULL COMMENT '提现申请ID',
  `bill_id` bigint(20) NOT NULL COMMENT '账单记录ID',
  `bill_no` varchar(64) NOT NULL COMMENT '账单编号',
  `withdraw_amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_withdraw_id` (`withdraw_id`),
  KEY `idx_bill_id` (`bill_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员提现明细表';
```

### 3.11 分销员税务记录表（yt_dist_agent_tax_record）
```sql
CREATE TABLE `yt_dist_agent_tax_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '税务记录ID',
  `tax_no` varchar(64) NOT NULL COMMENT '税务编号',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `withdraw_id` bigint(20) NOT NULL COMMENT '提现申请ID',
  `tax_year` int(11) NOT NULL COMMENT '税务年度',
  `tax_month` int(11) NOT NULL COMMENT '税务月份',
  `income_amount` decimal(10,2) NOT NULL COMMENT '收入金额',
  `tax_rate` decimal(5,2) NOT NULL COMMENT '税率（%）',
  `tax_amount` decimal(10,2) NOT NULL COMMENT '税费金额',
  `after_tax_amount` decimal(10,2) NOT NULL COMMENT '税后金额',
  `report_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '申报状态：0-未申报，1-已申报',
  `report_time` datetime DEFAULT NULL COMMENT '申报时间',
  `report_user` varchar(64) DEFAULT NULL COMMENT '申报人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tax_no` (`tax_no`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_withdraw_id` (`withdraw_id`),
  KEY `idx_tax_year_month` (`tax_year`, `tax_month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员税务记录表';
```

## 4. 核心功能设计

### 4.1 商品数据模型说明

#### 4.1.1 SPU与SKU设计
在分销系统中，我们采用标准的电商商品数据模型：
- **SPU (Standard Product Unit)**：标准产品单位，代表一个商品
- **SKU (Stock Keeping Unit)**：库存单位，代表商品的具体规格

**设计原因**：
1. **避免冗余**：goods_id通常与spu_id功能重复，统一使用spu_id更规范
2. **灵活性**：支持按SPU（整个商品）或SKU（具体规格）配置分销方案
3. **兼容性**：与主流电商系统保持一致的商品模型

**使用场景**：
- 配置全品类佣金：`apply_scope = 1`
- 配置特定商品佣金：`apply_scope = 2, spu_id = xxx`
- 配置特定规格佣金：`apply_scope = 2, spu_id = xxx, sku_id = yyy`

### 4.2 分销员申请流程

#### 4.2.1 申请流程设计
系统支持灵活的审核机制，可通过配置控制：

1. **审核机制**
   - **审核开关**：可配置是否启用审核（使用系统配置 infra_config，配置键：dist.apply.audit.enable）
   - **自动审核**：可配置是否自动审核通过（配置键：dist.apply.audit.auto）
   - **人工审核**：支持管理员手动审核申请

2. **邀请码类型**
   - **分销员邀请码**：每个分销员的专属邀请码
   - **等级邀请码**：特定等级的通用邀请码
   - 根据邀请码类型确定初始等级和关系

3. **新用户注册申请**
   - 通过分销员邀请码注册：邀请人同时成为上级和介绍人
   - 通过等级邀请码注册：获得对应等级，无上级和介绍人
   - 自动提交审核或直接通过（根据配置）

4. **已有用户申请**
   - 使用分销员邀请码：可选择绑定类型
     - 同时绑定为上级和介绍人（默认）
     - 仅绑定为上级
     - 仅绑定为介绍人
   - 使用等级邀请码：获得对应等级，不影响现有关系

5. **无邀请码申请**
   - 使用系统默认等级（配置键：dist.apply.default.level）
   - 成为独立分销员

#### 4.2.2 等级管理
- **初始等级**：根据邀请码类型或系统配置确定
- **等级调整**：
  - 人工调整：管理员可手动修改分销员等级
  - 自动升降级：根据配置的条件自动调整
- **等级邀请码**：每个等级可配置专属邀请码，方便批量招募特定等级分销员

### 4.3 分销等级管理

#### 4.3.1 等级体系结构
```
等级示例（按level_grade从高到低）：
- 钻石等级 (level_grade: 40, invite_code: DIA2024)
- 黄金等级 (level_grade: 30, invite_code: GOLD2024)
- 白银等级 (level_grade: 20, invite_code: SILVER2024)
- 青铜等级 (level_grade: 10, invite_code: BRONZE2024)

说明：
- level_grade数值越大，等级越高
- 各等级之间相互独立，无链式关系
- 每个等级可配置独立的邀请码
- 通过等级邀请码申请的用户，初始即为对应等级
- 等级可通过人工调整或满足条件自动升降级
```

#### 4.3.2 升降级条件配置示例
```json
{
  "upgrade_conditions": {
    "sales_amount": 50000,          // 累计销售额
    "team_count": 100,              // 团队人数
    "direct_count": 20,             // 直属人数
    "month_performance": 10000,     // 月度业绩
    "condition_logic": "AND"        // 条件逻辑：AND/OR
  },
  "downgrade_conditions": {
    "inactive_days": 90,            // 连续不活跃天数
    "month_performance": 1000,      // 月度业绩低于
    "condition_logic": "OR"
  }
}
```

#### 4.3.3 等级管理接口
```java
@RestController
@RequestMapping("/api/distribution/level")
public class DistLevelController {
    
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('distribution:level:create')")
    public CommonResult<Long> createLevel(@Valid @RequestBody DistLevelCreateReqVO req) {
        // 验证等级链的完整性
        validateLevelChain(req);
        return success(levelService.createLevel(req));
    }
    
    @PutMapping("/update-chain")
    @PreAuthorize("@ss.hasPermission('distribution:level:update')")
    public CommonResult<Boolean> updateLevelChain(@Valid @RequestBody LevelChainUpdateReqVO req) {
        // 更新等级链关系
        levelService.updateLevelChain(req);
        return success(true);
    }
    
    @GetMapping("/chain")
    public CommonResult<List<DistLevelVO>> getLevelChain() {
        // 获取完整的等级链
        return success(levelService.getLevelChain());
    }
}
```

### 4.4 分销码管理

#### 4.4.1 分销码生成策略
```java
@Service
public class AgentCodeGeneratorService {
    
    private static final String CODE_PREFIX = "YT";
    private static final int CODE_LENGTH = 8;
    
    public String generateAgentCode() {
        // 生成规则：前缀 + 时间戳后4位 + 随机4位
        String timestamp = String.valueOf(System.currentTimeMillis());
        String timePart = timestamp.substring(timestamp.length() - 4);
        String randomPart = RandomStringUtils.randomAlphanumeric(4).toUpperCase();
        
        String code = CODE_PREFIX + timePart + randomPart;
        
        // 确保唯一性
        while (agentMapper.existsByAgentCode(code)) {
            randomPart = RandomStringUtils.randomAlphanumeric(4).toUpperCase();
            code = CODE_PREFIX + timePart + randomPart;
        }
        
        return code;
    }
}
```

#### 4.2.3 绑定机制实现
```java
@Service
@Transactional
public class AgentBindingService {
    
    public void bindParentAgent(Long agentId, String inviteCode) {
        // 1. 验证邀请码
        YtDistAgentDO parentAgent = agentMapper.selectByAgentCode(inviteCode);
        if (parentAgent == null || parentAgent.getStatus() != 1) {
            throw new BusinessException("无效的邀请码");
        }
        
        // 2. 检查是否已有上级
        YtDistAgentDO agent = agentMapper.selectById(agentId);
        if (agent.getParentId() != null && agent.getParentId() > 0) {
            throw new BusinessException("已绑定上级，不可重复绑定");
        }
        
        // 3. 检查循环绑定
        if (checkCircularBinding(agentId, parentAgent.getId())) {
            throw new BusinessException("不能绑定自己的下级为上级");
        }
        
        // 4. 更新绑定关系
        agent.setParentId(parentAgent.getId());
        agent.setParentCode(inviteCode);
        agent.setBindTime(new Date());
        agent.setPath(buildPath(parentAgent));
        agent.setDepth(parentAgent.getDepth() + 1);
        agentMapper.updateById(agent);
        
        // 5. 更新上级统计
        updateParentStatistics(parentAgent.getId());
    }
    
    private String buildPath(YtDistAgentDO parent) {
        if (StringUtils.isBlank(parent.getPath())) {
            return String.valueOf(parent.getId());
        }
        return parent.getPath() + "," + parent.getId();
    }
}
```

### 4.5 佣金计算方案

#### 4.5.1 佣金类型说明
系统明确区分两种佣金类型：

1. **分销奖励**（销售佣金）
   - 直接销售者获得的奖励
   - 基于销售等级方案计算
   - 追溯层级 = 0（直接销售）
   - 账单类型 = 1

2. **分销分润**（上级/介绍人收益）
   - 上级或介绍人获得的分润
   - 基于分润等级方案计算
   - 追溯层级 > 0（一级、二级、三级）
   - 账单类型 = 2

#### 4.5.2 分润节点设计
```mermaid
graph TD
    A[订单创建] --> B{订单状态变更}
    B -->|支付成功| C[触发佣金计算]
    B -->|取消/失败| D[无需计算]
    C --> E{业务类型判断}
    E -->|商品订单| F[订单完成时计算]
    E -->|优惠券| G[券码核销时计算]
    F --> H[生成待结算账单]
    G --> H
    H --> I{退款判断}
    I -->|部分退款| J[生成负数账单]
    I -->|全部退款| K[取消原账单+负数账单]
    I -->|无退款| L[正常结算]
```

#### 4.5.3 分销触发节点设计

**核心原则**：分销触发时机完全根据分销方案配置决定，系统不做硬编码，提供最大的灵活性。

1. **方案级触发配置**
   每个分销方案（销售方案/分润方案）都可以独立配置触发条件：
   ```java
   public class DistributionSchemeConfig {
       // 触发阶段：1-订单支付后，2-订单收货后，3-订单完成后，4-券码核销后
       private Integer triggerStage;
       
       // 最低订单金额
       private BigDecimal minOrderAmount;
       
       // 用户类型限制：0-不限，1-仅新用户，2-仅老用户
       private Integer userTypeLimit;
       
       // 是否启用
       private Boolean enabled;
   }
   ```

2. **分销触发判断逻辑**
   ```java
   public boolean shouldTriggerDistribution(TradeOrderDO order, DistributionScheme scheme) {
       // 1. 检查方案是否启用
       if (!scheme.getEnabled()) {
           return false;
       }
       
       // 2. 检查订单状态是否匹配触发阶段
       if (!matchTriggerStage(order.getStatus(), scheme.getTriggerStage())) {
           return false;
       }
       
       // 3. 检查最低订单金额
       if (order.getPayAmount().compareTo(scheme.getMinOrderAmount()) < 0) {
           return false;
       }
       
       // 4. 检查用户类型限制
       if (!matchUserType(order.getMemberId(), scheme.getUserTypeLimit())) {
           return false;
       }
       
       return true;
   }
   ```

3. **灵活的触发配置**
   - 不同方案可以配置不同的触发时机
   - 支持多阶段触发（如：支付后计算分销奖励，完成后计算分润）
   - 通过配置控制，无需修改代码即可调整业务规则

#### 4.5.4 冻结机制设计

1. **冻结时长配置**
   - 通过系统配置 `infra_config` 表管理冻结时长：
   ```sql
   -- 配置示例
   INSERT INTO `infra_config` (`config_key`, `config_value`, `type`, `remark`) VALUES
   ('distribution.freeze.orderPay', '30', 1, '订单支付后分销冻结天数'),
   ('distribution.freeze.orderReceive', '15', 1, '订单收货后分销冻结天数'),
   ('distribution.freeze.orderComplete', '7', 1, '订单完成后分销冻结天数'),
   ('distribution.freeze.couponRedeem', '3', 1, '券码核销后分销冻结天数');
   ```

2. **冻结状态管理**
   - 账单创建时根据触发阶段自动设置冻结时长
   - 计算冻结结束时间：`freeze_end_time = bill_time + freeze_days`
   - 冻结状态字段已包含在账单表中

3. **解冻机制**
   - 使用Quartz定时任务每天执行自动解冻（详见5.6节定时任务实现）
   - 解冻逻辑：查找所有已到期的冻结账单并批量处理
   - 解冻后自动更新分销员可提现余额

4. **退款时的冻结处理**
   - 退款发生时，原账单保持冻结状态
   - 负数账单不设置冻结（直接扣减）
   - 如果原账单已解冻，负数金额立即生效

#### 4.5.5 分润计算流程
```mermaid
graph TD
    A[业务触发] --> B[获取直接分销员]
    B --> C{是否有分销员}
    C -->|无| D[结束]
    C -->|有| E[计算分销奖励]
    E --> F[查找适用的分润方案]
    F --> G{匹配唯一方案}
    G -->|无方案| H[仅记录分销奖励]
    G -->|有方案| I[根据profit_target追溯]
    I --> J[计算各级分润]
    J --> K[生成所有账单记录]
```

#### 4.5.6 销售等级方案配置示例
```json
{
  "levelId": 1,
  "levelName": "青铜分销员",
  "saleScheme": {
    "schemeCode": "SALE_L1_001",
    "schemeName": "青铜等级销售方案",
    "commissionMode": 2,
    "commissionRate": 15.0,
    "minOrderAmount": 100,
    "maxCommission": 500
  }
}
```

#### 4.5.7 分润等级方案配置示例
```json
{
  "profitSchemes": [
    {
      "schemeCode": "PROFIT_L3_L1_1",
      "schemeName": "黄金等级一级分润方案",
      "levelId": 3,
      "sourceLevelId": null,
      "traceLevel": 1,
      "commissionMode": 2,
      "commissionRate": 5.0,
      "conditionType": 1,
      "minOrderAmount": 100
    },
    {
      "schemeCode": "PROFIT_L4_L2_2",
      "schemeName": "钻石等级二级分润方案",
      "levelId": 4,
      "sourceLevelId": 2,
      "traceLevel": 2,
      "commissionMode": 2,
      "commissionRate": 3.0,
      "conditionType": 3,
      "conditionConfig": {
        "minSourceCommission": 50,
        "minSourceCommissionRate": 15
      }
    }
  ]
}
```

#### 4.5.8 方案唯一性保证

为确保分润不会重复计算，系统设计以下机制：

1. **方案优先级**
   - 每个方案设置优先级（priority）
   - 同等条件下，优先级高的方案生效
   
2. **匹配规则**
   - 销售方案：一个等级只能有一个启用的销售方案
   - 分润方案：根据（受益等级 + 来源等级 + 追溯层级 + 分润对象）确定唯一方案
   
3. **冲突检测**
   - 创建/修改方案时检测冲突
   - 确保同一业务场景只有一个方案生效

#### 4.5.9 N级分润追溯机制

1. **无限制级别追溯**
   - 分润方案表的`trace_level`字段支持任意正整数
   - 追溯逻辑支持N级向上查找，不再限制为3级
   - 通过循环而非硬编码实现级别追溯

2. **性能优化考虑**
   - 建议合理设置追溯级别，避免过深的追溯影响性能
   - 通过缓存优化频繁的上下级查询
   - 可配置最大追溯级别作为系统保护

3. **配置示例**
   ```json
   {
     "trace_level": 5,  // 支持追溯5级
     "commission_rate": 3.0,  // 每级3%分润
     "level_id": 4  // 钻石等级才能享受
   }
   ```

#### 4.5.10 退款处理机制

1. **部分退款**
   - 生成负数金额的账单记录
   - 关联原账单ID
   - 不影响原账单状态
   
2. **全部退款**
   - 原账单状态改为"已取消"
   - 生成等额负数账单
   - 记录退款原因

3. **退款时机**
   - 订单退款审核通过时
   - 自动计算需要扣除的佣金
   - 生成退款记录和负数账单

#### 4.5.11 佣金计算核心实现
```java
@Service
public class CommissionCalculationService {
    
    @Resource
    private DistSalesSchemeMapper salesSchemeMapper;
    
    @Resource
    private DistProfitSchemeMapper profitSchemeMapper;
    
    @Resource
    private DistAgentMapper agentMapper;
    
    @Resource
    private DistAgentBillRecordMapper billRecordMapper;
    
    /**
     * 订单完成时计算佣金
     */
    public void calculateOrderCommission(TradeOrderDO order) {
        // 1. 检查订单状态
        if (order.getStatus() != TradeOrderStatusEnum.COMPLETED.getStatus()) {
            return;
        }
        
        // 2. 获取直接分销员
        Long agentId = getOrderAgentId(order);
        if (agentId == null) {
            return;
        }
        
        YtDistAgentDO sourceAgent = agentMapper.selectById(agentId);
        if (sourceAgent == null || sourceAgent.getStatus() != 1) {
            return;
        }
        
        // 3. 计算分销奖励（直接销售佣金）
        calculateDistributionReward(order, sourceAgent);
        
        // 4. 计算分销分润（上级/介绍人分润）
        calculateProfitSharing(order, sourceAgent);
    }
    
    /**
     * 优惠券核销时计算佣金
     */
    public void calculateCouponCommission(TradeCouponDO coupon) {
        // 1. 检查券码状态
        if (coupon.getStatus() != TradeCouponStatusEnum.REDEEMED.getStatus()) {
            return;
        }
        
        // 2. 获取关联订单和分销员
        TradeOrderDO order = getOrderById(coupon.getOrderId());
        YtDistAgentDO sourceAgent = getOrderAgent(order);
        if (sourceAgent == null) {
            return;
        }
        
        // 3. 生成核销佣金账单
        generateCouponBill(coupon, order, sourceAgent);
    }
    
    /**
     * 生成券码核销账单
     */
    private void generateCouponBill(TradeCouponDO coupon, TradeOrderDO order, 
                                    YtDistAgentDO agent) {
        // 1. 获取销售方案
        DistSalesSchemeDO salesScheme = getUniqueSalesScheme(agent.getLevelId());
        if (salesScheme == null) {
            return;
        }
        
        // 2. 计算佣金金额（基于订单金额）
        BigDecimal commission = calculateCommissionAmount(
            order.getPayAmount(), salesScheme
        );
        
        // 3. 生成账单记录
        YtDistAgentBillRecordDO bill = new YtDistAgentBillRecordDO();
        bill.setBillNo(generateBillNo());
        bill.setAgentId(agent.getId());
        bill.setAgentName(agent.getAgentName());
        bill.setAgentLevelId(agent.getLevelId());
        bill.setBillType(1); // 分销奖励
        bill.setBizType(2); // 优惠券核销
        bill.setBizId(coupon.getId());
        bill.setBizNo(coupon.getCouponCode());
        bill.setOrderId(order.getId()); // 关联订单ID
        bill.setOrderAmount(order.getPayAmount()); // 订单金额
        bill.setChannelOrderId(order.getChannelOrderId()); // 渠道订单号
        bill.setCouponId(coupon.getId()); // 券码ID
        bill.setCouponCode(coupon.getCouponCode()); // 券码编号
        bill.setSourceAgentId(agent.getId());
        bill.setSourceAgentName(agent.getAgentName());
        bill.setTraceLevel(0); // 直接销售
        bill.setAmount(commission);
        bill.setBaseAmount(order.getPayAmount());
        bill.setRate(salesScheme.getCommissionRate());
        bill.setSchemeId(salesScheme.getId());
        bill.setSchemeType(1); // 销售方案
        bill.setSchemeSnapshot(JSON.toJSONString(salesScheme));
        bill.setTriggerStage(4); // 券码核销后
        bill.setFreezeStatus(1); // 冻结中
        bill.setFreezeDays(getFreezeDays(4)); // 券码核销的冻结天数
        bill.setFreezeEndTime(calculateFreezeEndTime(new Date(), bill.getFreezeDays()));
        bill.setStatus(0); // 待结算
        bill.setBillTime(new Date());
        bill.setRemark("券码核销：" + coupon.getCouponCode());
        
        billRecordMapper.insert(bill);
        
        // 4. 计算分润
        calculateProfitSharing(order, agent);
    }
    
    /**
     * 计算分销奖励（直接销售）
     */
    private void calculateDistributionReward(TradeOrderDO order, YtDistAgentDO agent) {
        // 1. 获取销售方案
        DistSalesSchemeDO salesScheme = getUniqueSalesScheme(agent.getLevelId());
        if (salesScheme == null || !isSchemeApplicable(salesScheme, order)) {
            return;
        }
        
        // 2. 计算佣金金额
        BigDecimal commission = calculateCommissionAmount(
            order.getPayAmount(), salesScheme
        );
        
        // 3. 生成账单记录
        YtDistAgentBillRecordDO bill = new YtDistAgentBillRecordDO();
        bill.setBillNo(generateBillNo());
        bill.setAgentId(agent.getId());
        bill.setAgentName(agent.getAgentName());
        bill.setAgentLevelId(agent.getLevelId());
        bill.setBillType(1); // 分销奖励
        bill.setBizType(1); // 商品订单
        bill.setBizId(order.getId());
        bill.setBizNo(order.getOrderNo());
        bill.setOrderId(order.getId()); // 订单ID
        bill.setOrderAmount(order.getPayAmount()); // 订单金额
        bill.setChannelOrderId(order.getChannelOrderId()); // 渠道订单号
        bill.setSourceAgentId(agent.getId());
        bill.setSourceAgentName(agent.getAgentName());
        bill.setTraceLevel(0); // 直接销售
        bill.setAmount(commission);
        bill.setBaseAmount(order.getPayAmount());
        bill.setRate(salesScheme.getCommissionRate());
        bill.setSchemeId(salesScheme.getId());
        bill.setSchemeType(1); // 销售方案
        bill.setSchemeSnapshot(JSON.toJSONString(salesScheme));
        bill.setTriggerStage(getTriggerStage(order)); // 获取触发阶段
        bill.setFreezeStatus(1); // 冻结中
        bill.setFreezeDays(getFreezeDays(bill.getTriggerStage())); // 获取冻结天数
        bill.setFreezeEndTime(calculateFreezeEndTime(bill.getBillTime(), bill.getFreezeDays()));
        bill.setStatus(0); // 待结算
        bill.setBillTime(new Date());
        
        billRecordMapper.insert(bill);
    }
    
    /**
     * 获取触发阶段
     */
    private Integer getTriggerStage(TradeOrderDO order) {
        // 根据订单状态判断触发阶段
        if (order.getStatus() == TradeOrderStatusEnum.SUCCESS.getStatus()) {
            return 1; // 订单支付后
        } else if (order.getStatus() == TradeOrderStatusEnum.COMPLETED.getStatus()) {
            return 3; // 订单完成后
        }
        return 1; // 默认为支付后
    }
    
    /**
     * 获取冻结天数
     */
    private Integer getFreezeDays(Integer triggerStage) {
        String configKey = "";
        switch (triggerStage) {
            case 1:
                configKey = "distribution.freeze.orderPay";
                break;
            case 2:
                configKey = "distribution.freeze.orderReceive";
                break;
            case 3:
                configKey = "distribution.freeze.orderComplete";
                break;
            case 4:
                configKey = "distribution.freeze.couponRedeem";
                break;
        }
        
        // 从系统配置中获取冻结天数
        String freezeDays = configService.getConfigValueByKey(configKey);
        return freezeDays != null ? Integer.parseInt(freezeDays) : 7; // 默认7天
    }
    
    /**
     * 计算冻结结束时间
     */
    private Date calculateFreezeEndTime(Date billTime, Integer freezeDays) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(billTime);
        calendar.add(Calendar.DAY_OF_MONTH, freezeDays);
        return calendar.getTime();
    }
    
    /**
     * 计算分销分润（上级/介绍人）
     */
    private void calculateProfitSharing(TradeOrderDO order, YtDistAgentDO sourceAgent) {
        // 1. 获取唯一适用的分润方案
        List<DistProfitSchemeDO> schemes = getApplicableProfitSchemes(sourceAgent);
        
        // 2. 按追溯层级分组，每个层级只能有一个方案
        Map<Integer, DistProfitSchemeDO> uniqueSchemes = selectUniqueSchemes(schemes);
        
        // 3. 根据方案计算各级分润
        for (DistProfitSchemeDO scheme : uniqueSchemes.values()) {
            calculateProfitByScheme(order, sourceAgent, scheme);
        }
    }
    
    /**
     * 根据分润方案计算分润
     */
    private List<CommissionDetail> calculateProfitByScheme(
            YtDistAgentDO sourceAgent,
            OrderDTO order,
            DistProfitSchemeDO scheme) {
        
        List<CommissionDetail> details = new ArrayList<>();
        
        // 根据分润对象类型决定追溯路径
        switch (scheme.getProfitTarget()) {
            case 1: // 仅上级
                calculateParentProfit(sourceAgent, order, scheme, details);
                break;
            case 2: // 仅介绍人
                calculateReferrerProfit(sourceAgent, order, scheme, details);
                break;
            case 3: // 两者都有
                ProfitConfig config = JSON.parseObject(scheme.getProfitConfig(), ProfitConfig.class);
                if (config != null) {
                    // 计算上级分润
                    if (config.getParent() != null) {
                        calculateParentProfitWithConfig(sourceAgent, order, scheme, config.getParent(), details);
                    }
                    // 计算介绍人分润
                    if (config.getReferrer() != null) {
                        calculateReferrerProfitWithConfig(sourceAgent, order, scheme, config.getReferrer(), details);
                    }
                }
                break;
        }
        
        return details;
    }
    
    /**
     * 计算销售佣金
     */
    private CommissionDetail calculateSalesCommission(
            YtDistAgentDO agent, 
            OrderDTO order, 
            DistSalesSchemeDO scheme) {
        
        CommissionDetail detail = new CommissionDetail();
        detail.setAgentId(agent.getId());
        detail.setAgentName(agent.getAgentName());
        detail.setLevelId(agent.getLevelId());
        detail.setTraceLevel(0);
        detail.setCommissionType("SALES"); // 销售佣金
        
        // 检查最低订单金额
        if (order.getAmount().compareTo(scheme.getMinOrderAmount()) < 0) {
            detail.setAmount(BigDecimal.ZERO);
            return detail;
        }
        
        BigDecimal amount = BigDecimal.ZERO;
        if (scheme.getCommissionMode() == 1) {
            amount = scheme.getCommissionAmount();
        } else {
            amount = order.getAmount()
                .multiply(scheme.getCommissionRate())
                .divide(new BigDecimal(100), 2, RoundingMode.DOWN);
        }
        
        // 应用最高佣金限制
        if (scheme.getMaxCommission() != null 
            && amount.compareTo(scheme.getMaxCommission()) > 0) {
            amount = scheme.getMaxCommission();
        }
        
        detail.setAmount(amount);
        detail.setSchemeId(scheme.getId());
        detail.setSchemeName(scheme.getSchemeName());
        
        return detail;
    }
    
    /**
     * 计算上级分润
     */
    private void calculateParentProfit(
            YtDistAgentDO sourceAgent,
            OrderDTO order,
            DistProfitSchemeDO scheme,
            List<CommissionDetail> details) {
        
        YtDistAgentDO currentAgent = sourceAgent;
        int currentLevel = 0;
        
        // 按照上级链路追溯，支持N级追溯
        while (currentLevel < scheme.getTraceLevel()) {
            if (currentAgent.getParentId() != null && currentAgent.getParentId() > 0) {
                currentAgent = agentMapper.selectById(currentAgent.getParentId());
                currentLevel++;
                
                if (currentAgent != null && currentAgent.getStatus() == 1) {
                    // 检查是否符合受益等级
                    if (currentAgent.getLevelId().equals(scheme.getLevelId())) {
                        CommissionDetail detail = calculateCommissionAmount(
                            currentAgent, order, scheme, "PROFIT_PARENT", currentLevel
                        );
                        if (detail.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                            details.add(detail);
                        }
                    }
                }
            } else {
                break;
            }
        }
    }
    
    /**
     * 计算介绍人分润
     */
    private void calculateReferrerProfit(
            YtDistAgentDO sourceAgent,
            OrderDTO order,
            DistProfitSchemeDO scheme,
            List<CommissionDetail> details) {
        
        YtDistAgentDO currentAgent = sourceAgent;
        int currentLevel = 0;
        
        // 按照介绍人链路追溯，支持N级追溯
        while (currentLevel < scheme.getTraceLevel()) {
            if (currentAgent.getReferrerId() != null && currentAgent.getReferrerId() > 0) {
                currentAgent = agentMapper.selectById(currentAgent.getReferrerId());
                currentLevel++;
                
                if (currentAgent != null && currentAgent.getStatus() == 1) {
                    // 检查是否符合受益等级
                    if (currentAgent.getLevelId().equals(scheme.getLevelId())) {
                        CommissionDetail detail = calculateCommissionAmount(
                            currentAgent, order, scheme, "PROFIT_REFERRER", currentLevel
                        );
                        if (detail.getAmount().compareTo(BigDecimal.ZERO) > 0) {
                            details.add(detail);
                        }
                    }
                }
            } else {
                break;
            }
        }
    }
    
    /**
     * 计算佣金金额
     */
    private CommissionDetail calculateCommissionAmount(
            YtDistAgentDO agent,
            OrderDTO order,
            DistProfitSchemeDO scheme,
            String commissionType,
            int traceLevel) {
        
        CommissionDetail detail = new CommissionDetail();
        detail.setAgentId(agent.getId());
        detail.setAgentName(agent.getAgentName());
        detail.setLevelId(agent.getLevelId());
        detail.setTraceLevel(traceLevel);
        detail.setCommissionType(commissionType);
        
        // 检查最低订单金额
        if (order.getAmount().compareTo(scheme.getMinOrderAmount()) < 0) {
            detail.setAmount(BigDecimal.ZERO);
            return detail;
        }
        
        BigDecimal amount = BigDecimal.ZERO;
        if (scheme.getCommissionMode() == 1) {
            amount = scheme.getCommissionAmount();
        } else {
            amount = order.getAmount()
                .multiply(scheme.getCommissionRate())
                .divide(new BigDecimal(100), 2, RoundingMode.DOWN);
        }
        
        // 应用最高佣金限制
        if (scheme.getMaxCommission() != null 
            && amount.compareTo(scheme.getMaxCommission()) > 0) {
            amount = scheme.getMaxCommission();
        }
        
        detail.setAmount(amount);
        detail.setSchemeId(scheme.getId());
        detail.setSchemeName(scheme.getSchemeName());
        
        return detail;
    }
}
```

#### 4.5.12 优化退款处理机制

**设计说明**：取消独立的退款记录表，直接通过账单表的负数记录处理退款，简化设计并避免数据冗余。

1. **退款处理策略**
   - 订单退款直接监听订单售后状态变更
   - 根据退款比例生成对应的负数账单
   - 利用现有的账单表统一管理所有收支记录

2. **退款处理实现**
```java
@Service
public class CommissionRefundService {
    
    @Resource
    private DistAgentBillRecordMapper billRecordMapper;
    
    @Resource
    private DistAgentBalanceService balanceService;
    
    /**
     * 处理订单退款（监听订单售后状态变更）
     */
    @EventListener
    @Async
    public void handleOrderRefund(OrderRefundEvent event) {
        TradeOrderDO order = event.getOrder();
        
        // 1. 查找原订单相关的所有佣金账单
        List<YtDistAgentBillRecordDO> originalBills = billRecordMapper.selectList(
            new LambdaQueryWrapper<YtDistAgentBillRecordDO>()
                .eq(YtDistAgentBillRecordDO::getBizId, order.getId())
                .eq(YtDistAgentBillRecordDO::getBizType, 1)
                .gt(YtDistAgentBillRecordDO::getAmount, 0) // 仅处理正数账单
        );
        
        if (CollectionUtils.isEmpty(originalBills)) {
            return;
        }
        
        // 2. 计算退款比例
        BigDecimal refundRatio = order.getRefundAmount()
            .divide(order.getPayAmount(), 4, RoundingMode.DOWN);
        
        // 3. 批量生成负数账单
        List<YtDistAgentBillRecordDO> refundBills = new ArrayList<>();
        for (YtDistAgentBillRecordDO originalBill : originalBills) {
            YtDistAgentBillRecordDO refundBill = createRefundBill(
                originalBill, refundRatio, order.getRefundStatus()
            );
            refundBills.add(refundBill);
        }
        
        // 4. 批量插入退款账单
        billRecordMapper.insertBatch(refundBills);
        
        // 5. 更新相关分销员余额
        updateAgentBalances(refundBills);
    }
    
    /**
     * 创建退款账单
     */
    private YtDistAgentBillRecordDO createRefundBill(
            YtDistAgentBillRecordDO originalBill, 
            BigDecimal refundRatio,
            Integer refundStatus) {
        
        // 计算退款金额
        BigDecimal refundAmount = originalBill.getAmount()
            .multiply(refundRatio)
            .setScale(2, RoundingMode.DOWN);
        
        YtDistAgentBillRecordDO refundBill = new YtDistAgentBillRecordDO();
        BeanUtils.copyProperties(originalBill, refundBill);
        refundBill.setId(null);
        refundBill.setBillNo(generateRefundBillNo());
        refundBill.setAmount(refundAmount.negate()); // 负数
        refundBill.setFreezeStatus(0); // 退款账单不冻结
        refundBill.setFreezeDays(0);
        refundBill.setFreezeEndTime(null);
        refundBill.setUnfreezeTime(new Date());
        refundBill.setStatus(1); // 直接已结算
        refundBill.setSettleTime(new Date());
        refundBill.setBillTime(new Date());
        refundBill.setRemark(buildRefundRemark(refundStatus, refundRatio));
        
        return refundBill;
    }
    
    /**
     * 构建退款备注
     */
    private String buildRefundRemark(Integer refundStatus, BigDecimal refundRatio) {
        String refundType = refundStatus == 20 ? "全部退款" : "部分退款";
        String ratio = refundRatio.multiply(new BigDecimal(100))
            .setScale(2, RoundingMode.DOWN) + "%";
        return String.format("订单%s，退款比例：%s", refundType, ratio);
    }
}
```

#### 4.5.13 分销员余额管理设计

采用独立的余额表设计，避免频繁计算和重复统计：

```java
@Service
public class DistAgentBalanceService {
    
    @Resource
    private DistAgentBalanceMapper balanceMapper;
    
    @Resource
    private DistAgentBillRecordMapper billRecordMapper;
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 更新分销员余额（使用乐观锁）
     */
    @Transactional
    public void updateAgentBalance(Long agentId, BigDecimal amount, String reason) {
        String lockKey = "dist:balance:lock:" + agentId;
        Boolean acquired = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, "1", Duration.ofSeconds(30));
        
        if (!acquired) {
            throw new BusinessException("余额更新中，请稍后再试");
        }
        
        try {
            YtDistAgentBalanceDO balance = balanceMapper.selectByAgentId(agentId);
            if (balance == null) {
                balance = initAgentBalance(agentId);
            }
            
            // 乐观锁更新
            YtDistAgentBalanceDO updateBalance = new YtDistAgentBalanceDO();
            updateBalance.setId(balance.getId());
            updateBalance.setBalanceVersion(balance.getBalanceVersion() + 1);
            
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                // 收入：增加冻结余额
                updateBalance.setTotalCommission(
                    balance.getTotalCommission().add(amount)
                );
                updateBalance.setFrozenBalance(
                    balance.getFrozenBalance().add(amount)
                );
            } else {
                // 支出：扣减可提现余额
                updateBalance.setAvailableBalance(
                    balance.getAvailableBalance().add(amount)
                );
            }
            
            int updated = balanceMapper.updateByIdAndVersion(
                updateBalance, balance.getBalanceVersion()
            );
            
            if (updated == 0) {
                throw new BusinessException("余额更新失败，请重试");
            }
            
        } finally {
            redisTemplate.delete(lockKey);
        }
    }
    
    /**
     * 解冻佣金（定时任务调用）
     */
    @Transactional
    public void unfreezeCommission(Long agentId, Long billId, BigDecimal amount) {
        YtDistAgentBalanceDO balance = balanceMapper.selectByAgentId(agentId);
        if (balance == null) {
            return;
        }
        
        YtDistAgentBalanceDO updateBalance = new YtDistAgentBalanceDO();
        updateBalance.setId(balance.getId());
        updateBalance.setBalanceVersion(balance.getBalanceVersion() + 1);
        updateBalance.setFrozenBalance(
            balance.getFrozenBalance().subtract(amount)
        );
        updateBalance.setAvailableBalance(
            balance.getAvailableBalance().add(amount)
        );
        updateBalance.setLastBillId(billId);
        updateBalance.setLastCalculateTime(new Date());
        
        balanceMapper.updateByIdAndVersion(
            updateBalance, balance.getBalanceVersion()
        );
    }
    
    /**
     * 获取可提现金额（避免重复计算）
     */
    public BigDecimal getWithdrawableAmount(Long agentId) {
        YtDistAgentBalanceDO balance = balanceMapper.selectByAgentId(agentId);
        if (balance == null) {
            return BigDecimal.ZERO;
        }
        
        return balance.getAvailableBalance().subtract(balance.getWithdrawingBalance());
    }
    
    /**
     * 冻结可提现余额（提现申请时调用）
     * 使用MySQL原子操作，避免并发问题
     */
    @Transactional
    public void freezeWithdrawableBalance(Long agentId, BigDecimal amount) {
        // 使用MySQL的原子更新操作
        int updated = balanceMapper.freezeBalance(agentId, amount);
        if (updated == 0) {
            throw new BusinessException("余额不足或更新失败");
        }
    }
    
    /**
     * 解冻可提现余额（提现审核拒绝时调用）
     * 使用MySQL原子操作
     */
    @Transactional
    public void unfreezeWithdrawableBalance(Long agentId, BigDecimal amount) {
        // 使用MySQL的原子更新操作
        int updated = balanceMapper.unfreezeBalance(agentId, amount);
        if (updated == 0) {
            throw new BusinessException("解冻余额失败");
        }
    }
}
```

#### 4.5.14 MySQL原子操作实现

为了确保余额操作的原子性和并发安全，在Mapper层实现原子更新：

```java
@Mapper
public interface DistAgentBalanceMapper extends BaseMapper<YtDistAgentBalanceDO> {
    
    /**
     * 根据分销员ID查询余额
     */
    YtDistAgentBalanceDO selectByAgentId(@Param("agentId") Long agentId);
    
    /**
     * 使用乐观锁更新余额
     */
    int updateByIdAndVersion(@Param("entity") YtDistAgentBalanceDO entity, 
                            @Param("oldVersion") Long oldVersion);
    
    /**
     * 冻结可提现余额（原子操作）
     * SQL: UPDATE yt_dist_agent_balance 
     *      SET available_balance = available_balance - #{amount},
     *          withdrawing_balance = withdrawing_balance + #{amount},
     *          update_time = NOW()
     *      WHERE agent_id = #{agentId} 
     *      AND available_balance >= #{amount}
     */
    @Update("UPDATE yt_dist_agent_balance " +
            "SET available_balance = available_balance - #{amount}, " +
            "    withdrawing_balance = withdrawing_balance + #{amount}, " +
            "    update_time = NOW() " +
            "WHERE agent_id = #{agentId} AND available_balance >= #{amount}")
    int freezeBalance(@Param("agentId") Long agentId, @Param("amount") BigDecimal amount);
    
    /**
     * 解冻可提现余额（原子操作）
     * SQL: UPDATE yt_dist_agent_balance 
     *      SET available_balance = available_balance + #{amount},
     *          withdrawing_balance = withdrawing_balance - #{amount},
     *          update_time = NOW()
     *      WHERE agent_id = #{agentId} 
     *      AND withdrawing_balance >= #{amount}
     */
    @Update("UPDATE yt_dist_agent_balance " +
            "SET available_balance = available_balance + #{amount}, " +
            "    withdrawing_balance = withdrawing_balance - #{amount}, " +
            "    update_time = NOW() " +
            "WHERE agent_id = #{agentId} AND withdrawing_balance >= #{amount}")
    int unfreezeBalance(@Param("agentId") Long agentId, @Param("amount") BigDecimal amount);
    
    /**
     * 完成提现（原子操作）
     */
    @Update("UPDATE yt_dist_agent_balance " +
            "SET withdrawing_balance = withdrawing_balance - #{amount}, " +
            "    withdrawn_balance = withdrawn_balance + #{amount}, " +
            "    update_time = NOW() " +
            "WHERE agent_id = #{agentId} AND withdrawing_balance >= #{amount}")
    int completeWithdraw(@Param("agentId") Long agentId, @Param("amount") BigDecimal amount);
}
```

#### 4.5.15 统一分销码生成机制

采用Redis+数据库的方式确保唯一性：

```java
@Component
public class DistributionCodeGenerator {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private DistAgentMapper agentMapper;
    
    @Resource
    private DistLevelMapper levelMapper;
    
    private static final String REDIS_KEY_AGENT_CODE = "dist:code:agent";
    private static final String REDIS_KEY_LEVEL_CODE = "dist:code:level";
    private static final String CODE_PREFIX = "YT";
    private static final int MAX_RETRY = 10;
    
    /**
     * 生成分销员邀请码
     */
    public String generateAgentCode() {
        return generateUniqueCode(REDIS_KEY_AGENT_CODE, this::checkAgentCodeExists);
    }
    
    /**
     * 生成等级邀请码
     */
    public String generateLevelCode() {
        return generateUniqueCode(REDIS_KEY_LEVEL_CODE, this::checkLevelCodeExists);
    }
    
    /**
     * 统一的唯一码生成逻辑
     */
    private String generateUniqueCode(String redisKey, Function<String, Boolean> checkExists) {
        for (int i = 0; i < MAX_RETRY; i++) {
            String code = generateRandomCode();
            
            // 1. Redis预检查（快速排重）
            Boolean exists = redisTemplate.opsForSet().isMember(redisKey, code);
            if (exists) {
                continue;
            }
            
            // 2. 数据库最终检查
            if (checkExists.apply(code)) {
                // 同步到Redis
                redisTemplate.opsForSet().add(redisKey, code);
                continue;
            }
            
            // 3. 预占位Redis
            redisTemplate.opsForSet().add(redisKey, code);
            redisTemplate.expire(redisKey, Duration.ofDays(30));
            
            return code;
        }
        
        throw new BusinessException("生成唯一码失败，请重试");
    }
    
    /**
     * 生成随机码
     */
    private String generateRandomCode() {
        StringBuilder code = new StringBuilder(CODE_PREFIX);
        Random random = new Random();
        
        for (int i = 0; i < 8; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }
    
    /**
     * 检查分销员码是否存在
     */
    private Boolean checkAgentCodeExists(String code) {
        return agentMapper.selectCount(
            new LambdaQueryWrapper<YtDistAgentDO>()
                .eq(YtDistAgentDO::getAgentCode, code)
        ) > 0;
    }
    
    /**
     * 检查等级码是否存在
     */
    private Boolean checkLevelCodeExists(String code) {
        return levelMapper.selectCount(
            new LambdaQueryWrapper<YtDistLevelDO>()
                .eq(YtDistLevelDO::getInviteCode, code)
        ) > 0;
    }
    
    /**
     * 初始化Redis缓存（应用启动时调用）
     */
    @PostConstruct
    public void initRedisCache() {
        // 加载现有分销员码到Redis
        List<YtDistAgentDO> agents = agentMapper.selectList(
            new LambdaQueryWrapper<YtDistAgentDO>()
                .select(YtDistAgentDO::getAgentCode)
        );
        
        if (!agents.isEmpty()) {
            String[] codes = agents.stream()
                .map(YtDistAgentDO::getAgentCode)
                .toArray(String[]::new);
            redisTemplate.opsForSet().add(REDIS_KEY_AGENT_CODE, codes);
        }
        
        // 加载现有等级码到Redis
        List<YtDistLevelDO> levels = levelMapper.selectList(
            new LambdaQueryWrapper<YtDistLevelDO>()
                .select(YtDistLevelDO::getInviteCode)
                .isNotNull(YtDistLevelDO::getInviteCode)
        );
        
        if (!levels.isEmpty()) {
            String[] levelCodes = levels.stream()
                .map(YtDistLevelDO::getInviteCode)
                .toArray(String[]::new);
            redisTemplate.opsForSet().add(REDIS_KEY_LEVEL_CODE, levelCodes);
        }
    }
}
```

#### 4.5.16 一单一券码业务逻辑

系统支持一单一券码的业务模式，主要特点：

1. **券码生成机制**
   - 订单支付成功后自动生成唯一券码
   - 券码与订单一对一绑定
   - 券码状态与订单状态联动

2. **分销触发时机**
   - 订单支付成功，券码未核销：根据订单状态触发分销
   - 订单支付成功，券码已核销：以券码核销为触发点
   - 防止重复计算：同一订单只能产生一次分销收益

3. **实现逻辑**
   ```java
   @Service
   public class DistributionTriggerService {
       
       /**
        * 处理订单状态变更
        */
       public void handleOrderStatusChange(TradeOrderDO order) {
           // 检查是否已经计算过佣金
           if (hasCalculatedCommission(order.getId(), 1)) {
               return;
           }
           
           // 获取分销配置
           DistributionConfig config = getDistributionConfig();
           if (!config.isEnabled()) {
               return;
           }
           
           // 检查最低金额
           if (order.getPayAmount().compareTo(config.getMinAmount()) < 0) {
               return;
           }
           
           // 根据配置的触发阶段处理
           if (shouldTriggerDistribution(order.getStatus(), config.getTriggerStage())) {
               calculateOrderCommission(order);
           }
       }
       
       /**
        * 处理券码核销
        */
       public void handleCouponRedeem(TradeCouponDO coupon) {
           // 检查是否已经计算过佣金
           if (hasCalculatedCommission(coupon.getId(), 2)) {
               return;
           }
           
           // 券码核销时计算佣金
           calculateCouponCommission(coupon);
       }
       
       /**
        * 检查是否已计算佣金
        */
       private boolean hasCalculatedCommission(Long bizId, Integer bizType) {
           return billRecordMapper.selectCount(
               new LambdaQueryWrapper<YtDistAgentBillRecordDO>()
                   .eq(YtDistAgentBillRecordDO::getBizId, bizId)
                   .eq(YtDistAgentBillRecordDO::getBizType, bizType)
                   .ne(YtDistAgentBillRecordDO::getAmount, 0) // 排除退款负数账单
           ) > 0;
       }
   }
   ```

4. **防重机制**
   - 通过业务ID和业务类型确保唯一性
   - 退款产生的负数账单不影响防重判断
   - 支持订单和券码两种触发方式，但只计算一次

#### 4.5.17 Quartz定时任务设计

统一使用Quartz框架管理所有分销系统的定时任务：

```java
/**
 * 佣金解冻定时任务
 */
@Component
@DisallowConcurrentExecution
public class CommissionUnfreezeJob implements Job {
    
    @Resource
    private DistAgentBillRecordService billRecordService;
    
    @Resource
    private DistAgentBalanceService balanceService;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行佣金解冻任务");
        
        try {
            // 1. 查询需要解冻的账单
            List<YtDistAgentBillRecordDO> frozenBills = billRecordService.selectUnfreezableBills();
            
            if (CollectionUtils.isEmpty(frozenBills)) {
                log.info("暂无需要解冻的佣金账单");
                return;
            }
            
            // 2. 按分销员分组处理
            Map<Long, List<YtDistAgentBillRecordDO>> agentBillsMap = frozenBills.stream()
                .collect(Collectors.groupingBy(YtDistAgentBillRecordDO::getAgentId));
            
            // 3. 批量处理解冻
            for (Map.Entry<Long, List<YtDistAgentBillRecordDO>> entry : agentBillsMap.entrySet()) {
                Long agentId = entry.getKey();
                List<YtDistAgentBillRecordDO> bills = entry.getValue();
                
                processAgentUnfreeze(agentId, bills);
            }
            
            log.info("佣金解冻任务执行完成，处理账单数量：{}", frozenBills.size());
            
        } catch (Exception e) {
            log.error("佣金解冻任务执行失败", e);
            throw e;
        }
    }
    
    /**
     * 处理单个分销员的佣金解冻
     */
    private void processAgentUnfreeze(Long agentId, List<YtDistAgentBillRecordDO> bills) {
        try {
            BigDecimal totalUnfreezeAmount = bills.stream()
                .map(YtDistAgentBillRecordDO::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 1. 批量更新账单状态
            billRecordService.batchUnfreezeBills(bills);
            
            // 2. 更新分销员余额
            balanceService.unfreezeCommission(agentId, totalUnfreezeAmount);
            
            log.info("分销员[{}]佣金解冻成功，解冻金额：{}", agentId, totalUnfreezeAmount);
            
        } catch (Exception e) {
            log.error("分销员[{}]佣金解冻失败", agentId, e);
        }
    }
}

/**
 * 分销员等级升降级任务
 */
@Component
@DisallowConcurrentExecution
public class AgentLevelUpgradeJob implements Job {
    
    @Resource
    private DistAgentService agentService;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行分销员等级升降级任务");
        
        // 1. 获取需要检查等级的分销员
        List<YtDistAgentDO> agents = agentService.selectActiveAgents();
        
        // 2. 批量处理等级变更
        for (YtDistAgentDO agent : agents) {
            try {
                agentService.checkAndUpdateAgentLevel(agent);
            } catch (Exception e) {
                log.error("分销员[{}]等级检查失败", agent.getId(), e);
            }
        }
        
        log.info("分销员等级升降级任务执行完成");
    }
}

/**
 * 余额校对任务
 */
@Component
@DisallowConcurrentExecution
public class BalanceReconciliationJob implements Job {
    
    @Resource
    private DistAgentBalanceService balanceService;
    
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("开始执行余额校对任务");
        
        // 执行余额校对逻辑
        balanceService.reconcileAllAgentBalances();
        
        log.info("余额校对任务执行完成");
    }
}

/**
 * Quartz任务调度配置
 */
@Configuration
public class DistributionQuartzConfig {
    
    @Bean
    public JobDetail commissionUnfreezeJobDetail() {
        return JobBuilder.newJob(CommissionUnfreezeJob.class)
            .withIdentity("commissionUnfreezeJob", "distribution")
            .storeDurably()
            .build();
    }
    
    @Bean
    public Trigger commissionUnfreezeTrigger() {
        return TriggerBuilder.newTrigger()
            .forJob(commissionUnfreezeJobDetail())
            .withIdentity("commissionUnfreezeTrigger", "distribution")
            .withSchedule(CronScheduleBuilder.cronSchedule("0 0 2 * * ?")) // 每天凌晨2点
            .build();
    }
    
    @Bean
    public JobDetail agentLevelUpgradeJobDetail() {
        return JobBuilder.newJob(AgentLevelUpgradeJob.class)
            .withIdentity("agentLevelUpgradeJob", "distribution")
            .storeDurably()
            .build();
    }
    
    @Bean
    public Trigger agentLevelUpgradeTrigger() {
        return TriggerBuilder.newTrigger()
            .forJob(agentLevelUpgradeJobDetail())
            .withIdentity("agentLevelUpgradeTrigger", "distribution")
            .withSchedule(CronScheduleBuilder.cronSchedule("0 0 3 * * ?")) // 每天凌晨3点
            .build();
    }
    
    @Bean
    public JobDetail balanceReconciliationJobDetail() {
        return JobBuilder.newJob(BalanceReconciliationJob.class)
            .withIdentity("balanceReconciliationJob", "distribution")
            .storeDurably()
            .build();
    }
    
    @Bean
    public Trigger balanceReconciliationTrigger() {
        return TriggerBuilder.newTrigger()
            .forJob(balanceReconciliationJobDetail())
            .withIdentity("balanceReconciliationTrigger", "distribution")
            .withSchedule(CronScheduleBuilder.cronSchedule("0 30 3 * * ?")) // 每天凌晨3点30分
            .build();
    }
}
```

#### 4.5.18 TradeOrderDO扩展设计

建议在TradeOrderDO中仅增加分销员分销码字段：

```java
public class TradeOrderDO {
    // 现有字段...
    
    // ################## 分销相关 ##################
    /**
     * 分销员分销码
     */
    private String distributorCode;
}
```

**设计理念**：
- **最小化改动**：仅增加一个必要字段，减少对现有系统的影响
- **追溯便利**：通过分销码可以反查分销员信息和分销方案
- **灵活扩展**：业务需要时可通过分销码关联查询详细分销信息

**使用方式**：
```java
// 订单创建时记录分销码
if (StringUtils.isNotEmpty(distributorCode)) {
    order.setDistributorCode(distributorCode);
}

// 分销计算时查询分销员
YtDistAgentDO agent = agentMapper.selectOne(
    new LambdaQueryWrapper<YtDistAgentDO>()
        .eq(YtDistAgentDO::getAgentCode, order.getDistributorCode())
);
```

#### 4.5.19 分销员提现功能设计

系统支持完整的提现管理流程，包括申请、审核、打款、凭证回填等环节。

1. **提现条件控制**
   ```sql
   -- 系统配置示例
   INSERT INTO `infra_config` (`config_key`, `config_value`, `type`, `remark`) VALUES
   ('distribution.withdraw.minAmount', '100', 2, '最低提现金额'),
   ('distribution.withdraw.maxAmount', '50000', 2, '单次最高提现金额'),
   ('distribution.withdraw.taxRate', '20', 2, '代扣申报税率（%）'),
   ('distribution.withdraw.enabled', 'true', 4, '是否启用提现功能');
   ```

2. **提现流程设计**
   ```mermaid
   graph TD
       A[分销员提交申请] --> B{检查条件}
       B -->|条件不符| C[申请失败]
       B -->|条件符合| D[创建提现申请]
       D --> E[财务审核]
       E -->|审核拒绝| F[申请结束]
       E -->|审核通过| G{发票类型}
       G -->|有发票| H[按发票金额打款]
       G -->|无发票| I[代扣申报打款]
       H --> J[财务打款]
       I --> K[计算税后金额]
       K --> J
       J --> L[回填打款凭证]
       L --> M[提现完成]
   ```

3. **税务处理机制**
   - **有发票情况**：按发票金额进行打款，分销员自行处理税务
   - **无发票情况**：系统代扣申报，扣除税费后打款给分销员
   - 税率通过系统配置管理，支持动态调整
   - 记录税务申报信息，便于后续查询和统计

4. **提现实现核心代码**
   ```java
   @Service
   public class AgentWithdrawService {
       
       /**
        * 提交提现申请
        */
       @Transactional
       public WithdrawApplyResult applyWithdraw(WithdrawApplyDTO request) {
           // 1. 校验分销员状态
           YtDistAgentDO agent = validateAgent(request.getAgentId());
           
           // 2. 校验提现条件
           validateWithdrawConditions(agent, request.getApplyAmount());
           
           // 3. 获取收款账户信息
           YtDistAgentAccountDO account = getWithdrawAccount(
               agent.getId(), request.getAccountId()
           );
           
           // 4. 计算税费和实际金额
           WithdrawCalculation calculation = calculateWithdrawAmount(
               request.getApplyAmount(), request.getInvoiceType()
           );
           
           // 5. 创建提现申请
           YtDistAgentWithdrawApplyDO withdraw = createWithdrawApply(
               agent, request, account, calculation
           );
           
           // 6. 冻结可提现余额
           freezeWithdrawableBalance(agent.getId(), request.getApplyAmount());
           
           // 7. 创建提现明细
           createWithdrawDetails(withdraw.getId(), agent.getId(), 
                               request.getApplyAmount());
           
           return buildApplyResult(withdraw, calculation);
       }
       
       /**
        * 计算提现金额
        */
       private WithdrawCalculation calculateWithdrawAmount(
               BigDecimal applyAmount, Integer invoiceType) {
           
           WithdrawCalculation calculation = new WithdrawCalculation();
           calculation.setApplyAmount(applyAmount);
           
           if (invoiceType == 1) { // 无发票，代扣申报
               BigDecimal taxRate = getTaxRate();
               BigDecimal taxAmount = applyAmount
                   .multiply(taxRate)
                   .divide(new BigDecimal(100), 2, RoundingMode.DOWN);
               BigDecimal actualAmount = applyAmount.subtract(taxAmount);
               
               calculation.setTaxRate(taxRate);
               calculation.setTaxAmount(taxAmount);
               calculation.setActualAmount(actualAmount);
           } else { // 有发票
               calculation.setTaxRate(BigDecimal.ZERO);
               calculation.setTaxAmount(BigDecimal.ZERO);
               calculation.setActualAmount(applyAmount);
           }
           
           return calculation;
       }
       
       /**
        * 财务审核
        */
       @Transactional
       public void auditWithdraw(Long withdrawId, Integer auditResult, 
                                String auditRemark, String auditor) {
           YtDistAgentWithdrawApplyDO withdraw = withdrawMapper.selectById(withdrawId);
           if (withdraw == null || withdraw.getStatus() != 0) {
               throw new BusinessException("提现申请状态异常");
           }
           
           withdraw.setStatus(auditResult); // 1-通过，2-拒绝
           withdraw.setAuditTime(new Date());
           withdraw.setAuditUser(auditor);
           withdraw.setAuditRemark(auditRemark);
           
           if (auditResult == 2) { // 审核拒绝
               // 解冻余额
               unfreezeWithdrawableBalance(withdraw.getAgentId(), 
                                         withdraw.getApplyAmount());
           }
           
           withdrawMapper.updateById(withdraw);
       }
       
       /**
        * 财务打款
        */
       @Transactional
       public void processPayment(Long withdrawId, String paymentUser, 
                                 String paymentVoucher) {
           YtDistAgentWithdrawApplyDO withdraw = withdrawMapper.selectById(withdrawId);
           if (withdraw == null || withdraw.getStatus() != 1) {
               throw new BusinessException("提现申请状态异常");
           }
           
           withdraw.setStatus(3); // 已打款
           withdraw.setPaymentTime(new Date());
           withdraw.setPaymentUser(paymentUser);
           withdraw.setPaymentVoucher(paymentVoucher);
           
           withdrawMapper.updateById(withdraw);
           
           // 更新账单状态为已提现
           updateBillsWithdrawStatus(withdrawId);
           
           // 记录税务信息（无发票情况）
           if (withdraw.getInvoiceType() == 1) {
               recordTaxInfo(withdraw);
           }
           
           // 完成提现
           completeWithdraw(withdrawId);
       }
       
       /**
        * 记录税务信息
        */
       private void recordTaxInfo(YtDistAgentWithdrawApplyDO withdraw) {
           YtDistAgentTaxRecordDO taxRecord = new YtDistAgentTaxRecordDO();
           taxRecord.setTaxNo(generateTaxNo());
           taxRecord.setAgentId(withdraw.getAgentId());
           taxRecord.setAgentName(withdraw.getAgentName());
           taxRecord.setWithdrawId(withdraw.getId());
           
           Date now = new Date();
           Calendar calendar = Calendar.getInstance();
           calendar.setTime(now);
           taxRecord.setTaxYear(calendar.get(Calendar.YEAR));
           taxRecord.setTaxMonth(calendar.get(Calendar.MONTH) + 1);
           
           taxRecord.setIncomeAmount(withdraw.getApplyAmount());
           taxRecord.setTaxRate(withdraw.getTaxRate());
           taxRecord.setTaxAmount(withdraw.getTaxAmount());
           taxRecord.setAfterTaxAmount(withdraw.getActualAmount());
           taxRecord.setReportStatus(0); // 待申报
           
           taxRecordMapper.insert(taxRecord);
       }
   }
   ```

## 5. API接口设计

### 5.1 分销员申请接口

#### 5.1.1 申请成为分销员
```
POST /api/distribution/agent/apply
Request:
{
    "inviteCode": "YT12345678",  // 可选，邀请码（分销员码或等级码）
    "bindType": 1  // 可选，绑定类型：1-同时绑定（默认），2-仅上级，3-仅介绍人
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "applyNo": "AP202401150001",
        "status": 0,  // 0-待审核，1-审核通过
        "autoAudit": false,  // 是否自动审核
        "agentId": null,  // 审核通过后才有值
        "agentCode": null,  // 审核通过后才有值
        "levelName": "青铜分销员",
        "message": "申请已提交，请等待审核"
    }
}

// 如果配置了自动审核通过，返回示例：
{
    "code": 0,
    "msg": "success",
    "data": {
        "applyNo": "AP202401150001",
        "status": 1,  // 已通过
        "autoAudit": true,
        "agentId": 10001,
        "agentCode": "YT56781234",
        "levelName": "黄金分销员",  // 使用等级邀请码时显示对应等级
        "parentAgentName": "李四",
        "referrerAgentName": "李四",
        "message": "恭喜您成为黄金分销员"
    }
}
```

#### 5.1.2 查询申请状态
```
GET /api/distribution/agent/apply/status?applyNo=AP202401150001
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "applyNo": "AP202401150001",
        "status": 1,  // 0-待审核，1-审核通过，2-审核拒绝
        "agentCode": "YT56781234",  // 审核通过后的分销码
        "agentId": 10001,
        "levelName": "黄金分销员",
        "parentAgentName": "李四",
        "referrerAgentName": "李四",
        "auditTime": "2024-01-15 15:30:00",
        "auditRemark": "审核通过"
    }
}
```

#### 5.1.3 查询我的分销员信息
```
GET /api/distribution/agent/info
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "agentId": 10001,
        "agentCode": "YT56781234",
        "agentName": "张三",
        "levelName": "青铜分销员",
        "levelGrade": 10,
        "parentAgentName": "李四",
        "referrerAgentName": "李四",
        "teamCount": 5,
        "directCount": 2,
        "totalCommission": "1580.00",
        "availableBalance": "800.00"
    }
}
```

### 5.2 分销码管理接口

#### 5.2.1 获取我的分销码
```
GET /api/distribution/agent/code
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "agentCode": "YT56781234",
        "qrCodeUrl": "https://xxx.com/qr/YT56781234.png",
        "inviteUrl": "https://xxx.com/invite?code=YT56781234",
        "shareTitle": "邀请您成为分销合伙人",
        "shareDesc": "使用我的邀请码注册，享受专属优惠"
    }
}
```

#### 5.2.2 验证分销码
```
GET /api/distribution/agent/code/verify?code=YT56781234
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "valid": true,
        "agentId": 10001,
        "agentName": "李四",
        "levelName": "白银分销员"
    }
}
```

### 5.3 等级管理接口

#### 5.3.1 获取等级体系
```
GET /api/distribution/level/list
Response:
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "levelId": 4,
            "levelName": "钻石分销员",
            "levelGrade": 4,
            "isHighest": true,
            "isLowest": false,
            "nextLevelId": 3,
            "nextLevelName": "黄金分销员",
            "benefits": ["30%最高佣金", "专属客服", "优先发货"]
        },
        {
            "levelId": 3,
            "levelName": "黄金分销员",
            "levelGrade": 3,
            "isHighest": false,
            "isLowest": false,
            "prevLevelId": 4,
            "prevLevelName": "钻石分销员",
            "nextLevelId": 2,
            "nextLevelName": "白银分销员",
            "benefits": ["25%佣金", "月度奖励"]
        }
    ]
}
```

#### 5.3.2 获取我的等级信息
```
GET /api/distribution/agent/level/info
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "currentLevel": {
            "levelId": 2,
            "levelName": "白银分销员",
            "levelGrade": 2,
            "salesScheme": {
                "schemeName": "白银等级销售方案",
                "commissionRate": "20%"
            }
        },
        "nextLevel": {
            "levelId": 3,
            "levelName": "黄金分销员",
            "upgradeProgress": {
                "salesAmount": "45000/50000",
                "teamCount": "85/100",
                "directCount": "18/20"
            }
        },
        "prevLevel": {
            "levelId": 1,
            "levelName": "青铜分销员"
        }
    }
}
```

### 5.4 提现管理接口

#### 5.4.1 提交提现申请
```
POST /api/distribution/agent/withdraw/apply
Request:
{
    "applyAmount": "500.00",
    "withdrawType": 1,  // 1-银行卡，2-微信，3-支付宝
    "accountId": 10001,  // 收款账户ID（可选，为空则使用默认账户）
    "invoiceType": 1,  // 1-无发票（代扣申报），2-有发票
    "invoiceAmount": "500.00",  // 发票金额（有发票时必填）
    "invoiceUrls": ["https://xxx.com/invoice1.jpg"],  // 发票图片（有发票时必填）
    "remark": "提现备注"
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "withdrawNo": "WD202401150001",
        "applyAmount": "500.00",
        "taxRate": "20%",
        "taxAmount": "100.00",
        "actualAmount": "400.00",
        "status": 0,  // 0-待财务审核
        "message": "提现申请已提交，请等待财务审核"
    }
}
```

#### 5.4.2 查询提现记录
```
GET /api/distribution/agent/withdraw/list?pageNo=1&pageSize=10&status=0
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 15,
        "pages": 2,
        "records": [
            {
                "withdrawNo": "WD202401150001",
                "applyAmount": "500.00",
                "actualAmount": "400.00",
                "withdrawType": 1,
                "withdrawTypeName": "银行卡",
                "accountInfo": {
                    "accountName": "张三",
                    "accountNumber": "6222****1234",
                    "bankName": "招商银行"
                },
                "invoiceType": 1,
                "invoiceTypeName": "代扣申报",
                "status": 1,
                "statusName": "审核通过",
                "applyTime": "2024-01-15 10:30:00",
                "auditTime": "2024-01-15 15:30:00",
                "paymentTime": null
            }
        ]
    }
}
```

#### 5.4.3 查询可提现金额
```
GET /api/distribution/agent/withdraw/available
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "totalCommission": "2580.00",
        "availableBalance": "1500.00",
        "frozenBalance": "580.00",
        "withdrawingAmount": "500.00",
        "minWithdrawAmount": "100.00",
        "withdrawableAmount": "1000.00"
    }
}
```

### 5.5 收款账户管理接口

#### 5.5.1 添加收款账户
```
POST /api/distribution/agent/account/add
Request:
{
    "accountType": 1,  // 1-银行卡，2-微信，3-支付宝
    "accountName": "张三",
    "accountNumber": "6222024000001234567",
    "bankName": "招商银行",  // 银行卡必填
    "bankBranch": "深圳分行南山支行",  // 银行卡必填
    "isDefault": true
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "accountId": 10001,
        "message": "收款账户添加成功"
    }
}
```

#### 5.5.2 查询收款账户列表
```
GET /api/distribution/agent/account/list
Response:
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "accountId": 10001,
            "accountType": 1,
            "accountTypeName": "银行卡",
            "accountName": "张三",
            "accountNumber": "6222****1234",
            "bankName": "招商银行",
            "bankBranch": "深圳分行南山支行",
            "isDefault": true,
            "status": 1
        },
        {
            "accountId": 10002,
            "accountType": 2,
            "accountTypeName": "微信",
            "accountName": "张三",
            "accountNumber": "138****5678",
            "isDefault": false,
            "status": 1
        }
    ]
}
```

### 5.6 分润查询接口

#### 5.6.1 获取商品分润信息
```
GET /api/distribution/product/commission/info?productId=1001
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "productId": 1001,
        "productName": "精品课程",
        "price": "299.00",
        "myCommission": {
            "schemeType": "SALES",
            "schemeName": "白银等级销售方案",
            "rate": "20%",
            "amount": "59.80"
        },
        "parentCommissions": [
            {
                "level": "一级上级",
                "levelName": "黄金分销员",
                "schemeType": "PROFIT",
                "schemeName": "黄金等级一级分润方案",
                "rate": "5%",
                "amount": "14.95"
            },
            {
                "level": "二级上级",
                "levelName": "钻石分销员",
                "schemeType": "PROFIT",
                "schemeName": "钻石等级二级分润方案",
                "rate": "3%",
                "amount": "8.97"
            }
        ]
    }
}
```

### 5.7 管理后台财务接口

#### 5.7.1 提现申请审核
```
POST /admin/distribution/withdraw/audit
Request:
{
    "withdrawId": 10001,
    "auditResult": 1,  // 1-通过，2-拒绝
    "auditRemark": "审核通过"
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": "审核操作成功"
}
```

#### 5.7.2 财务打款操作
```
POST /admin/distribution/withdraw/payment
Request:
{
    "withdrawId": 10001,
    "paymentVoucher": "https://xxx.com/voucher/20240115001.jpg"
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": "打款操作成功"
}
```

#### 5.7.3 提现申请列表（管理后台）
```
GET /admin/distribution/withdraw/list?pageNo=1&pageSize=20&status=0&agentName=张三
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 50,
        "pages": 3,
        "records": [
            {
                "withdrawId": 10001,
                "withdrawNo": "WD202401150001",
                "agentId": 20001,
                "agentName": "张三",
                "agentLevelName": "黄金分销员",
                "applyAmount": "500.00",
                "actualAmount": "400.00",
                "withdrawType": 1,
                "withdrawTypeName": "银行卡",
                "accountInfo": {
                    "accountName": "张三",
                    "accountNumber": "6222****1234",
                    "bankName": "招商银行",
                    "bankBranch": "深圳分行南山支行"
                },
                "invoiceType": 1,
                "invoiceTypeName": "代扣申报",
                "invoiceUrls": [],
                "status": 0,
                "statusName": "待财务审核",
                "applyTime": "2024-01-15 10:30:00",
                "auditTime": null,
                "paymentTime": null
            }
        ]
    }
}
```

#### 5.7.4 税务记录查询
```
GET /admin/distribution/tax/list?pageNo=1&pageSize=20&taxYear=2024&taxMonth=1
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 25,
        "records": [
            {
                "taxId": 30001,
                "taxNo": "TAX202401150001",
                "agentId": 20001,
                "agentName": "张三",
                "withdrawNo": "WD202401150001",
                "taxYear": 2024,
                "taxMonth": 1,
                "incomeAmount": "500.00",
                "taxRate": "20%",
                "taxAmount": "100.00",
                "afterTaxAmount": "400.00",
                "reportStatus": 0,
                "reportStatusName": "未申报"
            }
        ]
    }
}
```

#### 5.7.5 财务统计报表
```
GET /admin/distribution/finance/statistics?startDate=2024-01-01&endDate=2024-01-31
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "withdrawStatistics": {
            "totalAmount": "150000.00",
            "totalCount": 300,
            "approvedAmount": "120000.00",
            "approvedCount": 240,
            "rejectedAmount": "30000.00",
            "rejectedCount": 60
        },
        "taxStatistics": {
            "totalTaxAmount": "24000.00",
            "reportedTaxAmount": "20000.00",
            "unreportedTaxAmount": "4000.00"
        },
        "dailyWithdraw": [
            {
                "date": "2024-01-15",
                "amount": "5000.00",
                "count": 10
            }
        ],
        "topAgents": [
            {
                "agentId": 20001,
                "agentName": "张三",
                "totalWithdraw": "8000.00",
                "withdrawCount": 16
            }
        ]
    }
}
```

## 6. 管理后台功能

### 6.1 等级管理
- 创建/编辑分销等级
- 设置等级级别（level_grade）
- 配置等级邀请码
- 配置升降级条件
- 设置等级权益

### 6.2 分润方案管理

#### 6.2.1 销售等级方案管理
- 为每个等级配置销售奖励方案
- 设置直接销售佣金比例（适用于当前分销员）
- 配置最低订单金额和最高佣金限制
- 设置方案有效期

#### 6.2.2 分润等级方案管理
- 为不同等级配置上级分润方案
- 设置受益等级和来源等级关系
- 配置分润层级（一级、二级、三级）
- 设置分润条件（无条件、基于下级销售额、基于下级佣金）

### 6.3 分销员管理
- 审核分销申请（支持批量审核）
- 查看分销员列表
- 调整分销员等级（人工调整）
- 管理分销关系（上级/介绍人）
- 查看申请记录和历史

### 6.4 系统配置管理
- 配置审核开关（是否启用审核）
- 配置自动审核（是否自动通过）
- 设置默认分销等级
- 配置分销码前缀等参数
- 说明：使用系统自带的 infra_config 表进行配置管理

### 6.5 财务管理功能

#### 6.5.1 提现审核管理
- **提现申请列表**：查看所有分销员提现申请
- **审核操作**：审核通过/拒绝提现申请
- **批量审核**：支持批量处理提现申请
- **审核详情**：查看申请详情、发票信息、收款账户
- **筛选条件**：按状态、时间、金额范围、分销员等筛选

#### 6.5.2 财务打款管理
- **待打款列表**：已审核通过的提现申请
- **打款操作**：记录打款信息和凭证
- **打款查询**：查询历史打款记录
- **凭证管理**：上传和管理打款凭证
- **账户信息**：显示收款账户详情

#### 6.5.3 税务管理
- **税务记录查询**：查看代扣申报记录
- **税务统计**：按年度、月度统计税务信息
- **申报管理**：标记税务申报状态
- **税率配置**：动态调整代扣申报税率

#### 6.5.4 财务统计报表
- **提现统计**：按时间维度统计提现金额和次数
- **税务统计**：统计代扣税费总额
- **分销员财务状况**：查看单个分销员财务明细
- **异常监控**：识别异常提现行为

### 6.6 系统配置管理

#### 6.6.1 提现配置
```sql
-- 提现相关配置
INSERT INTO `infra_config` (`config_key`, `config_value`, `type`, `remark`) VALUES
('distribution.withdraw.enabled', 'true', 4, '是否启用提现功能'),
('distribution.withdraw.minAmount', '100', 2, '最低提现金额'),
('distribution.withdraw.maxAmount', '50000', 2, '单次最高提现金额'),
('distribution.withdraw.maxDailyAmount', '100000', 2, '单日最高提现金额'),
('distribution.withdraw.taxRate', '20', 2, '代扣申报税率（%）'),
('distribution.withdraw.workingDays', 'true', 4, '是否仅工作日允许提现'),
('distribution.withdraw.auditAuto', 'false', 4, '是否自动审核提现申请');
```

#### 6.6.2 冻结配置
```sql
-- 冻结相关配置
INSERT INTO `infra_config` (`config_key`, `config_value`, `type`, `remark`) VALUES
('distribution.freeze.orderPay', '30', 1, '订单支付后分销冻结天数'),
('distribution.freeze.orderReceive', '15', 1, '订单收货后分销冻结天数'),
('distribution.freeze.orderComplete', '7', 1, '订单完成后分销冻结天数'),
('distribution.freeze.couponRedeem', '3', 1, '券码核销后分销冻结天数');
```

### 6.7 数据统计与报表

#### 6.7.1 分销统计
- 各等级分销员统计
- 佣金发放统计
- 分销业绩排行
- 等级变动分析

#### 6.7.2 财务统计
- 提现金额统计（按日、周、月）
- 税务申报统计
- 冻结资金统计
- 分销员收益排行

#### 6.7.3 业务监控
- 异常提现监控
- 大额资金流动监控
- 分销员行为分析
- 系统性能监控

## 7. 技术实现要点

### 7.1 等级管理保证
```java
@Service
public class LevelManagementService {
    
    public void validateLevelConfig() {
        List<YtDistLevelDO> levels = levelMapper.selectAllActive();
        
        // 1. 验证等级编码唯一性
        Set<String> levelCodes = new HashSet<>();
        for (YtDistLevelDO level : levels) {
            if (!levelCodes.add(level.getLevelCode())) {
                throw new BusinessException("等级编码重复：" + level.getLevelCode());
            }
        }
        
        // 2. 验证等级级别唯一性
        Set<Integer> levelGrades = new HashSet<>();
        for (YtDistLevelDO level : levels) {
            if (!levelGrades.add(level.getLevelGrade())) {
                throw new BusinessException("等级级别重复：" + level.getLevelGrade());
            }
        }
        
        // 3. 验证至少有一个等级
        if (levels.isEmpty()) {
            throw new BusinessException("系统必须至少配置一个分销等级");
        }
    }
    
    /**
     * 获取下一个升级等级
     */
    public YtDistLevelDO getNextUpgradeLevel(Integer currentGrade) {
        return levelMapper.selectOne(new LambdaQueryWrapper<YtDistLevelDO>()
            .gt(YtDistLevelDO::getLevelGrade, currentGrade)
            .eq(YtDistLevelDO::getStatus, 1)
            .orderByAsc(YtDistLevelDO::getLevelGrade)
            .last("LIMIT 1"));
    }
    
    /**
     * 获取降级等级
     */
    public YtDistLevelDO getDowngradeLevel(Integer currentGrade) {
        return levelMapper.selectOne(new LambdaQueryWrapper<YtDistLevelDO>()
            .lt(YtDistLevelDO::getLevelGrade, currentGrade)
            .eq(YtDistLevelDO::getStatus, 1)
            .orderByDesc(YtDistLevelDO::getLevelGrade)
            .last("LIMIT 1"));
    }
}
```

### 7.2 分销码防重机制
- 使用Redis缓存已生成的分销码
- 定期清理过期的缓存
- 数据库唯一索引保证最终一致性

### 7.3 佣金计算性能优化
- 使用缓存存储分销关系链
- 批量计算同一订单的多级佣金
- 异步处理佣金结算

## 8. 数据迁移方案

如果系统中已有分销数据，需要进行数据迁移：

1. **等级数据迁移**
   - 创建新的等级体系
   - 映射旧等级到新等级
   - 设置等级级别（level_grade）

2. **分销员数据迁移**
   - 生成唯一分销码
   - 建立分销关系
   - 迁移历史佣金数据

3. **分润方案迁移**
   - 转换旧的佣金规则
   - 配置新的分润方案
   - 验证计算结果

## 9. 测试用例

### 9.1 等级管理测试
- 创建新等级
- 修改等级信息
- 删除等级（需验证是否有分销员）
- 等级编码和级别唯一性验证
- 升降级等级查找测试

### 9.2 分销员申请测试
- 使用分销员邀请码申请（测试关系绑定）
- 使用等级邀请码申请（测试初始等级）
- 无邀请码申请（测试默认等级）
- 无效邀请码申请
- 审核流程测试：
  - 自动审核通过
  - 人工审核通过
  - 人工审核拒绝
- 重复申请防护

### 9.3 佣金计算测试
- **分销奖励测试**
  - 直接销售佣金计算
  - 不同等级销售方案测试
  - 最低订单金额限制测试
  - 最高佣金限制测试
- **分销分润测试**
  - 上级分润计算测试
  - 介绍人分润计算测试
  - 混合分润（上级+介绍人）测试
  - 多级追溯测试（一级、二级、三级）
- **方案唯一性测试**
  - 同级别方案冲突检测
  - 优先级判断测试
- **分润节点测试**
  - 订单完成触发测试
  - 优惠券核销触发测试
  - 状态变更监听测试

### 9.4 退款处理测试
- **部分退款测试**
  - 按比例生成负数账单
  - 原账单状态不变
  - 退款记录正确生成
- **全额退款测试**
  - 原账单状态变为已取消
  - 生成等额负数账单
  - 所有相关账单都处理
- **退款计算准确性**
  - 多级分润的退款计算
  - 金额精度测试
  - 并发退款测试

## 10. 部署注意事项

1. **数据库索引优化**
   - 分销码查询索引
   - 等级关系索引
   - 佣金查询索引

2. **缓存配置**
   - Redis集群配置
   - 缓存过期策略
   - 缓存预热

3. **性能监控**
   - 接口响应时间
   - 数据库查询性能
   - 缓存命中率

4. **安全配置**
   - 接口权限控制
   - 数据加密
   - 防刷机制

## 11. 分润方案管理详细说明

### 11.1 方案设计理念

本系统将分润方案拆分为两种类型，以实现更灵活、更精细的佣金管理：

1. **销售等级方案**：针对直接销售商品的分销员，根据其所属等级确定销售奖励
2. **分润等级方案**：针对上级分销员，根据其等级和与下级的关系确定分润比例

### 11.2 销售等级方案详解

#### 11.2.1 应用场景
- 青铜分销员：15%销售佣金
- 白银分销员：20%销售佣金
- 黄金分销员：25%销售佣金
- 钻石分销员：30%销售佣金

#### 11.2.2 特点
- 一个等级对应一个销售方案
- 只应用于直接销售者
- 支持固定金额和百分比两种模式
- 可设置最低订单金额和最高佣金限制

### 11.3 分润等级方案详解

#### 11.3.1 应用场景
- 黄金分销员的一级下级销售时，黄金分销员获得5%分润
- 钻石分销员的二级下级销售时，钻石分销员获得3%分润
- 可以根据下级等级设置不同的分润比例

#### 11.3.2 特点
- 可为同一等级配置多个分润方案
- 支持根据来源等级（下级等级）区分分润
- 支持设置分润条件（如最低下级佣金要求）
- 支持多级追溯（一级、二级、三级）

### 11.4 方案匹配规则

#### 11.4.1 销售方案匹配
1. 根据当前分销员等级查找对应的销售方案
2. 检查方案是否在有效期内
3. 检查订单金额是否满足最低要求
4. 计算佣金并应用最高限制

#### 11.4.2 分润方案匹配
1. 根据上级分销员等级和追溯层级查找方案
2. 如有来源等级限制，检查下级等级是否匹配
3. 检查分润条件是否满足
4. 按优先级选择方案
5. 计算分润并应用限制

## 12. 管理后台API接口设计

### 12.1 分销申请审核管理

#### 12.1.1 获取待审核列表
```
GET /admin/distribution/apply/list?status=0&page=1&size=20
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 50,
        "list": [
            {
                "id": 1,
                "applyNo": "AP202401150001",
                "memberName": "张三",
                "phone": "138****8000",
                "inviteCode": "YT12345678",
                "inviteType": 1,  // 1-分销员邀请码，2-等级邀请码
                "levelName": "青铜分销员",
                "parentAgentName": "李四",
                "applyTime": "2024-01-15 10:30:00",
                "status": 0
            }
        ]
    }
}
```

#### 12.1.2 审核申请
```
POST /admin/distribution/apply/audit
Request:
{
    "ids": [1, 2, 3],  // 支持批量审核
    "status": 1,  // 1-通过，2-拒绝
    "auditRemark": "审核通过"
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "successCount": 3,
        "failCount": 0
    }
}
```

### 12.2 销售等级方案管理

#### 12.2.1 创建/编辑销售方案
```
POST /admin/distribution/sales-scheme/save
Request:
{
    "id": null,  // null为新增，有值为编辑
    "schemeCode": "SALE_L2_001",
    "schemeName": "白银等级销售方案",
    "schemeDesc": "适用于白银等级分销员的销售奖励方案",
    "levelId": 2,
    "commissionMode": 2,  // 1-固定金额，2-百分比
    "commissionRate": 20,
    "commissionAmount": 0,
    "minOrderAmount": "100",
    "maxCommission": "1000",
    "effectiveType": 1,  // 1-长期有效，2-限时有效
    "startTime": null,
    "endTime": null,
    "priority": 100
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "schemeId": 1
    }
}
```

#### 12.2.2 获取销售方案列表
```
GET /admin/distribution/sales-scheme/list?page=1&size=20&levelId=2
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 5,
        "list": [
            {
                "id": 1,
                "schemeCode": "SALE_L2_001",
                "schemeName": "白银等级销售方案",
                "levelName": "白银分销员",
                "commissionMode": 2,
                "commissionRate": "20%",
                "status": 1,
                "createTime": "2024-01-15 10:00:00"
            }
        ]
    }
}
```

### 12.2 分润等级方案管理

#### 12.2.1 创建/编辑分润方案
```
POST /admin/distribution/profit-scheme/save
Request:
{
    "id": null,
    "schemeCode": "PROFIT_L3_ALL_1",
    "schemeName": "黄金等级一级分润方案",
    "schemeDesc": "黄金分销员从一级下级获得的分润",
    "levelId": 3,  // 受益等级（黄金）
    "sourceLevelId": null,  // null表示所有等级
    "traceLevel": 1,  // 追溯层级
    "commissionMode": 2,
    "commissionRate": 5,
    "commissionAmount": 0,
    "conditionType": 1,  // 1-无条件，2-基于下级销售额，3-基于下级佣金
    "conditionConfig": null,
    "minOrderAmount": "0",
    "maxCommission": "500",
    "effectiveType": 1,
    "priority": 100
}
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "schemeId": 10
    }
}
```

#### 12.2.2 获取分润方案列表
```
GET /admin/distribution/profit-scheme/list?page=1&size=20&levelId=3
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "total": 3,
        "list": [
            {
                "id": 10,
                "schemeCode": "PROFIT_L3_ALL_1",
                "schemeName": "黄金等级一级分润方案",
                "levelName": "黄金分销员",
                "sourceLevelName": "所有等级",
                "traceLevel": 1,
                "traceLevelName": "一级上级",
                "commissionRate": "5%",
                "conditionType": 1,
                "conditionTypeName": "无条件",
                "status": 1
            }
        ]
    }
}
```

### 12.3 方案分配查询

#### 12.3.1 获取某等级的所有方案
```
GET /admin/distribution/scheme/by-level?levelId=3
Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "salesScheme": {
            "id": 3,
            "schemeName": "黄金等级销售方案",
            "commissionRate": "25%"
        },
        "profitSchemes": [
            {
                "id": 10,
                "schemeName": "黄金等级一级分润方案",
                "traceLevel": 1,
                "commissionRate": "5%"
            },
            {
                "id": 11,
                "schemeName": "黄金等级二级分润方案",
                "traceLevel": 2,
                "commissionRate": "3%"
            }
        ]
    }
}
```

## 13. 更新说明

### 13.1 v1.0 版本更新（2024-01-15）
1. **分润方案重构**
   - 将原来的单一分润方案拆分为销售等级方案和分润等级方案
   - 销售等级方案：专注于直接销售者的奖励
   - 分润等级方案：专注于上级分销员的分润

2. **数据库设计优化**
   - 新增 `yt_dist_sales_scheme` 表
   - 新增 `yt_dist_profit_scheme` 表
   - 移除原 `yt_dist_level_commission_scheme` 表

3. **业务逻辑增强**
   - 支持根据来源等级设置不同分润
   - 支持更灵活的分润条件配置
   - 优化佣金计算流程

4. **API接口更新**
   - 新增销售方案管理接口
   - 新增分润方案管理接口
   - 优化佣金查询接口返回信息

### 13.2 v1.1 版本更新（2024-01-16）
1. **新增介绍人机制**
   - 区分上级（管理关系）和介绍人（推荐关系）
   - 分销员表新增介绍人相关字段
   - 申请时可选择邀请码的用途

2. **分润对象灵活配置**
   - 分润方案新增 profit_target 字段
   - 支持配置分润给上级、介绍人或两者
   - 支持为上级和介绍人设置不同分润比例

3. **佣金计算优化**
   - 根据分润方案配置决定追溯路径
   - 支持按管理关系和推荐关系分别追溯
   - 优化佣金计算性能

### 13.3 v1.2 版本更新（2024-01-17）
1. **简化分销等级设计**
   - 去除等级间的上下级链式关系
   - 使用level_grade数值判断等级高低
   - 更灵活的等级管理

2. **优化申请流程**
   - 新用户注册时，邀请人同时成为上级和介绍人
   - 已有用户申请时，可灵活选择绑定类型
   - 申请自动通过，无需审核

3. **简化数据表设计**
   - 将分销申请记录表改为申请日志表
   - 去除审核相关字段，保留核心日志信息
   - 优化表结构，提升性能

### 13.4 v1.3 版本更新（2024-01-18）
1. **完善审核机制**
   - 恢复分销员申请记录表（yt_dist_agent_apply_record），支持审核流程
   - 使用系统自带的 infra_config 表进行配置管理
   - 支持自动审核和人工审核两种模式

2. **新增等级邀请码**
   - 每个分销等级可配置专属邀请码
   - 通过等级邀请码申请直接获得对应等级
   - 区分分销员邀请码和等级邀请码

3. **增强配置能力**
   - 可配置是否启用审核（dist.apply.audit.enable）
   - 可配置是否自动审核（dist.apply.audit.auto）
   - 可配置默认分销等级（dist.apply.default.level）
   - 使用系统 infra_config 表统一管理配置

### 13.5 v1.4 版本更新（2024-01-19）
1. **优化佣金管理**
   - 佣金计算记录表改为佣金账单记录表（yt_dist_agent_bill_record）
   - 明确区分分销奖励（bill_type=1）和分销分润（bill_type=2）
   - 新增退款记录表（yt_dist_agent_refund_record）

2. **完善分润机制**
   - 明确分润计算节点：订单完成、优惠券核销
   - 保证方案唯一性，避免重复分润
   - 支持按优先级选择唯一生效方案

3. **新增退款处理**
   - 支持部分退款：生成负数账单
   - 支持全额退款：取消原账单+负数账单
   - 自动计算并扣除相应佣金

### 13.6 v1.5 版本更新（2025-01-10）
1. **优化商品数据模型**
   - 移除冗余的goods_id字段，统一使用spu_id
   - 方案表支持按SPU或SKU配置分销规则
   - 账单记录表保留spu/sku信息，便于数据追踪

2. **完善技术实现**
   - 定时任务统一使用Quartz框架
   - 依赖注入统一使用@Resource注解
   - 分润追溯支持N级，不再限制为3级
   - MySQL原子操作保证余额更新的并发安全

3. **新增商品配置能力**
   - 支持全品类佣金配置（apply_scope=1）
   - 支持指定SPU佣金配置（apply_scope=2, spu_id=xxx）
   - 支持指定SKU佣金配置（apply_scope=2, spu_id=xxx, sku_id=yyy）

## 14. 介绍人机制详细说明

### 14.1 设计理念

介绍人机制是分销系统中的重要组成部分，与传统的上下级管理关系并存，为分销网络提供更灵活的激励方式。

### 14.2 三种关系对比

| 关系类型 | 作用 | 特点 | 应用场景 |
|---------|------|------|----------|
| **上级** | 管理关系 | - 决定组织架构<br>- 影响团队统计<br>- 通常不可更改 | - 团队管理<br>- 业绩统计<br>- 等级升降 |
| **等级** | 权益定位 | - 决定佣金比例<br>- 享受不同权益<br>- 可升可降 | - 销售奖励<br>- 分润比例<br>- 特权服务 |
| **介绍人** | 推荐关系 | - 记录推荐来源<br>- 可有可无<br>- 可灵活奖励 | - 推荐奖励<br>- 追溯来源<br>- 特殊活动 |

### 14.3 典型应用场景

#### 14.3.1 场景一：上级和介绍人是同一人
- 张三通过李四的分销码申请成为分销员
- 李四既是张三的上级，也是介绍人
- 李四可以获得双重身份的分润

#### 14.3.2 场景二：上级和介绍人是不同人
- 张三通过王五的分销码了解到分销机会
- 张三申请时选择李四作为上级（因为李四的团队更强）
- 李四成为张三的上级，王五保留介绍人身份
- 根据分润方案，李四和王五都可能获得分润

#### 14.3.3 场景三：只有上级没有介绍人
- 张三自主发现平台并申请成为分销员
- 系统分配或张三选择李四作为上级
- 没有介绍人，相关分润无需计算

### 14.4 分润配置策略

#### 14.4.1 独立奖励策略
- 上级分润：基于管理责任和团队贡献
- 介绍人奖励：基于推荐贡献和拉新激励

#### 14.4.2 组合奖励策略
- 新人首单：介绍人获得额外奖励
- 业绩达标：上级获得管理奖金
- 特殊活动：两者都可获得奖励

### 14.5 技术实现要点

#### 14.5.1 申请处理与审核
```java
@Service
public class DistributorApplyService {
    
    @Resource
    private InfraConfigService infraConfigService;
    
    public ApplyResult applyDistributor(ApplyRequest request, MemberInfo member) {
        // 1. 创建申请记录
        YtDistAgentApplyRecordDO applyRecord = new YtDistAgentApplyRecordDO();
        applyRecord.setApplyNo(generateApplyNo());
        applyRecord.setMemberId(member.getId());
        applyRecord.setMemberName(member.getNickname());
        applyRecord.setPhone(member.getPhone());
        applyRecord.setInviteCode(request.getInviteCode());
        applyRecord.setBindType(request.getBindType());
        applyRecord.setApplyTime(new Date());
        
        // 2. 处理邀请码
        InviteCodeInfo inviteInfo = processInviteCode(request.getInviteCode());
        if (inviteInfo != null) {
            applyRecord.setInviteType(inviteInfo.getType());
            applyRecord.setInitialLevelId(inviteInfo.getLevelId());
            applyRecord.setParentAgentId(inviteInfo.getParentAgentId());
            applyRecord.setReferrerAgentId(inviteInfo.getReferrerAgentId());
        } else {
            // 无邀请码，使用默认等级
            String defaultLevelIdStr = infraConfigService.getConfigValue("dist.apply.default.level");
            Long defaultLevelId = Long.parseLong(defaultLevelIdStr);
            applyRecord.setInitialLevelId(defaultLevelId);
        }
        
        // 3. 判断是否需要审核
        boolean auditEnabled = Boolean.parseBoolean(
            infraConfigService.getConfigValue("dist.apply.audit.enable"));
        boolean autoAudit = Boolean.parseBoolean(
            infraConfigService.getConfigValue("dist.apply.audit.auto"));
        
        if (!auditEnabled || autoAudit) {
            // 自动通过
            applyRecord.setStatus(1);
            applyRecord.setAutoAudit(true);
            applyRecord.setAuditTime(new Date());
            applyRecord.setAuditUser("SYSTEM");
            applyRecord.setAuditRemark("自动审核通过");
            
            // 创建分销员
            YtDistAgentDO agent = createDistributor(applyRecord, member);
            applyRecord.setAgentId(agent.getId());
            applyRecord.setAgentCode(agent.getAgentCode());
        } else {
            // 需要人工审核
            applyRecord.setStatus(0);
            applyRecord.setAutoAudit(false);
        }
        
        // 4. 保存申请记录
        applyRecordMapper.insert(applyRecord);
        
        return buildApplyResult(applyRecord);
    }
    
    /**
     * 处理邀请码
     */
    private InviteCodeInfo processInviteCode(String inviteCode) {
        if (StringUtils.isBlank(inviteCode)) {
            return null;
        }
        
        InviteCodeInfo info = new InviteCodeInfo();
        
        // 1. 先检查是否是分销员邀请码
        YtDistAgentDO agent = agentMapper.selectByAgentCode(inviteCode);
        if (agent != null) {
            info.setType(1); // 分销员邀请码
            info.setLevelId(agent.getLevelId());
            info.setParentAgentId(agent.getId());
            info.setReferrerAgentId(agent.getId());
            return info;
        }
        
        // 2. 检查是否是等级邀请码
        YtDistLevelDO level = levelMapper.selectByInviteCode(inviteCode);
        if (level != null) {
            info.setType(2); // 等级邀请码
            info.setLevelId(level.getId());
            // 等级邀请码无上级和介绍人
            return info;
        }
        
        return null;
    }
}
```

#### 14.5.2 分润追溯
```java
// 根据配置追溯不同路径
public List<YtDistAgentDO> traceCommissionPath(
        YtDistAgentDO sourceAgent, 
        int profitTarget, 
        int maxLevel) {
    
    List<YtDistAgentDO> path = new ArrayList<>();
    YtDistAgentDO current = sourceAgent;
    int level = 0;
    
    while (current != null && level < maxLevel) {
        Long nextId = null;
        
        switch (profitTarget) {
            case 1: // 追溯上级
                nextId = current.getParentId();
                break;
            case 2: // 追溯介绍人
                nextId = current.getReferrerId();
                break;
            case 3: // 两者都追溯（需要特殊处理）
                // 分别处理两条路径
                break;
        }
        
        if (nextId != null && nextId > 0) {
            current = agentMapper.selectById(nextId);
            if (current != null && current.getStatus() == 1) {
                path.add(current);
                level++;
            }
        } else {
            break;
        }
    }
    
    return path;
}
```

### 14.6 注意事项

1. **关系完整性**
   - 上级关系一旦建立通常不允许修改
   - 介绍人关系可根据业务需要决定是否可修改
   - 避免循环引用

2. **分润公平性**
   - 合理设置上级和介绍人的分润比例
   - 避免重复奖励导致成本过高
   - 考虑特殊情况的处理

3. **数据统计**
   - 区分统计团队人数和介绍人数
   - 分别跟踪不同来源的业绩
   - 提供多维度的数据分析

## 15. 总结

本设计文档详细描述了分销代理系统的核心功能，包括：

1. **灵活的等级体系**：支持自定义分销等级，并设置上下级关系
2. **完善的关系管理**：区分上级（管理）、等级（权益）、介绍人（推荐）三种关系
3. **灵活的分润机制**：支持配置分润给上级、介绍人或两者
4. **完整的业务流程**：从申请、审核、绑定到佣金计算、结算

系统设计充分考虑了扩展性和灵活性，可以满足不同业务场景的需求。