# 分销代理系统详细设计文档 v2.0

## 1. 概述

本文档基于 v1.1 版本进行全面升级，主要改进点：
1. **多维度配置体系**：支持个人专属、标签、等级、商品等多维度配置
2. **智能归因机制**：建立清晰的配置优先级体系，自动处理配置冲突
3. **标签化管理**：支持人员标签配置，实现精细化分销策略
4. **实时缓存优化**：提升配置查询性能，支持实时生效
5. **完善监控体系**：全链路追踪，配置生效可视化

## 2. 核心需求分析

### 2.1 自定义分销等级
- **需求描述**：支持自定义分销等级，通过等级级别（level_grade）区分高低
- **设计要点**：
  - 等级可灵活配置（数量、名称、条件）
  - 通过 level_grade 数值大小判断等级高低
  - 支持升降级条件配置
  - 等级变更自动触发相关业务流程

### 2.2 分销员申请与绑定
- **需求描述**：人员申请成为分销员后，有自己的分销码。其他人员根据分销码，申请或者注册就成功绑定其为分销员的子分销员
- **设计要点**：
  - 每个分销员拥有唯一的分销码
  - 区分三种关系：
    - **上级**：管理关系，决定组织架构和团队归属
    - **等级**：决定分销员的分润比例和权益
    - **介绍人**：推荐关系，申请时谁介绍的（可有可无）
  - 支持通过分销码建立关系
  - **新注册用户**：通过分销码注册时，分销码拥有者同时成为其上级和介绍人
  - **已注册用户**：申请时可灵活选择绑定关系类型

### 2.3 多维度奖励配置体系【v2.0核心升级】
- **需求描述**：建立多维度、多层级的奖励配置体系，支持精细化分销策略
- **设计要点**：
  - **个人专属配置**：为特定分销员设置专属奖励政策
  - **标签配置**：基于人员标签（KOL、VIP、合作伙伴等）设置差异化奖励
  - **等级配置**：按分销等级设置不同的奖励标准
  - **商品配置**：按商品维度（SKU/SPU/类目）设置专项奖励
  - **全局配置**：系统默认的基础奖励配置
  - **智能归因**：自动选择最优配置，避免冲突

### 2.4 人员标签管理【v2.0新增】
- **需求描述**：支持为分销员打标签，实现人群分类和精准营销
- **设计要点**：
  - 支持多种标签类型：系统标签、业务标签、营销标签
  - 标签可设置有效期，支持临时性标签
  - 基于标签配置差异化奖励政策
  - 标签可视化管理，支持批量操作

### 2.5 配置归因机制【v2.0新增】
- **需求描述**：建立清晰的配置优先级体系，自动处理多维度配置冲突
- **设计要点**：
  - **优先级体系**：个人专属 > 标签配置 > 等级配置 > 商品配置 > 全局配置
  - **冲突处理**：自动选择最高优先级的有效配置
  - **决策追踪**：记录配置选择过程，支持审计和调试
  - **实时生效**：配置变更后立即生效，支持缓存刷新

## 3. 数据库设计

### 3.1 分销等级表（yt_dist_level）
```sql
CREATE TABLE `yt_dist_level` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_code` varchar(32) NOT NULL COMMENT '等级编码',
  `level_name` varchar(64) NOT NULL COMMENT '等级名称',
  `level_grade` int(11) NOT NULL COMMENT '等级级别，数值越大等级越高',
  `invite_code` varchar(32) DEFAULT NULL COMMENT '等级邀请码',
  `icon_url` varchar(256) DEFAULT NULL COMMENT '等级图标',
  `color` varchar(32) DEFAULT NULL COMMENT '等级颜色',
  `description` text COMMENT '等级说明',
  `benefits` text COMMENT '等级权益描述',
  `upgrade_conditions` json DEFAULT NULL COMMENT '升级条件（JSON格式）',
  `downgrade_conditions` json DEFAULT NULL COMMENT '降级条件（JSON格式）',
  `auto_upgrade` tinyint(1) DEFAULT '1' COMMENT '是否自动升级',
  `auto_downgrade` tinyint(1) DEFAULT '0' COMMENT '是否自动降级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序序号',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_level_code` (`level_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  KEY `idx_level_grade` (`level_grade`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销等级表';
```

### 3.2 分销员信息表（yt_dist_agent）
```sql
CREATE TABLE `yt_dist_agent` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分销员ID',
  `member_id` bigint(20) NOT NULL COMMENT '会员ID',
  `agent_code` varchar(32) NOT NULL COMMENT '分销员编码（分销码）',
  `agent_name` varchar(64) NOT NULL COMMENT '分销员名称',
  `level_id` bigint(20) NOT NULL COMMENT '等级ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '直接上级分销员ID（管理关系）',
  `parent_code` varchar(32) DEFAULT NULL COMMENT '上级分销码',
  `referrer_id` bigint(20) DEFAULT NULL COMMENT '介绍人ID（推荐关系）',
  `agent_tags` varchar(512) DEFAULT NULL COMMENT '分销员标签（逗号分隔）',
  `referrer_code` varchar(32) DEFAULT NULL COMMENT '介绍人分销码',
  `referrer_time` datetime DEFAULT NULL COMMENT '介绍时间',
  `path` varchar(512) DEFAULT NULL COMMENT '分销路径，如：1,2,3',
  `depth` int(11) DEFAULT '1' COMMENT '层级深度，顶级为1',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定上级时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `team_count` int(11) DEFAULT '0' COMMENT '团队人数（包含所有下级）',
  `direct_count` int(11) DEFAULT '0' COMMENT '直属下级人数',
  `referral_count` int(11) DEFAULT '0' COMMENT '介绍人数',
  `total_sales` decimal(10,2) DEFAULT '0.00' COMMENT '累计销售额',
  `month_sales` decimal(10,2) DEFAULT '0.00' COMMENT '本月销售额',
  `join_time` datetime NOT NULL COMMENT '成为分销员时间',
  `level_update_time` datetime DEFAULT NULL COMMENT '等级更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_member_id` (`member_id`),
  UNIQUE KEY `uk_agent_code` (`agent_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_referrer_id` (`referrer_id`),
  KEY `idx_referrer_code` (`referrer_code`),
  KEY `idx_level_id` (`level_id`),
  KEY `idx_status` (`status`),
  KEY `idx_depth` (`depth`),
  KEY `idx_agent_tags` (`agent_tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员信息表';
```

### 3.3 分销员标签表（yt_dist_agent_tag）【v2.0新增】
```sql
CREATE TABLE `yt_dist_agent_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_code` varchar(32) NOT NULL COMMENT '标签编码（唯一）',
  `tag_name` varchar(64) NOT NULL COMMENT '标签名称',
  `tag_desc` varchar(256) DEFAULT NULL COMMENT '标签描述',
  `tag_type` tinyint(4) DEFAULT '1' COMMENT '标签类型：1-系统标签，2-业务标签，3-营销标签',
  `color` varchar(16) DEFAULT NULL COMMENT '标签颜色（用于前端展示）',
  `icon` varchar(64) DEFAULT NULL COMMENT '标签图标',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_code` (`tag_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签表';
```

### 3.4 分销员标签关联表（yt_dist_agent_tag_rel）【v2.0新增】
```sql
CREATE TABLE `yt_dist_agent_tag_rel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `attach_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '标签附加时间',
  `expire_time` datetime DEFAULT NULL COMMENT '标签过期时间（NULL表示永久）',
  `attach_reason` varchar(256) DEFAULT NULL COMMENT '附加原因',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_agent_tag` (`agent_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分销员标签关联表';
```

### 3.5 奖励方案主表（yt_dist_reward_scheme）
```sql
CREATE TABLE `yt_dist_reward_scheme` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '方案ID',
  `scheme_code` varchar(32) NOT NULL COMMENT '方案编码',
  `scheme_name` varchar(64) NOT NULL COMMENT '方案名称',
  `scheme_desc` varchar(256) DEFAULT NULL COMMENT '方案描述',
  `apply_scope` tinyint(4) NOT NULL DEFAULT '1' COMMENT '适用范围：1-全局，2-指定商品类目，3-指定商品',
  `category_id` bigint(20) DEFAULT NULL COMMENT '商品类目ID（apply_scope=2时使用）',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID（apply_scope=3时使用）',
  `sku_id` bigint(20) DEFAULT NULL COMMENT 'SKU ID（apply_scope=3时使用，可选）',
  `enable_sales_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用销售奖励',
  `enable_profit_sharing` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用分润奖励',
  `profit_target` tinyint(4) DEFAULT '1' COMMENT '分润对象：1-上级，2-介绍人，3-两者都有',
  `max_trace_level` int(11) DEFAULT '3' COMMENT '最大追溯层级',
  `level_config_mode` tinyint(4) NOT NULL DEFAULT '1' COMMENT '等级配置模式：1-统一配置，2-分等级配置',
  `default_sales_mode` tinyint(4) DEFAULT '2' COMMENT '默认销售佣金模式：1-固定金额，2-百分比',
  `default_sales_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认销售佣金比例（%）',
  `default_sales_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认销售固定佣金',
  `default_profit_mode` tinyint(4) DEFAULT '2' COMMENT '默认分润佣金模式：1-固定金额，2-百分比',
  `default_profit_rate` decimal(5,2) DEFAULT '0.00' COMMENT '默认分润佣金比例（%）',
  `default_profit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '默认分润固定佣金',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `trigger_stage` tinyint(4) DEFAULT '1' COMMENT '触发阶段：1-订单支付后，2-券码核销后，3-订单完成后',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级，数值越大优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_code` (`scheme_code`),
  KEY `idx_apply_scope` (`apply_scope`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_level_config_mode` (`level_config_mode`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励方案主表';
```

### 3.6 等级奖励配置表（yt_dist_reward_level_config）
```sql
CREATE TABLE `yt_dist_reward_level_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '方案ID',
  `level_id` bigint(20) NOT NULL COMMENT '分销等级ID',
  `level_name` varchar(64) DEFAULT NULL COMMENT '等级名称（冗余）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：1-固定金额，2-百分比',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式，包含各级分润比例）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式，包含各级分润比例）',
  `condition_type` tinyint(4) DEFAULT '1' COMMENT '条件类型：1-无条件，2-基于销售额，3-基于销售量',
  `condition_config` json DEFAULT NULL COMMENT '条件配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT NULL COMMENT '最低订单金额（覆盖方案默认值）',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金（覆盖方案默认值）',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_level` (`scheme_id`, `level_id`),
  KEY `idx_scheme_id` (`scheme_id`),
  KEY `idx_level_id` (`level_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='等级奖励配置表';
```

### 3.7 标签奖励配置表（yt_dist_reward_tag_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_tag_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `scheme_id` bigint(20) NOT NULL COMMENT '奖励方案ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sales_commission_mode` tinyint(4) DEFAULT '1' COMMENT '销售佣金模式：1-不设置，2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `priority` int(11) DEFAULT '0' COMMENT '优先级（标签内部优先级）',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scheme_tag` (`scheme_id`, `tag_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签奖励配置表';
```

### 3.8 个人专属奖励配置表（yt_dist_reward_personal_config）【v2.0新增】
```sql
CREATE TABLE `yt_dist_reward_personal_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `agent_id` bigint(20) NOT NULL COMMENT '分销员ID',
  `scheme_id` bigint(20) DEFAULT NULL COMMENT '关联的奖励方案ID（可选）',
  `config_name` varchar(128) NOT NULL COMMENT '配置名称',
  `config_desc` varchar(512) DEFAULT NULL COMMENT '配置描述',
  `apply_scope` tinyint(4) DEFAULT '1' COMMENT '适用范围：1-全部商品，2-指定类目，3-指定SPU，4-指定SKU',
  `scope_ids` json DEFAULT NULL COMMENT '适用范围ID列表（JSON数组）',
  `sales_commission_mode` tinyint(4) DEFAULT '2' COMMENT '销售佣金模式：2-百分比，3-固定金额',
  `sales_commission_rate` decimal(5,2) DEFAULT '0.00' COMMENT '销售佣金比例（%）',
  `sales_commission_amount` decimal(10,2) DEFAULT '0.00' COMMENT '销售固定佣金金额',
  `enable_parent_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用上级分润',
  `parent_profit_config` json DEFAULT NULL COMMENT '上级分润配置（JSON格式）',
  `enable_referrer_profit` tinyint(1) DEFAULT '0' COMMENT '是否启用介绍人分润',
  `referrer_profit_config` json DEFAULT NULL COMMENT '介绍人分润配置（JSON格式）',
  `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低订单金额',
  `max_commission` decimal(10,2) DEFAULT NULL COMMENT '单笔最高佣金限制',
  `effective_type` tinyint(4) DEFAULT '1' COMMENT '生效类型：1-长期有效，2-限时有效',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `priority` int(11) DEFAULT '0' COMMENT '优先级',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `approve_status` tinyint(4) DEFAULT '0' COMMENT '审批状态：0-待审批，1-已通过，2-已拒绝',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approver` varchar(64) DEFAULT NULL COMMENT '审批人',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_status` (`status`, `approve_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='个人专属奖励配置表';
```
```
