package com.yitong.octopus.module.openapi.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.yitong.octopus.module.openapi.dto.validator.Auth;
import com.yitong.octopus.module.openapi.dto.validator.Biz;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.Date;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 接口基础类
 */
@Data
public class ApiBaseDto {

    /**
     * 应用Id
     */
    @NotEmpty(groups = {Auth.class, Biz.class},message = "app_id不能为空")
    private String appId;

    /**
     * 签名摘要算法。目前只支持：MD5。
     */
    @NotEmpty(groups = {Auth.class, Biz.class},message = "签名摘要算法不能为空")
    private String signMethod;

    /**
     * 随机值
     */
    @NotEmpty(groups = {Auth.class, Biz.class},message = "随机值不能为空")
    @Size(min = 32,max = 32)
    private String nonceStr;

    /**
     * 时间戳
     */
    @NotNull(groups = {Auth.class, Biz.class},message = "时间戳不能为空")
    private String timestamp;

    /**
     * 签名
     */
    @NotEmpty(groups = {Auth.class, Biz.class},message = "签名不能为空")
    private String sign;

    /**
     * 访问token
     */
    @NotEmpty(groups = {Biz.class},message = "access_token不能为空")
    private String accessToken;

    /**
     * 业务数据
     */
    @NotEmpty(groups = {Biz.class},message = "业务数据不能为空")
    private String bizContent;
}
