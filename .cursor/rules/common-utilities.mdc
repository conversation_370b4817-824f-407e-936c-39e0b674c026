---
description: 
globs: 
alwaysApply: false
---
# Common Utilities and Patterns

This project provides various utilities and patterns to simplify development.

## Core Utilities

- **ServletUtils**: HTTP request/response utilities
- **JsonUtils**: JSON serialization/deserialization
- **ExceptionUtil**: Exception handling utilities
- **DateUtils**: Date and time utilities
- **StringUtils**: String manipulation utilities
- **CollectionUtils**: Collection utilities
- **ObjectUtils**: Object utilities
- **BeanUtils**: Bean conversion utilities
- **ValidationUtils**: Validation utilities

## Common Patterns

### Result Handling

- Use `CommonResult<T>` for API responses
- Use `PageResult<T>` for paginated responses
- Use `success()` and `error()` static methods for creating results

### Data Transfer

- Use DTOs for API request/response
- Use `Convert` classes for DTO/Entity conversion
- Use `MapStruct` for object mapping

### Validation

- Use Bean Validation (`@Valid`, `@Validated`)
- Use custom validators for complex validation
- Use `ValidationUtils` for programmatic validation

### Security

- Use `SecurityUtils` for accessing current user
- Use `@PreAuthorize` for method-level security
- Use `@RequiresPermissions` for permission checks

### Multi-tenancy

- Use `TenantUtils` for tenant context
- Use `@TenantIgnore` to ignore tenant filtering
- Use `TenantContextHolder` to access current tenant

### Caching

- Use `@Cacheable` for method-level caching
- Use `RedisUtils` for direct Redis operations
- Use `CacheUtils` for common cache operations

### Logging

- Use SLF4J for logging
- Use `@OperateLog` for operation logging
- Use `LogUtils` for common logging patterns
