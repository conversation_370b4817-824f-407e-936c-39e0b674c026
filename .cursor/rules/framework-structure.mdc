---
description: 
globs: 
alwaysApply: false
---
# Yitong Framework Structure

The `yitong-framework` module contains common components and utilities used across the application.

## Key Components

- **yitong-common**: Common utilities, constants, and base classes
- **yitong-spring-boot-starter-web**: Web-related configuration and utilities
- **yitong-spring-boot-starter-security**: Security configuration and utilities
- **yitong-spring-boot-starter-mybatis**: Database access configuration
- **yitong-spring-boot-starter-redis**: Redis configuration and utilities
- **yitong-spring-boot-starter-file**: File storage abstraction
- **yitong-spring-boot-starter-job**: Job scheduling
- **yitong-spring-boot-starter-mq**: Message queue integration
- **yitong-spring-boot-starter-biz-tenant**: Multi-tenancy support
- **yitong-spring-boot-starter-biz-operatelog**: Operation logging
- **yitong-spring-boot-starter-biz-data-permission**: Data permission control

## Common Patterns

1. Each starter module follows Spring Boot's auto-configuration pattern
2. Configuration properties are prefixed with `yitong`
3. Autoconfiguration classes are in the `config` package
4. Core functionality is exposed through service interfaces
