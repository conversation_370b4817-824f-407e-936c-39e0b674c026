---
description: 
globs: 
alwaysApply: false
---
# Database Conventions

This project follows specific conventions for database design and access.

## Table Naming

- Use snake_case for table names
- Prefix tables with module name (e.g., `system_user`, `trade_order`)
- Use singular nouns for table names

## Column Naming

- Use snake_case for column names
- Primary key: `id` (bigint)
- Common audit columns:
  - `creator` (varchar): Creator username
  - `creator_id` (bigint): Creator ID
  - `updater` (varchar): Last updater username
  - `updater_id` (bigint): Last updater ID
  - `create_time` (datetime): Creation time
  - `update_time` (datetime): Last update time
  - `deleted` (tinyint): Soft delete flag (0: not deleted, 1: deleted)
  - `tenant_id` (bigint): Tenant ID for multi-tenancy

## MyBatis Usage

- Use XML mapping files for complex queries
- Use annotations for simple CRUD operations
- Use `MyBatis-Plus` for common CRUD operations
- Define custom SQL in the XML files
- Use dynamic SQL for complex conditions

## Database Access

- Use `BaseMapperX` for common operations
- Use `ServiceImpl` as base class for services
- Use `LambdaQueryWrapperX` for type-safe queries
- Implement soft delete with `deleted` flag

## Multi-Database Support

- The system supports multiple database types:
  - MySQL
  - PostgreSQL
  - Oracle
  - SQL Server
  - DM (Dameng)
- Use database-agnostic SQL when possible
- Use conditional SQL for database-specific features
