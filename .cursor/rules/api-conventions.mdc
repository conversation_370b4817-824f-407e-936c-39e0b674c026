---
description: 
globs: 
alwaysApply: false
---
# API Conventions

This project follows RESTful API design principles with some customizations.

## URL Structure

- Base URL is automatically determined by the controller package structure:
  - `com.yitong.octopus.module.{module}.controller.admin`: Admin backend APIs with prefix `/admin-api/{module}/{resource}`
  - `com.yitong.octopus.module.{module}.controller.app`: User-facing APIs with prefix `/app-api/{module}/{resource}`
- Use plural nouns for resources
- Use kebab-case for multi-word resources
- Examples:
  - `/admin-api/system/users`
  - `/app-api/trade/orders`
  - `/app-api/member/member-points`

## HTTP Methods

- `GET`: Retrieve resources
- `POST`: Create resources or perform actions
- `PUT`: Update resources (full update)
- `DELETE`: Delete resources
- `PATCH`: Partial update (not commonly used)

## Request Parameters

- Path variables: Used for resource identifiers
  - Example: `/admin-api/system/users/{userId}`
- Query parameters: Used for filtering, sorting, pagination
  - Example: `/admin-api/system/users?status=active&pageNo=1&pageSize=10`
- Request body: Used for resource creation and updates
  - Always use JSON format

## Response Format

All API responses follow the standard format:

```json
{
  "code": 0,       // 0 for success, non-zero for errors
  "data": {},      // Response data (object or array)
  "message": ""    // Error message if code is non-zero
}
```

## Pagination

Paginated responses follow this format:

```json
{
  "code": 0,
  "data": {
    "list": [],           // Array of items
    "total": 100,         // Total number of items
    "pageNo": 1,          // Current page number
    "pageSize": 10        // Items per page
  },
  "message": ""
}
```

## Error Handling

- Use appropriate HTTP status codes
- Include error details in the response body
- Use standardized error codes from `ErrorCodeConstants`

## API Documentation

- Use Swagger annotations for API documentation
- Include descriptions for all endpoints, parameters, and responses
- Group APIs by modules using tags