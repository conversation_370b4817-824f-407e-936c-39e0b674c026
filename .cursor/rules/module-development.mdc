---
description: 
globs: 
alwaysApply: false
---
# Module Development Guidelines

Each functional module in the Yitong Octopus Admin project follows a consistent structure and development pattern.

## Module Structure

- **{module}-api**: Public interfaces and DTOs
  - `controller`: Controller interfaces (if using API-first approach)
  - `dto`: Data Transfer Objects
  - `enums`: Enumeration classes
  - `vo`: View Objects for responses
  - `service`: Service interfaces

- **{module}-biz**: Implementation classes
  - `controller`: REST API controllers
  - `service`: Service implementations
  - `convert`: DTO/Entity conversion
  - `dal`: Data Access Layer
    - `dataobject`: Database entities
    - `mapper`: MyBatis mappers

## Development Flow

1. Define DTOs and service interfaces in the API module
2. Implement services in the BIZ module
3. Create controllers in the BIZ module
4. Define database entities and mappers in the DAL package

## Naming Conventions

- Controllers: `*Controller`
- Services: `*Service` (interface), `*ServiceImpl` (implementation)
- Mappers: `*Mapper`
- Entities: `*DO` (Data Object)
- DTOs: `*Request`, `*Response`
- Converters: `*Convert`

## Common Patterns

- Use `@Valid` for input validation
- Use `CommonResult<T>` for API responses
- Use `PageParam` and `PageResult<T>` for pagination
- Use `Convert` classes for DTO/Entity conversion
