---
description: 
globs: 
alwaysApply: false
---
# Code Conventions

This project follows the Alibaba Java Development Manual coding standards and conventions.

## Package Structure

- `com.yitong.octopus`: Root package
  - `.module.{module}`: Specific module packages
  - `.framework.{component}`: Framework components

## Naming Conventions

- Classes: PascalCase (e.g., `UserController`)
- Methods: camelCase (e.g., `getUserById`)
- Variables: camelCase (e.g., `userId`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_COUNT`)
- Packages: lowercase (e.g., `com.yitong.octopus.module.system`)

## Code Style

- Use 4 spaces for indentation
- Line width limit: 120 characters
- Always use braces for control structures
- Always add @Override annotation when implementing interface methods
- Use Lombok annotations to reduce boilerplate code

## Exception Handling

- Use custom exception classes for business exceptions
- Use `ServiceException` for service-layer exceptions
- Use `ErrorCode` for standardized error responses
- Use `CommonResult` for standardized responses
- Log exceptions with appropriate level (ERROR for system errors, WARN for business exceptions)

## Documentation

- Add JavaDoc comments for public classes and methods
- Include parameter descriptions and return value descriptions
- Document exceptions that may be thrown

## Testing

- Write unit tests for service and util classes
- Use JUnit 5 and Mockito for testing
- Follow the AAA pattern (Arrange, Act, Assert)
- Mock external dependencies
