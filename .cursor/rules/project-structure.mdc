---
description: 
globs: 
alwaysApply: false
---
# Yitong Octopus Admin Project Structure

This project is a comprehensive Java-based enterprise application built with Spring Boot. It follows a multi-module architecture with clear separation of concerns.

## Core Components

- **yitong-dependencies**: Maven dependency management for the entire project
- **yitong-framework**: Core framework extensions and utilities
- **yitong-server**: Main server application (backend for admin dashboard and user apps)
- **yitong-openapi**: OpenAPI server for external integrations

## Module Structure

Each functional module follows a consistent structure:
- **{module}-api**: Contains interfaces, DTOs, and enums for external consumption
- **{module}-biz**: Contains implementations, services, controllers, and DAOs

## Key Modules

- **yitong-module-system**: Core system functionality (users, roles, permissions)
- **yitong-module-member**: User/member management
- **yitong-module-infra**: Infrastructure services (file storage, job scheduling)
- **yitong-module-mall**: E-commerce functionality
- **yitong-module-mp**: WeChat public account integration
- **yitong-module-trade**: Order and payment processing
- **yitong-module-channel**: Channel management
- **yitong-module-merchant**: Merchant management
- **yitong-module-opensdk**: SDK integrations with external platforms

## Architecture

The application follows a layered architecture:
1. **Controller Layer**: Handles HTTP requests, input validation
2. **Service Layer**: Contains business logic
3. **Repository Layer**: Data access and persistence
4. **Domain Model**: Business entities and value objects

## Key Files

- [pom.xml](mdc:pom.xml): Root Maven configuration
- [yitong-server/src/main/java/com/yitong/octopus/server/YitongServerApplication.java](mdc:yitong-server/src/main/java/com/yitong/octopus/server/YitongServerApplication.java): Main application entry point
- [yitong-server/src/main/resources/application.yml](mdc:yitong-server/src/main/resources/application.yml): Main application configuration
