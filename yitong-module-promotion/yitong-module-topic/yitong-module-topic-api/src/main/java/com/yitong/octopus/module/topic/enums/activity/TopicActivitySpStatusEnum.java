package com.yitong.octopus.module.topic.enums.activity;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 状态：1：待审核，2：已通过 3：已拒绝
 */
@Getter
@AllArgsConstructor
public enum TopicActivitySpStatusEnum implements EnumKeyArrayValuable {

    WAITE(1, "待审核"),
    SUCCESS(2, "已通过"),
    FAIL(3, "已拒绝");

    public static final Object[] ARRAYS = Arrays.stream(values()).map(TopicActivitySpStatusEnum::getStatus).toArray();

    private final Integer status;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
