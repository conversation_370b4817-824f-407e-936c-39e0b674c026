package com.yitong.octopus.module.activity.service.admin.activity;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.framework.common.vo.CommonsStatusUpdateVo;
import com.yitong.octopus.module.activity.controller.admin.activity.vo.item.ActivityInfoItemPageReqVO;
import com.yitong.octopus.module.activity.controller.admin.activity.vo.item.ActivityInfoItemSaveReqVO;
import com.yitong.octopus.module.activity.controller.admin.activity.vo.item.ActivityInfoItemVisibleUpdateVo;
import com.yitong.octopus.module.activity.controller.app.activity.vo.activity.ActivityInfoItemCountXhsLbsRespVO;
import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 平台活动项 Service 接口
 *
 * <AUTHOR>
 */
public interface ActivityInfoItemService extends IService<ActivityInfoItemDO> {

    /**
     * 创建平台活动项
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInfoItem(@Valid ActivityInfoItemSaveReqVO createReqVO);

    /**
     * 更新平台活动项
     *
     * @param updateReqVO 更新信息
     */
    void updateInfoItem(@Valid ActivityInfoItemSaveReqVO updateReqVO);

    /**
     * 删除平台活动项
     *
     * @param id 编号
     */
    void deleteInfoItem(Long id);

    /**
     * 获得平台活动项
     *
     * @param id 编号
     * @return 平台活动项
     */
    ActivityInfoItemDO getInfoItemByIds(Long id);

    /**
     * 获得平台活动项分页
     *
     * @param pageReqVO 分页查询
     * @return 平台活动项分页
     */
    PageResult<ActivityInfoItemDO> getInfoItemPage(ActivityInfoItemPageReqVO pageReqVO);

    /**
     * 获得平台活动项列表
     *
     * @param activityId 活动Id
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getInfoItemListByActivityId(Long activityId);

    /**
     * 获得平台活动项列表
     *
     * @param activityIds 活动Id列表
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getInfoItemListByActivityIds(Collection<Long> activityIds);

    /**
     * 获得平台活动项列表[有效]
     *
     * @param activityIds 活动Id列表
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getInfoItemListValidByActivityIds(Collection<Long> activityIds);


    /**
     * 获得平台活动项列表[有效且可见]
     *
     * @param activityIds 活动Id列表
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getInfoItemListValidAndVisibleByActivityIds(Collection<Long> activityIds);

    /**
     * 获得平台活动项列表
     *
     * @param activityId 活动Id
     * @param activityItemId 活动项Id
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getInfoItemPreListByActivityId(Long activityId,Long activityItemId);

    /**
     * 获得平台活动项列表【有效】
     *
     * @param activityId 活动Id
     * @return 平台活动项分页
     */
    List<ActivityInfoItemDO> getValidInfoItemListByActivityId(Long activityId);

    /**
     * 更新活动项状态
     *
     * @param reqVO 更新信息
     */
    void updateInfoItemStatus(@Valid CommonsStatusUpdateVo reqVO);
    /**
     * 更新活动项可见
     *
     * @param reqVO 更新信息
     */
    void updateInfoItemVisible(@Valid ActivityInfoItemVisibleUpdateVo reqVO);

    /**
     * 获得平台活动项
     *
     * @param ids 编号列表
     * @return 平台活动项
     */
    List<ActivityInfoItemDO> getInfoItemByIds(List<Long> ids);

    /**
     * 获得平台活动项领取于使用汇总
     * @param activityCouponId
     * @param memberId
     * @return
     */
     List<ActivityInfoItemCountXhsLbsRespVO> getActivityItemSpuCountListByXhs(Long activityCouponId, Long memberId);

}