package com.yitong.octopus.module.activity.service.admin.activity;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.activity.controller.admin.activity.vo.booking.ActivityCouponBookingPageReqVO;
import com.yitong.octopus.module.activity.controller.admin.activity.vo.booking.ActivityCouponBookingSaveReqVO;
import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityCouponDO;
import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityCouponRedeemDO;
import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoItemSpuDO;
import com.yitong.octopus.module.activity.enums.activity.ActivityCouponBookingStatusEnum;
import com.yitong.octopus.module.activity.enums.activity.ActivityCouponStatusEnum;
import com.yitong.octopus.module.system.api.user.AdminUserApi;
import com.yitong.octopus.module.system.api.user.dto.AdminUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityCouponBookingDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.activity.dal.mysql.activity.ActivityCouponBookingMapper;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.activity.enums.ErrorCodeConstants.*;

/**
 * 活动预约记录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ActivityCouponBookingServiceImpl extends ServiceImpl<ActivityCouponBookingMapper,ActivityCouponBookingDO>  implements ActivityCouponBookingService {

    @Resource
    private ActivityCouponBookingMapper couponBookingMapper;

    @Lazy
    @Resource
    private ActivityCouponService activityCouponService;

    @Lazy
    @Resource
    private ActivityCouponRedeemService activityCouponRedeemService;

    @Lazy
    @Resource
    private ActivityInfoItemSpuService activityInfoItemSpuService;

    @Lazy
    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createCouponBooking(ActivityCouponBookingSaveReqVO createReqVO) {
        // 插入
        ActivityCouponBookingDO couponBooking = BeanUtils.toBean(createReqVO, ActivityCouponBookingDO.class);
        couponBookingMapper.insert(couponBooking);
        // 返回
        return couponBooking.getId();
    }

    @Override
    public void updateCouponBooking(ActivityCouponBookingSaveReqVO updateReqVO) {
        // 校验存在
        validateCouponBookingExists(updateReqVO.getId());
        // 更新
        ActivityCouponBookingDO updateObj = BeanUtils.toBean(updateReqVO, ActivityCouponBookingDO.class);
        couponBookingMapper.updateById(updateObj);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelCouponBooking(Long id) {
        // 校验存在
        ActivityCouponBookingDO activityCouponBooking = getCouponBooking(id);
        if (ObjectUtil.isNull(activityCouponBooking)){
            throw exception(COUPON_BOOKING_NOT_EXISTS);
        }
        if (!ActivityCouponBookingStatusEnum.VALID.getStatus().equals(activityCouponBooking.getStatus())){
            throw exception(COUPON_BOOKING_CANCEL_STATUS);
        }
        ActivityCouponDO activityCouponDO = activityCouponService.getCoupon(activityCouponBooking.getCouponId());
        if (ObjectUtil.isNull(activityCouponDO)){
            throw exception(COUPON_NOT_EXISTS);
        }
        if (!ActivityCouponStatusEnum.USE.getStatus().equals(activityCouponDO.getStatus())){
            throw exception(COUPON_BOOKING_CANCEL_STATUS_NOT_VALID,ActivityCouponStatusEnum.getByStatus(activityCouponDO.getStatus()).getName());
        }
        activityCouponBooking.setStatus(ActivityCouponBookingStatusEnum.CANCEL.getStatus());
        activityCouponBooking.setCancelTime(LocalDateTime.now());
        updateById(activityCouponBooking);
        activityInfoItemSpuService.releaseItemSpuStock(activityCouponBooking.getActivityItemSpuId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void redeemCouponBooking(Long id) {
        ActivityCouponBookingDO activityCouponBooking = getCouponBooking(id);
        if (ObjectUtil.isNull(activityCouponBooking)){
            throw exception(COUPON_BOOKING_NOT_EXISTS);
        }
        if (!ActivityCouponBookingStatusEnum.VALID.getStatus().equals(activityCouponBooking.getStatus())){
            throw exception(COUPON_BOOKING_CANCEL_STATUS);
        }
        ActivityCouponDO activityCouponDO = activityCouponService.getCoupon(activityCouponBooking.getCouponId());
        if (ObjectUtil.isNull(activityCouponDO)){
            throw exception(COUPON_NOT_EXISTS);
        }
        if (!ActivityCouponStatusEnum.USE.getStatus().equals(activityCouponDO.getStatus())){
            throw exception(COUPON_BOOKING_CANCEL_STATUS_NOT_VALID,ActivityCouponStatusEnum.getByStatus(activityCouponDO.getStatus()).getName());
        }

        ActivityCouponRedeemDO activityCouponRedeemDO = new ActivityCouponRedeemDO();
        activityCouponRedeemDO.setCouponId(activityCouponBooking.getCouponId());
        activityCouponRedeemDO.setActivityId(activityCouponBooking.getActivityId());
        activityCouponRedeemDO.setActivityItemId(activityCouponBooking.getActivityItemId());
        activityCouponRedeemDO.setMemberId(activityCouponBooking.getMemberId());
        activityCouponRedeemDO.setRedeemCode(activityCouponBooking.getBookingCode());
        activityCouponRedeemDO.setRedeemTime(LocalDateTime.now());
        activityCouponRedeemDO.setRedeemSpId(activityCouponBooking.getBookingSpId());
        activityCouponRedeemDO.setRedeemSpName(activityCouponBooking.getBookingSpName());

        AdminUserRespDTO adminUserResp = adminUserApi.getUser(SecurityFrameworkUtils.getLoginUserId());
        if (ObjectUtil.isNotNull(adminUserResp)){
            activityCouponRedeemDO.setRedeemUserId(adminUserResp.getId());
            activityCouponRedeemDO.setRedeemUserName(adminUserResp.getNickname());
        }
        //获取当前核销商家参加活动的品,并记录
        activityCouponRedeemDO.setRedeemSpuId(activityCouponBooking.getBookingSpuId());
        activityCouponRedeemDO.setRedeemSkuId(activityCouponBooking.getBookingSkuId());
        activityCouponRedeemDO.setRedeemSkuName(activityCouponBooking.getBookingSkuName());

        ActivityInfoItemSpuDO activityInfoItemSpuDO = activityInfoItemSpuService.getById(activityCouponBooking.getActivityItemSpuId());

        activityCouponRedeemDO.setRedeemSkuAmount(activityInfoItemSpuDO.getSkuSaleAmount());
        activityCouponRedeemDO.setRedeemSkuSettleAmount(activityInfoItemSpuDO.getSkuSettleAmount());
        activityCouponRedeemDO.setRedeemSkuSaleAmount(activityInfoItemSpuDO.getSkuSaleAmount());
        // 活动券码兑换
        Boolean result = activityCouponService.exchangeCouponById(activityCouponBooking.getCouponId());
        if (!result){
            log.warn("Activity coupon exchange error code={}", activityCouponBooking.getBookingCode());
            throw exception(COUPON_NOT_VALID);
        }
        activityCouponRedeemService.save(activityCouponRedeemDO);
    }

    @Override
    public void deleteCouponBooking(Long id) {
        // 校验存在
        validateCouponBookingExists(id);
        // 删除
        couponBookingMapper.deleteById(id);
    }

    private void validateCouponBookingExists(Long id) {
        if (couponBookingMapper.selectById(id) == null) {
            throw exception(COUPON_BOOKING_NOT_EXISTS);
        }
    }

    @Override
    public ActivityCouponBookingDO getCouponBooking(Long id) {
        return couponBookingMapper.selectById(id);
    }

    @Override
    public PageResult<ActivityCouponBookingDO> getCouponBookingPage(ActivityCouponBookingPageReqVO pageReqVO) {
        return couponBookingMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ActivityCouponBookingDO> getCouponBookingValidByCouponId(Long couponId) {
        return couponBookingMapper.selectList(new LambdaQueryWrapperX<ActivityCouponBookingDO>()
                .eq(ActivityCouponBookingDO::getCouponId,couponId)
                .eq(ActivityCouponBookingDO::getStatus, ActivityCouponBookingStatusEnum.VALID.getStatus())
        );
    }

    @Override
    public List<ActivityCouponBookingDO> getCouponBookingByCouponId(Long couponId) {
        return couponBookingMapper.selectList(ActivityCouponBookingDO::getCouponId,couponId);
    }

    @Override
    public List<ActivityCouponBookingDO> getCouponBookingByActivityIdAndMemberIdAndSpId(Long activityId, Long memberId, Long spId) {
        return couponBookingMapper.selectList(new LambdaQueryWrapperX<ActivityCouponBookingDO>()
                .eq(ActivityCouponBookingDO::getActivityId,activityId)
                .eq(ActivityCouponBookingDO::getBookingSpId,spId)
                .eq(ActivityCouponBookingDO::getMemberId,memberId)
                .eq(ActivityCouponBookingDO::getStatus, ActivityCouponBookingStatusEnum.VALID.getStatus())
        );
    }

}