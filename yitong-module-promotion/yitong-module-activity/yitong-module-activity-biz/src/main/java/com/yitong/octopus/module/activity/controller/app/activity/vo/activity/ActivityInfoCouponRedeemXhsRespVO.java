package com.yitong.octopus.module.activity.controller.app.activity.vo.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "前端 - 通兑券活动券码核销 Request VO")
@Data
public class ActivityInfoCouponRedeemXhsRespVO {
     /**
     * 活动项商品图片
     */
    private String spuMainImg;
    /**
     * 活动项商品图片
     */
    private String activityItemSpuImg;
    /**
     * 核销门店主图
     */
    private String storeMainImage;
    /**
     * 活动ID
     */
    private Long activityItemId;

    /**
     * 核销时间
     */
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime redeemTime;
    /**
     * 核销编码
     */
    private String redeemCode;
    /**
     * 核销人ID
     */
    private Long redeemUserId;
    /**
     * 核销人名称
     */
    private String redeemUserName;
    /**
     * 核销门店ID
     */
    private Long redeemStoreId;
    /**
     * 核销门店名称
     */
    private String redeemStoreName;
    /**
     * 核销商家ID
     */
    private Long redeemSpId;
    /**
     * 核销商家名称
     */
    private String redeemSpName;

    /**
     * 核销商家名称
     */
    private String redeemSkuName;

    /**
     * 核销商家SkuId
     */
    private Long redeemSkuId;

    /**
     * 核销商家SpuId
     */
    private Long redeemSpuId;
}

