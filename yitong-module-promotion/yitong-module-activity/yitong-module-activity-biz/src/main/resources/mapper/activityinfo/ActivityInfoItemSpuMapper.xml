<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.activity.dal.mysql.activity.ActivityInfoItemSpuMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
     -->

    <select id="getSpInfoApplyPage"
            resultType="com.yitong.octopus.module.activity.controller.admin.activity.vo.itemspu.ActivityInfoItemSpuPageRespVO"
            parameterType="com.yitong.octopus.module.activity.controller.admin.activity.vo.itemspu.ActivityInfoItemSpuPageReqVO">
             SELECT
                 a.id
                 ,a.activity_id
                 ,a.apply_time
                 ,a.sku_id
                 ,a.spu_id
                 ,a.sp_id
                 ,s.sp_name
                 ,spu.full_name `name`
                 ,spu.main_img spu_main_img
                 ,sku.sale_stock
                 ,sku.market_amount
                 ,sku.sale_amount
                 ,(SELECT count(1)FROM sp_goods_spu_store WHERE spu_id = a.spu_id) store_count
            FROM yt_activity_info_item_spu a
             JOIN sp_goods_sku sku ON a.sku_id = sku.id AND  sku.deleted = 0
             JOIN sp_goods_spu spu ON sku.spu_id = spu.id  AND  spu.deleted = 0
             JOIN sp_main_info s ON a.sp_id = s.id  AND  s.deleted = 0
            WHERE 1=1
            <if test="vo.activityId!=null">
                AND  a.activity_id = #{vo.activityId}
            </if>
            <if test="vo.spuName!=null and vo.spuName!=''">
                AND  spu.full_name LIKE CONCAT('%',#{vo.spuName},'%')
            </if>
            <if test="vo.spId!=null">
                AND a.sp_id = #{vo.spId}
            </if>
            <if test="vo.applyStatus!=null">
                AND a.apply_status = #{vo.applyStatus}
            </if>
            <if test="vo.submitChannel!=null">
                AND a.submit_channel = #{vo.submitChannel}
            </if>
            ORDER BY a.sort
    </select>

    <select id="selectConfirmListByActivityIdAndSpId"
            resultType="com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoItemSpuDO">
        SELECT
            sa.*
        FROM yt_activity_info_item_spu  sa
        WHERE sa.apply_status = 2
          AND sa.activity_id =  #{activityId}
          AND sa.sp_id=  #{spId}
        ORDER BY sa.sort
    </select>

    <select id="selectValidListByActivityItemIdAndActivityItemSpuIdAndSpuId"
            resultType="com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoItemSpuDO">
        SELECT
            isp.*
        FROM  yt_activity_info_item_spu isp
        WHERE isp.activity_item_id =#{activityItemId}  AND isp.deleted =0 AND isp.spu_id = #{spuId}
        <if test="activityItemSpuId !=null ">
            AND isp.id != #{activityItemSpuId}
        </if>
        ORDER BY isp.sort
    </select>

    <select id="getItemTotalCouponCountByActivityId" resultType="java.lang.Integer">
        SELECT
            SUM(exchange_count)
        FROM yt_activity_info_item
        WHERE activity_id = #{activityId}
          AND item_status = 1
          AND deleted =0
    </select>

    <update id="lockItemSpuStock" parameterType="java.lang.Long">
        UPDATE yt_activity_info_item_spu
        SET sale_stock = sale_stock -1
        WHERE id = #{id} AND sale_stock >0
    </update>

    <update id="releaseItemSpuStock" parameterType="java.lang.Long">
        UPDATE yt_activity_info_item_spu
        SET sale_stock = sale_stock + 1
        WHERE id = #{id}
    </update>

    <select id="getActivityStorePageByActivityId"
            resultType="com.yitong.octopus.module.activity.api.xhsminiapp.vo.store.ActivityItemStoreRespVo">
        SELECT
            a.*
            ,${vo.activityId} 'activityId'
        FROM  (
            SELECT
                DISTINCT
                ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_add
                ,ssi.sp_city
                ,ssi.rating
                ,ss.spu_img
                ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
            FROM (
                SELECT
                    DISTINCT
                    spus.store_id
                    ,aiis.sp_id
                    ,spus.spu_id
                    ,aiis.sort
                    ,aiis.spu_img
                FROM yt_activity_info_item_spu aiis
                JOIN sp_goods_spu_store spus ON aiis.spu_id = spus.spu_id AND spus.deleted = 0
                WHERE aiis.activity_id =  #{vo.activityId} AND aiis.deleted =0  AND aiis.status = 1
                <if test="vo.category != null and vo.category !='' ">
                    AND  aiis.category = #{vo.category}
                </if>
                ORDER BY aiis.sort DESC
            ) ss
            JOIN sp_store_info ssi ON ssi.id = ss.store_id AND ssi.deleted =0
            JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
            WHERE  1=1
            AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
            <if test="vo.storeName != null and vo.storeName != ''">
                AND ssi.store_name  like CONCAT('%', #{vo.storeName}, '%')
            </if>
            <if test="vo.city != null and vo.city !='' ">
                AND  ssi.sp_city = #{vo.city}
            </if>
            <if test="vo.radius != null ">
                HAVING  distance &lt;= #{vo.radius}
            </if>
            ORDER BY distance
        ) a
    </select>

    <resultMap id="ActivityStoreSpuRespVoResulMap" type="com.yitong.octopus.module.activity.api.xhsminiapp.vo.store.ActivityStoreSpuRespVo">
        <result column="spu_id" property="spuId"/>
        <result column="full_name" property="fullName"/>
        <result column="short_name" property="shortName"/>
        <result column="images" property="images" typeHandler="com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler"/>
        <result column="sku_id" property="skuId"/>
        <result column="sale_amount" property="saleAmount"/>
        <result column="market_amount" property="marketAmount"/>
    </resultMap>

    <select id="getActivityStoreSpuVo" resultMap="ActivityStoreSpuRespVoResulMap">
        SELECT
            spu.id spu_id
             ,spu.full_name
             ,spu.short_name
             ,spu.images
             ,sku.id sku_id
             ,sku.sale_amount
             ,sku.market_amount
        FROM yt_activity_info_item_spu aiis
         JOIN sp_goods_sku sku ON aiis.sku_id = sku.id
         JOIN sp_goods_spu spu ON sku.spu_id = spu.id
         JOIN sp_goods_spu_store sgss ON sgss.spu_id = spu.id
        WHERE  1=1
          AND aiis.activity_id = #{vo.activityId}
          AND sgss.store_id = #{vo.storeId}
          AND aiis.status = 1
          AND aiis.deleted = 0
        ORDER BY aiis.sort
    </select>

    <select id="getActivityStoreBySpuId"
            resultType="com.yitong.octopus.module.activity.api.xhsminiapp.vo.store.ActivityItemStoreRespVo">
        SELECT
            a.*
        FROM  (
            SELECT
                DISTINCT
                ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_add
                ,ssi.sp_city
                ,ssi.rating
                ,ss.spu_img
                ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
            FROM (
                SELECT
                DISTINCT
                spus.store_id
                ,aiis.sp_id
                ,spus.spu_id
                ,aiis.sort
                ,aiis.spu_img
                FROM yt_activity_info_item_spu aiis
                JOIN sp_goods_spu_store spus ON aiis.spu_id = spus.spu_id AND spus.deleted = 0
                WHERE aiis.activity_id =  #{vo.activityId} AND aiis.deleted =0  AND aiis.status = 1
                <if test="vo.category != null and vo.category !='' ">
                    AND  aiis.category = #{vo.category}
                </if>
                ORDER BY aiis.sort
            ) ss
            JOIN sp_store_info ssi ON ssi.id = ss.store_id AND ssi.deleted =0
            JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
            WHERE  1=1
            AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
            <if test="vo.storeName != null and vo.storeName != ''">
                AND ssi.store_name  like CONCAT('%', #{vo.storeName}, '%')
            </if>
            <if test="vo.city != null and vo.city !='' ">
                AND  ssi.sp_city = #{vo.city}
            </if>
            <if test="vo.radius != null ">
                HAVING  distance &lt;= #{vo.radius}
            </if>
            ORDER BY distance
        ) a

    </select>

    <select id="getValidItemSpListByActivityIdAndCategoryAndStoreId"  resultType="com.yitong.octopus.module.activity.dal.dataobject.activity.ActivityInfoItemSpuDO">
        SELECT
            ispu.*
        FROM yt_activity_info_item_spu ispu
         JOIN sp_goods_spu_store ss ON ispu.spu_id = ss.spu_id AND ss.deleted =0
        WHERE  ispu.`status` = 1  AND ispu.deleted =0
          AND ispu.activity_id =#{activityId}
          <if test="category !=null and category !='' ">
              AND ispu.category = #{category}
          </if>
          AND ss.store_id = #{storeId}
        ORDER BY ispu.sort
    </select>

    <resultMap id="ActivityStoreSpuActivityRespVoResulMap" type="com.yitong.octopus.module.activity.api.xhsminiapp.vo.store.ActivityStoreSpuActivityRespVo">
        <result column="activity_id" property="activityId"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_image_url" property="activityImageUrl"/>
        <result column="activity_start_time" property="activityStartTime"/>
        <result column="activity_end_time" property="activityEndTime"/>
        <result column="activity_desc" property="activityDesc"/>
        <result column="activity_rule_info" property="activityRuleInfo"/>
        <result column="privileges" property="privileges"/>
        <result column="take_type" property="takeType"/>
        <result column="discount_type" property="discountType"/>
        <result column="take_limit_count" property="takeLimitCount"/>
        <result column="discount_percent" property="discountPercent"/>
        <result column="discount_limit_price" property="discountLimitPrice"/>
        <result column="spu_count" property="spuCount"/>
        <result column="sp_store_count" property="spStoreCount"/>
        <result column="sp_count" property="spuCount"/>
        <!-- spu 相关-->
        <result column="spu_id" property="spuId"/>
        <result column="spu_type" property="spuType"/>
        <result column="full_name" property="fullName"/>
        <result column="short_name" property="shortName"/>
        <result column="images" property="images" typeHandler="com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler"/>
        <result column="sku_id" property="skuId"/>
        <result column="sale_amount" property="saleAmount"/>
        <result column="market_amount" property="marketAmount"/>
    </resultMap>

    <select id="getStoreSpuActivityList" resultMap="ActivityStoreSpuActivityRespVoResulMap">
        SELECT
         spu.id spu_id
        ,spu.type spu_type
        ,spu.full_name
        ,spu.short_name
        ,spu.images
        ,sku.id sku_id
        ,sku.market_amount
        ,sku.sale_amount
        FROM sp_goods_spu spu
        JOIN sp_goods_sku sku ON spu.id = sku.spu_id AND  sku.deleted = 0
        JOIN sp_goods_spu_store spus ON spu.id = spus.spu_id AND  spus.deleted = 0
        JOIN yt_activity_info_item_spu ispu ON ispu.spu_id = spus.spu_id AND  ispu.deleted = 0
        WHERE  1=1
          AND ispu.activity_id = #{vo.activityId}
          AND spus.store_id = #{vo.storeId}
        ORDER BY ispu.sort
    </select>

    <select id="getActivityStoreSpuList" resultMap="ActivityStoreSpuActivityRespVoResulMap">
        SELECT
            ai.id activity_id
            ,ai.activity_name
            ,ai.activity_type
            ,ai.activity_image_url
            ,ai.activity_start_time
            ,ai.activity_end_time
            ,ai.activity_desc
            ,ai.activity_rule_info
            ,ai.privileges
            ,ai.take_type
            ,ai.discount_type
            ,ai.take_limit_count
            ,ai.discount_percent
            ,ai.discount_limit_price
            ,ai.spu_count
            ,ai.sp_store_count
            ,ai.sp_count
            ,spu.id spu_id
            ,spu.type spu_type
            ,spu.full_name
            ,spu.short_name
            ,spu.images
            ,sku.id sku_id
            ,sku.market_amount
            ,sku.sale_amount
        FROM sp_goods_spu spu
        JOIN sp_goods_sku sku ON spu.id = sku.spu_id
        JOIN sp_goods_spu_store spus ON spu.id = spus.spu_id
        JOIN yt_activity_info_item_spu ispu ON ispu.spu_id = spus.spu_id
        JOIN yt_activity_info ai ON ai.id = ispu.activity_id AND ai.deleted = 0
        WHERE  1=1
            AND spus.store_id = #{storeId}
            AND ai.activity_status = 2
            AND ai.activity_start_time &lt;= NOW() AND ai.activity_end_time >=NOW()
        ORDER BY ai.sort,ispu.sort
    </select>

    <resultMap id="ActivityStoreActivityRespVoResulMap" type="com.yitong.octopus.module.activity.api.xhsminiapp.vo.store.ActivityStoreActivityRespVo">
        <result column="activity_id" property="activityId"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_type" property="activityType"/>
        <result column="activity_image_url" property="activityImageUrl"/>
        <result column="activity_start_time" property="activityStartTime"/>
        <result column="activity_end_time" property="activityEndTime"/>
        <result column="activity_desc" property="activityDesc"/>
        <result column="activity_rule_info" property="activityRuleInfo"/>
        <result column="privileges" property="privileges"/>
        <result column="take_type" property="takeType"/>
        <result column="discount_type" property="discountType"/>
        <result column="take_limit_count" property="takeLimitCount"/>
        <result column="discount_percent" property="discountPercent"/>
        <result column="discount_limit_price" property="discountLimitPrice"/>
        <result column="spu_count" property="spuCount"/>
        <result column="sp_store_count" property="spStoreCount"/>
        <result column="sp_count" property="spuCount"/>
    </resultMap>

    <select id="getActivityStoreList" resultMap="ActivityStoreActivityRespVoResulMap">
        SELECT
            ai.id activity_id
            ,ai.activity_name
            ,ai.activity_type
            ,ai.activity_image_url
            ,ai.activity_start_time
            ,ai.activity_end_time
            ,ai.activity_desc
            ,ai.activity_rule_info
            ,ai.privileges
            ,ai.take_type
            ,ai.discount_type
            ,ai.take_limit_count
            ,ai.discount_percent
            ,ai.discount_limit_price
            ,ai.spu_count
            ,ai.sp_store_count
            ,ai.sp_count
        FROM yt_activity_info ai
        WHERE  ai.deleted = 0
        AND ai.id IN  (
            SELECT
            DISTINCT
            ispu.activity_id
            FROM  yt_activity_info_item_spu ispu
            JOIN sp_goods_spu_store spus ON spus.spu_id = ispu.spu_id
            WHERE spus.store_id = #{storeId}
        )
        AND ai.activity_status = 2
        AND ai.activity_start_time  &lt;= NOW() AND ai.activity_end_time >=NOW()
        ORDER BY ai.sort
    </select>


</mapper>
