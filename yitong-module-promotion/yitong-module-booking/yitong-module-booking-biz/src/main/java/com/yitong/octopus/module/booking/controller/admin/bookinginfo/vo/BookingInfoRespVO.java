package com.yitong.octopus.module.booking.controller.admin.bookinginfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.framework.jackson.core.databind.LocalDateTimeYmdSerializer;
import com.yitong.octopus.module.booking.api.utils.jackson.BookingServiceNameJsonSerializer;
import com.yitong.octopus.module.booking.api.utils.jackson.BookingSpaceNameJsonSerializer;
import com.yitong.octopus.module.booking.api.utils.jackson.BookingStaffNameJsonSerializer;
import com.yitong.octopus.module.diyform.api.utils.jackson.FormDiyNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 预约详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookingInfoRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "32112")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "商家ID", example = "26412")
    @ExcelProperty("商家ID")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "HH")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "图片")
    @ExcelProperty("图片")
    private String mainImg;

    /**
     * 预约类型 1 酒店
     */
    @Schema(description = "预约类型 1 酒店")
    @ExcelProperty("预约类型")
    private Integer bookingType;

    @Schema(description = "状态", example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）", example = "2")
    @ExcelProperty("预约审核（1 提交预约后,自动审核通过,并占用库存、2 提交预约后,占用库存资源,订单需审核,审核通过代表预约成功）")
    private Integer applyAuditType;

    @Schema(description = "发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）", example = "2")
    @ExcelProperty("发起预约配置参数（1 无限制、2 需要提前N 小时、3  需要提前N 天）")
    private Integer applyType;

    @Schema(description = "发起预约具体配置参数（小时或天数）")
    @ExcelProperty("发起预约具体配置参数（小时或天数）")
    private Integer applyTimes;

    @Schema(description = "取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）", example = "2")
    @ExcelProperty("取消预约（1 无限制、2 需要提前N 小时、3  需要提前N 天）")
    private Integer cancelType;

    @Schema(description = "取消预约具体配置参数（小时或天数）")
    @ExcelProperty("取消预约具体配置参数（小时或天数）")
    private Integer cancelTimes;

    @Schema(description = "更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）", example = "1")
    @ExcelProperty("更改预约（1 无限制、2 需要提前N 小时、3  需要提前N 天、 0不允许更改）")
    private Integer changeType;

    @Schema(description = "更改预约具体参数（小时或天数）")
    @ExcelProperty("更改预约具体参数（小时或天数）")
    private Integer changeTimes;

    @Schema(description = "预约日期（0 全部日期可用、1 指定日期可用）", example = "1")
    @ExcelProperty("预约日期（0 全部日期可用、1 指定日期可用）")
    private Integer bookingDateType;

    @Schema(description = "预约可用具体日期")
    @ExcelProperty("预约可用具体日期")
    private List<String> bookingDate;

    @Schema(description = "不可预约日期（ 0，不设定 1 每周不可用、2 节假日不可以、3指定某天不可用）", example = "2")
    @ExcelProperty("不可预约日期（ 0，不设定 1 每周不可用、2 节假日不可以、3指定某天不可用）")
    private Integer bookingUnableType;

    @Schema(description = "不可预约具体日期")
    @ExcelProperty("不可预约具体日期")
    private List<String> bookingUnableDate;

    @Schema(description = "每周不可用(具体周几)")
    @ExcelProperty("每周不可用(具体周几)")
    private List<String> bookingUnableWeek;

    @Schema(description = "不可使用具体节假日")
    @ExcelProperty("不可使用具体节假日")
    private List<String> bookingUnableFestival;

    @Schema(description = "日历开始日期")
    @ExcelProperty("日历开始日期")
    @JsonSerialize(using = LocalDateTimeYmdSerializer.class)
    private LocalDateTime bookingStartDate;

    @Schema(description = "日历结束日期")
    @ExcelProperty("日历结束日期")
    @JsonSerialize(using = LocalDateTimeYmdSerializer.class)
    private LocalDateTime bookingEndDate;

    @Schema(description = "预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）")
    @ExcelProperty("预约次数限制（下单人手机号控制 0 无限制、同一日期内根据数量限制）")
    private Integer bookingNum;

    @Schema(description = "服务备注", example = "你猜")
    @ExcelProperty("服务备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 申请表单
     */
    @Schema(description = "申请表单", example = "你猜")
    @JsonSerialize(using = FormDiyNameJsonSerializer.class)
    private Long diyFormId;
    /**
     * 申请奖品Id
     */
    @Schema(description = "申请奖品", example = "你猜")
    @JsonSerialize(using = SpGoodsApplyGiftSpuNameJsonSerializer.class)
    private Long applyGiftId;

    @Schema(description = "预约详情与商品关系列表")
    @JsonSerialize(using = SpGoodsSpuNameListJsonSerializer.class)
    private List<Long> spuIds;

    @Schema(description = "全部门店")
    private Boolean allStore;

    @Schema(description = "预约详情与门店关系列表")
    @JsonSerialize(using = SpStoreNameListJsonSerializer.class)
    private List<Long> storeIds;

    @Schema(description = "空间Id", example = "8576")
    @JsonSerialize(using = BookingSpaceNameJsonSerializer.class)
    private Long spaceId;

    @Schema(description = "服务Id", example = "8576")
    @JsonSerialize(using = BookingServiceNameJsonSerializer.class)
    private Long serviceId;

    @Schema(description = "人员Id", example = "8576")
    @JsonSerialize(using = BookingStaffNameJsonSerializer.class)
    private Long staffId;
}