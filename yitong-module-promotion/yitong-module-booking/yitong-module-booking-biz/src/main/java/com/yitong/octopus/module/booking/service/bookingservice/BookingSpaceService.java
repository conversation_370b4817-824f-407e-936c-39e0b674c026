package com.yitong.octopus.module.booking.service.bookingservice;

import javax.validation.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.booking.controller.admin.bookingservice.vo.*;
import com.yitong.octopus.module.booking.dal.dataobject.bookingservice.BookingSpaceDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import java.util.Collection;
import java.util.List;

/**
 * 商家空间 Service 接口
 *
 * <AUTHOR>
 */
public interface BookingSpaceService extends IService<BookingSpaceDO>{

    /**
     * 创建商家空间
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long createSpace(@Valid BookingSpaceSaveReqVO reqVO);

    /**
     * 更新商家空间
     *
     * @param reqVO 更新信息
     */
    void updateSpace(@Valid BookingSpaceSaveReqVO reqVO);

    /**
     * 删除商家空间
     *
     * @param id 编号
     */
    void deleteSpace(Long id);

    /**
     * 获得商家空间
     *
     * @param id 编号
     * @return 商家空间
     */
    BookingSpaceDO getSpace(Long id);

    /**
     * 获得商家空间分页
     *
     * @param pageReqVO 分页查询
     * @return 商家空间分页
     */
    PageResult<BookingSpaceDO> getSpacePage(BookingSpacePageReqVO pageReqVO);

    /**
     * 获得商家空间
     *
     * @param spId 商家Id
     * @return 商家空间
     */
    List<BookingSpaceDO> getSpaceValidListBySpId(Long spId);

    /**
     * 获得商家空间
     *
     * @param ids id列表
     * @return 商家空间
     */
    List<BookingSpaceDO> getSpaceListByIds(List<Long> ids);

    // ################################### 空间与门店的关系  ###################################

    /**
     * 根据类型查询空间关联的门店列表
     * @param spaceId 服务Id
     * @return
     */
    List<BookingServiceStoreSimpleRefRespVO> getStoreRefBySpaceId(Long spaceId);

    /**
     * 保存空间与门店的关系
     * @param spaceId 服务Id
     * @return
     */
    void batchSpaceStoreRef(Long spaceId, Collection<Long> storeIds);

    // ################################## 与【服务，人员】关联 ##################################
    /**
     * 根据类型查询空间关联的服务列表
     * @param spaceId 空间Id
     * @return
     */
    List<BookingServiceSimpleRefRespVO> getServiceRefBySpaceId(Long spaceId);

    /**
     * 保存服务与空间的关系
     * @param spaceId 空间Id
     * @param refIds 服务Id列表
     * @return
     */
    void batchServiceSpaceRef(Long spaceId, Collection<Long> refIds);

    /**
     * 根据类型查询空间关联的人员列表
     * @param spaceId 空间Id
     * @return
     */
    List<BookingServiceSimpleRefRespVO> getStaffRefBySpaceId(Long spaceId);

    /**
     * 保存空间与人员的关系
     * @param spaceId 空间Id
     * @param refIds 人员Id列表
     * @return
     */
    void batchSpaceStaffRef(Long spaceId, Collection<Long> refIds);
}