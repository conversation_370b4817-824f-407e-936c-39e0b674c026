package com.yitong.octopus.module.booking.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 预约类型
 * 类型：1：酒店
 */
@Getter
@AllArgsConstructor
public enum BookingTypeEnum implements EnumKeyArrayValuable {

    HOTEL(1, "酒店")
    ;

    public static final Object[] ARRAYS = Arrays.stream(values()).map(BookingTypeEnum::getType).toArray();

    private final Integer type;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
