package com.yitong.octopus.module.trade.controller.admin.coupon.vo;

import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import com.yitong.octopus.framework.excel.core.convert.MoneyConvert;
import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 券码 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class TradeCouponExcelVO {

    @ExcelProperty("券码")
    private String code;

    @ExcelProperty(value = "订单支付金额",converter = MoneyConvert.class)
    private Long amount;

    @ExcelProperty(value = "状态",converter = DictConvert.class)
    @DictFormat("yt_trade_coupon_status")
    private Integer status;

    @ExcelProperty("使用有效期开始")
    private LocalDateTime useDateFrom;

    @ExcelProperty("使用有效期截至")
    private LocalDateTime useDateTo;

    @ExcelProperty("服务商ID")
    private String agentId;

    @ExcelProperty("服务商名称")
    private String agentName;

    @ExcelProperty("商户ID")
    private String merchantId;

    @ExcelProperty("商户名")
    private String merchantName;

    @ExcelProperty("商品SpuId")
    private String spuId;

    @ExcelProperty("商品名称")
    private String skuName;

    @ExcelProperty("商品ID")
    private String skuId;

    @ExcelProperty("退款时间")
    private LocalDateTime refundTime;

    @ExcelProperty("核销时间")
    private LocalDateTime redeemTime;

    @ExcelProperty("核销人名称")
    private String redeemUserName;

    @ExcelProperty("核销门店名称")
    private String redeemStoreName;

    @ExcelProperty("渠道名称")
    private String channelName;

    @ExcelProperty("渠道订单号")
    private String channelOrderId;

    @ExcelProperty("渠道核销结果 ")
    private Boolean channelRedeemCode;

    @ExcelProperty("渠道核销结果描述")
    private String channelRedeemMsg;

    @ExcelProperty("支付时间")
    private LocalDateTime payTime;

    @ExcelProperty("交易时间")
    private LocalDateTime applyTime;
}
