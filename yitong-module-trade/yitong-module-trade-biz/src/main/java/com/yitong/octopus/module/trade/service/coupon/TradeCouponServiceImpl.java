package com.yitong.octopus.module.trade.service.coupon;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.yitong.octopus.framework.common.util.date.DateUtils;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.trade.api.coupon.TradeCouponChannelApi;
import com.yitong.octopus.module.trade.api.coupon.dto.TradeCouponRedeemReqDto;
import com.yitong.octopus.module.trade.api.coupon.dto.TradeCouponRedeemCancelReqDto;
import com.yitong.octopus.module.trade.enums.TradeCouponStatusEnum;
import com.yitong.octopus.module.trade.enums.TradeOrderAfterSaleStatusEnum;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import com.yitong.octopus.module.trade.controller.admin.coupon.vo.*;
import com.yitong.octopus.module.trade.dal.dataobject.coupon.TradeCouponDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.trade.dal.mysql.coupon.TradeCouponMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.trade.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.module.trade.enums.TradeCouponStatusEnum.*;

/**
 * 券码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TradeCouponServiceImpl extends ServiceImpl<TradeCouponMapper,TradeCouponDO> implements TradeCouponService {

    @Lazy
    @Resource
    private TradeCouponChannelApi tradeCouponChannelApi;

    @Override
    public String createCoupon(TradeCouponSaveReqVO reqVO) {
        // 插入
        TradeCouponDO coupon = BeanUtil.toBean(reqVO,TradeCouponDO.class);
        coupon.setStatus(TradeCouponStatusEnum.INIT.getStatus());
        coupon.setCrtTime(LocalDateTime.now());
        coupon.setUpdTime(LocalDateTime.now());
        getBaseMapper().insert(coupon);
        // 返回
        return coupon.getCode();
    }

    @Override
    public void updateCoupon(TradeCouponSaveReqVO reqVO) {
        // 校验存在
        validateCouponExists(reqVO.getId());
        // 更新
        TradeCouponDO updateObj = BeanUtil.toBean(reqVO,TradeCouponDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteCoupon(Long id) {
        // 校验存在
        validateCouponExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateCouponExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(TRADE_COUPON_NOT_EXISTS);
        }
    }

    @Override
    public TradeCouponDO getCouponById(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public List<TradeCouponDO> getCouponList(Collection<Long> ids) {
        return getBaseMapper().selectBatchIds(ids);
    }

    @Override
    public List<TradeCouponDO> getCouponListByOrderId(Long orderId) {
        return getBaseMapper().selectList(new LambdaQueryWrapperX<TradeCouponDO>()
                .eq(TradeCouponDO::getOrderId,orderId));
    }

    @Override
    public List<TradeCouponDO> getCouponListRedeemByLotteryId(Collection<Long> lotteryIds) {
        return getBaseMapper().selectListRedeemByLotteryIds(lotteryIds);
    }

    @Override
    public PageResult<TradeCouponDO> getCouponPage(TradeCouponPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public List<TradeCouponDO> getCouponList(TradeCouponPageReqVO exportReqVO) {
        return getBaseMapper().selectList(exportReqVO);
    }

    @Override
    public PageResult<TradeCouponRespVO> getCouponVoPage(TradeCouponPageReqVO pageReqVO) {
        return getBaseMapper().selectVoPage(pageReqVO);
    }

    @Override
    public List<TradeCouponRespVO> getCouponVoList(TradeCouponPageReqVO exportReqVO) {
        return getBaseMapper().selectVoList(exportReqVO);
    }

    @Override
    public TradeCouponDO getCouponByCode(String code) {
        return getBaseMapper().selectOne(new LambdaQueryWrapperX<TradeCouponDO>().eq(TradeCouponDO::getCode,code));
    }

    @Override
    public void redeem(TradeCouponRedeemReqVO reqVO) {
        if (ObjectUtil.isNull(reqVO.getRedeemTime())){
            reqVO.setRedeemTime(LocalDateTime.now());
        }

        TradeCouponDO tradeCouponDO = getCouponByCode(reqVO.getCode());
        if (ObjectUtil.isNull(tradeCouponDO)){
            throw exception(TRADE_COUPON_NOT_EXISTS);
        }
        if (!INIT.getStatus().equals(tradeCouponDO.getStatus())){
            throw exception(TRADE_COUPON_STATUS_ERROR);
        }
        TradeCouponRedeemReqDto req = new TradeCouponRedeemReqDto();
        req.setOrderId(String.valueOf(tradeCouponDO.getOrderId()));
        req.setRedeem_time(DateUtils.of(reqVO.getRedeemTime()).getTime()/1000);
        req.setSp_id(reqVO.getSpId());
        req.setRedeem_user_id(reqVO.getRedeemUserId());
        req.setStore_id(reqVO.getStoreId());
        req.setOut_codes(Lists.newArrayList(new TradeCouponRedeemReqDto.OutCode(reqVO.getCode())));
        tradeCouponChannelApi.redeem(req);
    }

    @Override
    public void redeemCancel(TradeCouponRedeemCancelReqVO reqVO) {
        TradeCouponDO tradeCouponDO = getCouponByCode(reqVO.getCode());
        if (ObjectUtil.isNull(tradeCouponDO)){
            throw exception(TRADE_COUPON_NOT_EXISTS);
        }
        if (!REDEEMED.getStatus().equals(tradeCouponDO.getStatus())){
            throw exception(TRADE_COUPON_STATUS_ERROR);
        }
        //再次校验24小时
        if (DateUtils.in24H(tradeCouponDO.getRedeemTime())){
            throw exception(TRADE_COUPON_CANCEL_REDEEM_24H_ERROR);
        }
        TradeCouponRedeemCancelReqDto req = new TradeCouponRedeemCancelReqDto();
        req.setSp_id(reqVO.getSpId());
        req.setRedeem_user_id(reqVO.getRedeemUserId());
        req.setCode(reqVO.getCode());
        tradeCouponChannelApi.redeemCancel(req);
    }

    @Override
    public Long getSaleTotalBySpuIdAndDateRange(Long spuId, LocalDateTime start, LocalDateTime end) {
        return getBaseMapper().selectCount(new LambdaQueryWrapperX<TradeCouponDO>()
                .eq(TradeCouponDO::getSpuId,spuId)
                .ne(TradeCouponDO::getStatus, DISCARD.getStatus())
                .between(TradeCouponDO::getCrtTime,start,end)
        );
    }

    @Override
    public List<TradeCouponDO> getTradeCouponByOrderNum(String couponOrderNum) {
        return getBaseMapper().selectList(TradeCouponDO::getChannelOrderId,couponOrderNum);
    }

    @Override
    public List<TradeCouponDO> getCouponListByOrderIds(Collection<Long> orderIds) {
        return getBaseMapper().selectList(new LambdaQueryWrapperX<TradeCouponDO>().in(TradeCouponDO::getOrderId,orderIds));
    }

    @Override
    public int updateTradeCouponRefundByApply(Long couponId, Long afterSaleId) {
        return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.APPLY.getStatus())
                .set(TradeCouponDO::getAfterSaleId,afterSaleId)
                .set(TradeCouponDO::getStatus, DISCARD.getStatus())
                .set(TradeCouponDO::getRefundTime, LocalDateTime.now())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.NO_APPLY.getStatus())
        );
    }

    @Override
    public int updateTradeCouponRefundRedeemByApply(Long couponId, Long afterSaleId) {
        return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.APPLY.getStatus())
                .set(TradeCouponDO::getAfterSaleId,afterSaleId)
                .set(TradeCouponDO::getRefundTime, LocalDateTime.now())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.NO_APPLY.getStatus())
        );
    }

    @Override
    public int updateTradeCouponRefundByApplyCancel(Long couponId) {
        return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.NO_APPLY.getStatus())
                .set(TradeCouponDO::getAfterSaleId,null)
                .set(TradeCouponDO::getStatus, INIT.getStatus())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.APPLY.getStatus())
        );
    }

    @Override
    public int updateTradeCouponRefundByConfirm(Long couponId) {
        return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.SELLER_AGREE.getStatus())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.APPLY.getStatus())
        );
    }

    @Override
    public int updateTradeCouponRefundByReject(Long couponId) {
       return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.SELLER_DISAGREE.getStatus())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.SELLER_AGREE.getStatus())
        );
    }

    @Override
    public int updateTradeCouponRefundByFinish(Long couponId) {
         return getBaseMapper().update(new LambdaUpdateWrapper<TradeCouponDO>()
                .set(TradeCouponDO::getAfterSaleStatus,TradeOrderAfterSaleStatusEnum.COMPLETE.getStatus())
                .set(TradeCouponDO::getStatus, DISCARD.getStatus())
                .eq(TradeCouponDO::getId,couponId)
                .eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.SELLER_AGREE.getStatus())
                 .or().eq(TradeCouponDO::getAfterSaleStatus, TradeOrderAfterSaleStatusEnum.APPLY.getStatus())
        );
    }

    @Override
    public List<TradeCouponDO> getExpireCouponByAppId(Long appId) {
        return getBaseMapper().selectList(
                new LambdaQueryWrapperX<TradeCouponDO>()
                        .eq(TradeCouponDO::getStatus, INIT.getStatus())
                        .eq(TradeCouponDO::getAppId, appId)
                        .lt(TradeCouponDO::getUseDateTo, LocalDateTime.now())
        );
    }
}