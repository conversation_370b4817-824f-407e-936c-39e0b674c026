<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.trade.dal.mysql.order.TradeOrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getExpireOrderByAppId"
            resultType="com.yitong.octopus.module.trade.dal.dataobject.order.TradeOrderDO">
        SELECT
            *
        FROM t_order
        WHERE `status` = 0
        AND app_id = #{appId}
        AND expire_time &lt; NOW()
    </select>

    <resultMap id="AppTradeOrderDtoMap" type="com.yitong.octopus.module.trade.api.order.vo.AppTradeOrderDto">
        <result column="merchant_id" property="spId" />
    </resultMap>

    <select id="selectOrderCouponPageByStatusAndAppIdAndUserId"  resultMap="AppTradeOrderDtoMap">
        SELECT
            t.*
            ,t1.status coupon_status
        FROM t_order t
        LEFT JOIN t_coupon t1 ON t1.order_id = t.id
        WHERE  t.member_id = #{userId}
        <if test="orderStatus == 1 ">
            AND t.status = 0
        </if>
        <if test="orderStatus == 2 ">
            AND t.status = 1 AND t1.status = 0
        </if>
        <if test="orderStatus == 3 ">
            AND t.status = 1 AND t1.status = 1
        </if>
        <if test="orderStatus == 4 ">
            AND ((t.status = 1 AND t1.status = 2) OR (t.status = 2))
        </if>
        ORDER BY t.id desc
    </select>
</mapper>
