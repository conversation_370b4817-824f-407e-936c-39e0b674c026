# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-module Spring Boot application for 一筒科技 (Yitong Technology), based on the RuoYi-Vue Pro framework. It's an enterprise-level platform with modules for CRM, e-commerce, content management, and various business domains.

## Key Commands

### Build and Run
```bash
# Build the entire project
mvn clean package -DskipTests

# Run the application locally
cd yitong-server
mvn spring-boot:run -Dspring.profiles.active=local

# Run tests
mvn test                                    # All tests
mvn test -Dtest=TestClassName              # Specific test class
mvn test -Dtest=TestClassName#methodName   # Specific test method
```

### Deployment
```bash
# Uses Jenkins pipeline and deployment script
./bin/deploy.sh
```

## Architecture Overview

### Module Structure
The project follows a modular architecture with these key modules:
- `yitong-server/` - Main Spring Boot application that aggregates all modules
- `yitong-dependencies/` - Centralized dependency management
- `yitong-framework/` - Core framework with common utilities and configurations
- `yitong-module-*` - Business modules organized by domain:
  - `system` - System management, users, roles, permissions
  - `infra` - Infrastructure components (file storage, config, jobs)
  - `member` - Member/user management
  - `mall` - E-commerce functionality
  - `trade` - Trading/transaction system
  - `merchant` - Merchant management
  - `aigc` - AI-generated content integration

### Technology Stack
- **Backend**: Spring Boot 2.7.18, Java 8
- **Database**: MySQL 5.7/8.0+ with MyBatis Plus *******
- **Cache**: Redis 5.0/6.0
- **Workflow**: Flowable 6.8.0
- **API Docs**: SpringDoc with Knife4j UI
- **Security**: Spring Security with JWT tokens

### Key Patterns
1. **Multi-tenancy**: Built-in SaaS support with tenant isolation
2. **Modular Design**: Each business domain is a separate Maven module
3. **Service Layer**: Business logic in service classes with @Service annotation
4. **MyBatis Plus**: Repository pattern with BaseMapper interfaces
5. **Unified Response**: Common response wrapper for API endpoints
6. **Validation**: JSR303 validation with custom validators

## Development Environment

### Database Setup
- MySQL database: `yitong_dev` (connection details in application-local.yaml)
- Redis on localhost:6379 (database 1)
- Database scripts in `/sql/mysql/` directory

### Application Configuration
- Main config: `application.yaml`
- Environment configs: `application-{env}.yaml` (local, dev, prod)
- Default port: 48080
- Health check: `http://127.0.0.1:48080/actuator/health/`

### Important URLs
- Swagger UI: `http://localhost:48080/swagger-ui`
- API Docs: `http://localhost:48080/v3/api-docs`

## Code Conventions

### Package Structure
```
cn.jianwoo.octopus.admin
├── controller    # REST API endpoints
├── service       # Business logic interfaces
│   └── impl      # Service implementations
├── dal           # Data access layer
│   ├── dataobject # Database entities (DO)
│   └── mapper     # MyBatis mappers
├── convert       # MapStruct converters
└── api           # Module API interfaces
```

### Naming Conventions
- Controllers: `*Controller`
- Services: `*Service` (interface) and `*ServiceImpl`
- Data Objects: `*DO`
- Mappers: `*Mapper`
- API responses use `CommonResult<T>` wrapper

### Common Annotations
- `@Tag` - Swagger API documentation
- `@PreAuthorize` - Security permissions
- `@Validated` - Request validation
- `@SaaSIgnore` - Bypass tenant filtering

## Testing Approach
- Unit tests use JUnit 5 and Mockito
- Test classes follow `*Test` naming convention
- Integration tests use `@SpringBootTest`
- Mock external dependencies with `@MockBean`

## Build Properties
- Maven version management: Uses `${revision}` property (1.7.2-snapshot)
- Java version: 1.8
- Spring Boot parent: 2.7.18
- Character encoding: UTF-8