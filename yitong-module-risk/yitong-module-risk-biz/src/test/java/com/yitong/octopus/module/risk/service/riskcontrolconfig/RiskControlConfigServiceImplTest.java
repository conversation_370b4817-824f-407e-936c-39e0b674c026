package com.yitong.octopus.module.opensdk.service.riskcontrolconfig;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import com.yitong.octopus.framework.test.core.ut.BaseDbUnitTest;

import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;
import com.yitong.octopus.module.opensdk.dal.mysql.riskcontrolconfig.RiskControlConfigMapper;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.annotation.Resource;
import org.springframework.context.annotation.Import;
import java.util.*;
import java.time.LocalDateTime;

import static cn.hutool.core.util.RandomUtil.*;
import static com.yitong.octopus.module.opensdk.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.framework.test.core.util.AssertUtils.*;
import static com.yitong.octopus.framework.test.core.util.RandomUtils.*;
import static com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils.*;
import static com.yitong.octopus.framework.common.util.object.ObjectUtils.*;
import static com.yitong.octopus.framework.common.util.date.DateUtils.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
* {@link RiskControlConfigServiceImpl} 的单元测试类
*
* <AUTHOR>
*/
@Import(RiskControlConfigServiceImpl.class)
public class RiskControlConfigServiceImplTest extends BaseDbUnitTest {

    @Resource
    private RiskControlConfigServiceImpl controlConfigService;

    @Resource
    private RiskControlConfigMapper controlConfigMapper;

    @Test
    public void testCreateControlConfig_success() {
        // 准备参数
        RiskControlConfigCreateReqVO reqVO = randomPojo(RiskControlConfigCreateReqVO.class);

        // 调用
        Long controlConfigId = controlConfigService.createControlConfig(reqVO);
        // 断言
        assertNotNull(controlConfigId);
        // 校验记录的属性是否正确
        RiskControlConfigDO controlConfig = controlConfigMapper.selectById(controlConfigId);
        assertPojoEquals(reqVO, controlConfig);
    }

    @Test
    public void testUpdateControlConfig_success() {
        // mock 数据
        RiskControlConfigDO dbControlConfig = randomPojo(RiskControlConfigDO.class);
        controlConfigMapper.insert(dbControlConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        RiskControlConfigUpdateReqVO reqVO = randomPojo(RiskControlConfigUpdateReqVO.class, o -> {
            o.setId(dbControlConfig.getId()); // 设置更新的 ID
        });

        // 调用
        controlConfigService.updateControlConfig(reqVO);
        // 校验是否更新正确
        RiskControlConfigDO controlConfig = controlConfigMapper.selectById(reqVO.getId()); // 获取最新的
        assertPojoEquals(reqVO, controlConfig);
    }

    @Test
    public void testUpdateControlConfig_notExists() {
        // 准备参数
        RiskControlConfigUpdateReqVO reqVO = randomPojo(RiskControlConfigUpdateReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> controlConfigService.updateControlConfig(reqVO), CONTROL_CONFIG_NOT_EXISTS);
    }

    @Test
    public void testDeleteControlConfig_success() {
        // mock 数据
        RiskControlConfigDO dbControlConfig = randomPojo(RiskControlConfigDO.class);
        controlConfigMapper.insert(dbControlConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbControlConfig.getId();

        // 调用
        controlConfigService.deleteControlConfig(id);
       // 校验数据不存在了
       assertNull(controlConfigMapper.selectById(id));
    }

    @Test
    public void testDeleteControlConfig_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> controlConfigService.deleteControlConfig(id), CONTROL_CONFIG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetControlConfigPage() {
       // mock 数据
       RiskControlConfigDO dbControlConfig = randomPojo(RiskControlConfigDO.class, o -> { // 等会查询到
           o.setConfigName(null);
           o.setCategory(null);
           o.setType(null);
           o.setSpId(null);
           o.setStoreId(null);
           o.setChannelId(null);
           o.setStatus(null);
           o.setCreateTime(null);
       });
       controlConfigMapper.insert(dbControlConfig);
       // 测试 configName 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setConfigName(null)));
       // 测试 category 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setCategory(null)));
       // 测试 type 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setType(null)));
       // 测试 spId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setSpId(null)));
       // 测试 storeId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setStoreId(null)));
       // 测试 channelId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setChannelId(null)));
       // 测试 status 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setStatus(null)));
       // 测试 createTime 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setCreateTime(null)));
       // 准备参数
       RiskControlConfigPageReqVO reqVO = new RiskControlConfigPageReqVO();
       reqVO.setConfigName(null);
       reqVO.setCategory(null);
       reqVO.setType(null);
       reqVO.setSpId(null);
       reqVO.setStoreId(null);
       reqVO.setChannelId(null);
       reqVO.setStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       PageResult<RiskControlConfigDO> pageResult = controlConfigService.getControlConfigPage(reqVO);
       // 断言
       assertEquals(1, pageResult.getTotal());
       assertEquals(1, pageResult.getList().size());
       assertPojoEquals(dbControlConfig, pageResult.getList().get(0));
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetControlConfigList() {
       // mock 数据
       RiskControlConfigDO dbControlConfig = randomPojo(RiskControlConfigDO.class, o -> { // 等会查询到
           o.setConfigName(null);
           o.setCategory(null);
           o.setType(null);
           o.setSpId(null);
           o.setStoreId(null);
           o.setChannelId(null);
           o.setStatus(null);
           o.setCreateTime(null);
       });
       controlConfigMapper.insert(dbControlConfig);
       // 测试 configName 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setConfigName(null)));
       // 测试 category 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setCategory(null)));
       // 测试 type 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setType(null)));
       // 测试 spId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setSpId(null)));
       // 测试 storeId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setStoreId(null)));
       // 测试 channelId 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setChannelId(null)));
       // 测试 status 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setStatus(null)));
       // 测试 createTime 不匹配
       controlConfigMapper.insert(cloneIgnoreId(dbControlConfig, o -> o.setCreateTime(null)));
       // 准备参数
       RiskControlConfigExportReqVO reqVO = new RiskControlConfigExportReqVO();
       reqVO.setConfigName(null);
       reqVO.setCategory(null);
       reqVO.setType(null);
       reqVO.setSpId(null);
       reqVO.setStoreId(null);
       reqVO.setChannelId(null);
       reqVO.setStatus(null);
       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

       // 调用
       List<RiskControlConfigDO> list = controlConfigService.getControlConfigList(reqVO);
       // 断言
       assertEquals(1, list.size());
       assertPojoEquals(dbControlConfig, list.get(0));
    }

}
