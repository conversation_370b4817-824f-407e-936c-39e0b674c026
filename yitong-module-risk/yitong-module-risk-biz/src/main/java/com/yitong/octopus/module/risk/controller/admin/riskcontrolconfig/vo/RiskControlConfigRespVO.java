package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 系统风控配置 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskControlConfigRespVO extends RiskControlConfigBaseVO {

    @Schema(description = "ID", required = true, example = "26720")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
