package com.yitong.octopus.module.opensdk.convert.riskcontrolconfig;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;

/**
 * 系统风控配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RiskControlConfigConvert {

    RiskControlConfigConvert INSTANCE = Mappers.getMapper(RiskControlConfigConvert.class);

    RiskControlConfigDO convert(RiskControlConfigCreateReqVO bean);

    RiskControlConfigDO convert(RiskControlConfigUpdateReqVO bean);

    RiskControlConfigRespVO convert(RiskControlConfigDO bean);

    List<RiskControlConfigRespVO> convertList(List<RiskControlConfigDO> list);

    PageResult<RiskControlConfigRespVO> convertPage(PageResult<RiskControlConfigDO> page);

    List<RiskControlConfigExcelVO> convertList02(List<RiskControlConfigDO> list);

}
