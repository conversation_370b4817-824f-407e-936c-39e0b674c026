package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 系统风控配置 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class RiskControlConfigBaseVO {

    @Schema(description = "风控名称", example = "王五")
    private String configName;

    @Schema(description = "分类")
    private Integer category;

    @Schema(description = "类型", example = "1")
    private Integer type;

    @Schema(description = "商家ID", example = "31470")
    private Long spId;

    @Schema(description = "门店ID", example = "2711")
    private Long storeId;

    @Schema(description = "平台渠道", example = "29384")
    private Long channelId;

    @Schema(description = "状态", example = "2")
    private Boolean status;

}
