package com.yitong.octopus.module.opensdk.service.riskcontrolconfig;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 系统风控配置 Service 接口
 *
 * <AUTHOR>
 */
public interface RiskControlConfigService {

    /**
     * 创建系统风控配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createControlConfig(@Valid RiskControlConfigCreateReqVO createReqVO);

    /**
     * 更新系统风控配置
     *
     * @param updateReqVO 更新信息
     */
    void updateControlConfig(@Valid RiskControlConfigUpdateReqVO updateReqVO);

    /**
     * 删除系统风控配置
     *
     * @param id 编号
     */
    void deleteControlConfig(Long id);

    /**
     * 获得系统风控配置
     *
     * @param id 编号
     * @return 系统风控配置
     */
    RiskControlConfigDO getControlConfig(Long id);

    /**
     * 获得系统风控配置列表
     *
     * @param ids 编号
     * @return 系统风控配置列表
     */
    List<RiskControlConfigDO> getControlConfigList(Collection<Long> ids);

    /**
     * 获得系统风控配置分页
     *
     * @param pageReqVO 分页查询
     * @return 系统风控配置分页
     */
    PageResult<RiskControlConfigDO> getControlConfigPage(RiskControlConfigPageReqVO pageReqVO);

    /**
     * 获得系统风控配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 系统风控配置列表
     */
    List<RiskControlConfigDO> getControlConfigList(RiskControlConfigExportReqVO exportReqVO);

}
