package com.yitong.octopus.module.opensdk.dal.mysql.riskcontrolconfig;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;

/**
 * 系统风控配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RiskControlConfigMapper extends BaseMapperX<RiskControlConfigDO> {

    default PageResult<RiskControlConfigDO> selectPage(RiskControlConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RiskControlConfigDO>()
                .likeIfPresent(RiskControlConfigDO::getConfigName, reqVO.getConfigName())
                .eqIfPresent(RiskControlConfigDO::getCategory, reqVO.getCategory())
                .eqIfPresent(RiskControlConfigDO::getType, reqVO.getType())
                .eqIfPresent(RiskControlConfigDO::getSpId, reqVO.getSpId())
                .eqIfPresent(RiskControlConfigDO::getStoreId, reqVO.getStoreId())
                .eqIfPresent(RiskControlConfigDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(RiskControlConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(RiskControlConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RiskControlConfigDO::getId));
    }

    default List<RiskControlConfigDO> selectList(RiskControlConfigExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RiskControlConfigDO>()
                .likeIfPresent(RiskControlConfigDO::getConfigName, reqVO.getConfigName())
                .eqIfPresent(RiskControlConfigDO::getCategory, reqVO.getCategory())
                .eqIfPresent(RiskControlConfigDO::getType, reqVO.getType())
                .eqIfPresent(RiskControlConfigDO::getSpId, reqVO.getSpId())
                .eqIfPresent(RiskControlConfigDO::getStoreId, reqVO.getStoreId())
                .eqIfPresent(RiskControlConfigDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(RiskControlConfigDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(RiskControlConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(RiskControlConfigDO::getId));
    }

}
