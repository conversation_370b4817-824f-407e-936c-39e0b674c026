package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 系统风控配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskControlConfigPageReqVO extends PageParam {

    @Schema(description = "风控名称", example = "王五")
    private String configName;

    @Schema(description = "分类")
    private Integer category;

    @Schema(description = "类型", example = "1")
    private Integer type;

    @Schema(description = "商家ID", example = "31470")
    private Long spId;

    @Schema(description = "门店ID", example = "2711")
    private Long storeId;

    @Schema(description = "平台渠道", example = "29384")
    private Long channelId;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
