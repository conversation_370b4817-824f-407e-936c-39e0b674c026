package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 系统风控配置 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class RiskControlConfigExcelVO {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("风控名称")
    private String configName;

    @ExcelProperty(value = "分类", converter = DictConvert.class)
    @DictFormat("sp_risk_category") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer category;

    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat("sp_risk_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer type;

    @ExcelProperty("商家ID")
    private Long spId;

    @ExcelProperty("门店ID")
    private Long storeId;

    @ExcelProperty("平台渠道")
    private Long channelId;

    @ExcelProperty("状态")
    private Boolean status;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
