package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;
import com.yitong.octopus.module.opensdk.convert.riskcontrolconfig.RiskControlConfigConvert;
import com.yitong.octopus.module.opensdk.service.riskcontrolconfig.RiskControlConfigService;

@Tag(name = "管理后台 - 系统风控配置")
@RestController
@RequestMapping("/risk/control-config")
@Validated
public class RiskControlConfigController {

    @Resource
    private RiskControlConfigService controlConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建系统风控配置")
    @PreAuthorize("@ss.hasPermission('risk:control-config:create')")
    public CommonResult<Long> createControlConfig(@Valid @RequestBody RiskControlConfigCreateReqVO createReqVO) {
        return success(controlConfigService.createControlConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新系统风控配置")
    @PreAuthorize("@ss.hasPermission('risk:control-config:update')")
    public CommonResult<Boolean> updateControlConfig(@Valid @RequestBody RiskControlConfigUpdateReqVO updateReqVO) {
        controlConfigService.updateControlConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除系统风控配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('risk:control-config:delete')")
    public CommonResult<Boolean> deleteControlConfig(@RequestParam("id") Long id) {
        controlConfigService.deleteControlConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得系统风控配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('risk:control-config:query')")
    public CommonResult<RiskControlConfigRespVO> getControlConfig(@RequestParam("id") Long id) {
        RiskControlConfigDO controlConfig = controlConfigService.getControlConfig(id);
        return success(RiskControlConfigConvert.INSTANCE.convert(controlConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得系统风控配置列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('risk:control-config:query')")
    public CommonResult<List<RiskControlConfigRespVO>> getControlConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<RiskControlConfigDO> list = controlConfigService.getControlConfigList(ids);
        return success(RiskControlConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得系统风控配置分页")
    @PreAuthorize("@ss.hasPermission('risk:control-config:query')")
    public CommonResult<PageResult<RiskControlConfigRespVO>> getControlConfigPage(@Valid RiskControlConfigPageReqVO pageVO) {
        PageResult<RiskControlConfigDO> pageResult = controlConfigService.getControlConfigPage(pageVO);
        return success(RiskControlConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出系统风控配置 Excel")
    @PreAuthorize("@ss.hasPermission('risk:control-config:export')")
    @OperateLog(type = EXPORT)
    public void exportControlConfigExcel(@Valid RiskControlConfigExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<RiskControlConfigDO> list = controlConfigService.getControlConfigList(exportReqVO);
        // 导出 Excel
        List<RiskControlConfigExcelVO> datas = RiskControlConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "系统风控配置.xls", "数据", RiskControlConfigExcelVO.class, datas);
    }

}
