package com.yitong.octopus.module.opensdk.service.riskcontrolconfig;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo.*;
import com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig.RiskControlConfigDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.opensdk.convert.riskcontrolconfig.RiskControlConfigConvert;
import com.yitong.octopus.module.opensdk.dal.mysql.riskcontrolconfig.RiskControlConfigMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.opensdk.enums.ErrorCodeConstants.*;

/**
 * 系统风控配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RiskControlConfigServiceImpl implements RiskControlConfigService {

    @Resource
    private RiskControlConfigMapper controlConfigMapper;

    @Override
    public Long createControlConfig(RiskControlConfigCreateReqVO createReqVO) {
        // 插入
        RiskControlConfigDO controlConfig = RiskControlConfigConvert.INSTANCE.convert(createReqVO);
        controlConfigMapper.insert(controlConfig);
        // 返回
        return controlConfig.getId();
    }

    @Override
    public void updateControlConfig(RiskControlConfigUpdateReqVO updateReqVO) {
        // 校验存在
        validateControlConfigExists(updateReqVO.getId());
        // 更新
        RiskControlConfigDO updateObj = RiskControlConfigConvert.INSTANCE.convert(updateReqVO);
        controlConfigMapper.updateById(updateObj);
    }

    @Override
    public void deleteControlConfig(Long id) {
        // 校验存在
        validateControlConfigExists(id);
        // 删除
        controlConfigMapper.deleteById(id);
    }

    private void validateControlConfigExists(Long id) {
        if (controlConfigMapper.selectById(id) == null) {
            throw exception(CONTROL_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    public RiskControlConfigDO getControlConfig(Long id) {
        return controlConfigMapper.selectById(id);
    }

    @Override
    public List<RiskControlConfigDO> getControlConfigList(Collection<Long> ids) {
        return controlConfigMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<RiskControlConfigDO> getControlConfigPage(RiskControlConfigPageReqVO pageReqVO) {
        return controlConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<RiskControlConfigDO> getControlConfigList(RiskControlConfigExportReqVO exportReqVO) {
        return controlConfigMapper.selectList(exportReqVO);
    }

}
