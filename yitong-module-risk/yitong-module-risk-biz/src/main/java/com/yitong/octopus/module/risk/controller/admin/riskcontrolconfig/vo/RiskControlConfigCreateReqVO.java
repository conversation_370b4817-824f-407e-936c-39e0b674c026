package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 系统风控配置创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskControlConfigCreateReqVO extends RiskControlConfigBaseVO {

    @Schema(description = "配置信息值JSON")
    private String configValue;

}
