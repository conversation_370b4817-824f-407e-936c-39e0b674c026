package com.yitong.octopus.module.opensdk.dal.dataobject.riskcontrolconfig;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 系统风控配置 DO
 *
 * <AUTHOR>
 */
@TableName("sp_risk_control_config")
@KeySequence("sp_risk_control_config_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RiskControlConfigDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 风控名称
     */
    private String configName;
    /**
     * 分类
     *
     * 枚举 {@link TODO sp_risk_category 对应的类}
     */
    private Integer category;
    /**
     * 类型
     *
     * 枚举 {@link TODO sp_risk_type 对应的类}
     */
    private Integer type;
    /**
     * 商家ID
     */
    private Long spId;
    /**
     * 门店ID
     */
    private Long storeId;
    /**
     * 平台渠道
     */
    private Long channelId;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 配置信息值JSON
     */
    private String configValue;

}
