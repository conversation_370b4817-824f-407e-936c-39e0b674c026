package com.yitong.octopus.module.opensdk.controller.admin.riskcontrolconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 系统风控配置更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RiskControlConfigUpdateReqVO extends RiskControlConfigBaseVO {

    @Schema(description = "ID", required = true, example = "26720")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "配置信息值JSON")
    private String configValue;

}
