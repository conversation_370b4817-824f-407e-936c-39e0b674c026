//package com.yitong.octopus.module.system.controller.admin.errorcode.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.ToString;
//
//@Schema(description = "管理后台 - 错误码创建 Request VO")
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
//public class ErrorCodeCreateReqVO extends ErrorCodeBaseVO {
//
//}
