//package com.yitong.octopus.module.opensdk.xhsminiapp.client;
//
//import com.yitong.octopus.framework.common.exception.ServerException;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.session.request.SessionCodeRequest;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.session.response.SessionCodeResponse;
//import org.junit.jupiter.api.Test;
//
//
//public class SessionClientTest {
//
//    private SessionClient client = new SessionClient();
//
//    @Test
//    public void session() {
//        try {
//            SessionCodeRequest request = new SessionCodeRequest("11111");
//            SessionCodeResponse response = client.session(request);
//            System.out.println(response.getOpenid());
//        }catch (ServerException e){
//            System.out.println(e.getMessage());
//        }
//    }
//}
