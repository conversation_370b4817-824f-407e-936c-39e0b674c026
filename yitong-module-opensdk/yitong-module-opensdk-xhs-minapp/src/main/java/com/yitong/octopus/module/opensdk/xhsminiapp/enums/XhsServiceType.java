package com.yitong.octopus.module.opensdk.xhsminiapp.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 服务类别，枚举值如下：
 * 1:订座
 * 2:排队
 * 3:点单
 * 4:团购
 * 5:组局
 * 6:会员
 * 7:领券
 * 8:门店导览
 * 9:商场停车
 * 10:外卖配送
 * 100:其他服务
 */
@Getter
@AllArgsConstructor
public enum XhsServiceType implements EnumKeyArrayValuable {

    RESERVATION(1, "订座"),
    LINE_UP(2, "排队"),
    ORDER(3, "点单"),
    GROUP_BUYING(4, "团购"),
    ORGANIZING_THE_BUREAU(5, "组局"),
    MEMBERSHIP(6, "会员"),
    COUPON_COLLECTION(7, "领券"),
    STORE_TOUR(8, "门店导览"),
    SHOPPING_MALL_PARKING(9, "商场停车"),
    TAKEOUT_DELIVERY(10, "外卖配送"),
    OTHER_SERVICES(100, "其他服务"),
    ;

    public static final Object[] ARRAYS = Arrays.stream(values()).map(XhsServiceType::getType).toArray();

    private final Integer type;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
