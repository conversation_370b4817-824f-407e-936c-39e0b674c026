package com.yitong.octopus.module.opensdk.xhsminiapp.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 售后类型：1：退款，2：退款退货
 *
 */
@Getter
@AllArgsConstructor
public enum XhsAfterSaleType implements EnumKeyArrayValuable {
    WAIREFUND_ONLYTE(1, "退款"),
    REFUND_AND_RETURN(2, "退款退货"),
    ;

    public static final Object[] ARRAYS = Arrays.stream(values()).map(XhsAfterSaleType::getType).toArray();

    private final Integer type;
    private final String name;

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
