package com.yitong.octopus.module.opensdk.xhsfls.client;

import cn.hutool.core.map.MapUtil;
import com.yitong.octopus.framework.common.util.json.JsonUtils;
import com.yitong.octopus.module.opensdk.xhsfls.entity.BaseResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.AddDeclarePortRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.AuditCancelApplyRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.CreateTransferBatchRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetCancelApplyListRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetItemCustomInfoRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetPackageDeclareRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetPackageDetailRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetPackageListRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetPackageTrackRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetReceiverInfoRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.GetSupportedPortListRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.ModifyPackageExpressRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.ModifySellerMarkRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.PackageDeliverRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.ResendBondedPaymentRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.SyncItemCustomsRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.request.UpdateProxyPackageWeightRequest;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.CreateTransferBatchResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetCancelApplyListResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetItemCustomsInfoResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetPackageDeclareResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetPackageDetailResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetPackageTrackResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetPackagesListResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetReceiveInfoResponse;
import com.yitong.octopus.module.opensdk.xhsfls.entity.packages.response.GetSupportedPortListResponse;
import com.yitong.octopus.module.opensdk.xhsfls.util.Utils;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class PackageClient extends BaseClient {
    public PackageClient(String url, String appId, String version, String appSecret) {
        super(url, appId, version, appSecret);
    }

    public BaseResponse<GetPackagesListResponse> execute(GetPackageListRequest request, String accessToken) throws IOException {
        BaseResponse<GetPackagesListResponse> response = new BaseResponse();
        request.setMethod("package.getPackageList");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetPackagesListResponse getPackagesListResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetPackagesListResponse.class);
            response.setSuccessResponse(getPackagesListResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetPackageDetailResponse> execute(GetPackageDetailRequest request, String accessToken) throws IOException {
        BaseResponse<GetPackageDetailResponse> response = new BaseResponse();
        request.setMethod("package.getPackageDetail");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetPackageDetailResponse getPackageDetailResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetPackageDetailResponse.class);
            response.setSuccessResponse(getPackageDetailResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(ResendBondedPaymentRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.resendBondedPaymentRecord");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(SyncItemCustomsRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.syncItemCustomsInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetItemCustomsInfoResponse> execute(GetItemCustomInfoRequest request, String accessToken) throws IOException {
        BaseResponse<GetItemCustomsInfoResponse> response = new BaseResponse();
        request.setMethod("package.getItemCustomsInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetItemCustomsInfoResponse getItemCustomsInfoResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetItemCustomsInfoResponse.class);
            response.setSuccessResponse(getItemCustomsInfoResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetReceiveInfoResponse> execute(GetReceiverInfoRequest request, String accessToken) throws IOException {
        BaseResponse<GetReceiveInfoResponse> response = new BaseResponse();
        request.setMethod("package.getPackageReceiverInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetReceiveInfoResponse getReceiveInfoResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetReceiveInfoResponse.class);
            response.setSuccessResponse(getReceiveInfoResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(ModifyPackageExpressRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.modifyPackageExpressInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(PackageDeliverRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.packageDeliver");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(ModifySellerMarkRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.modifySellerMarkInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetPackageTrackResponse> execute(GetPackageTrackRequest request, String accessToken) throws IOException {
        BaseResponse<GetPackageTrackResponse> response = new BaseResponse();
        request.setMethod("package.getPackageTracking");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetPackageTrackResponse getPackageTrackResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetPackageTrackResponse.class);
            response.setSuccessResponse(getPackageTrackResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetPackageDeclareResponse> execute(GetPackageDeclareRequest request, String accessToken) throws IOException {
        BaseResponse<GetPackageDeclareResponse> response = new BaseResponse();
        request.setMethod("package.getPackageDeclareInfo");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetPackageDeclareResponse getPackageDeclareResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetPackageDeclareResponse.class);
            response.setSuccessResponse(getPackageDeclareResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetSupportedPortListResponse> execute(GetSupportedPortListRequest request, String accessToken) throws IOException {
        BaseResponse<GetSupportedPortListResponse> response = new BaseResponse();
        request.setMethod("package.getSupportedPortList");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetSupportedPortListResponse getSupportedPortListResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetSupportedPortListResponse.class);
            response.setSuccessResponse(getSupportedPortListResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<GetCancelApplyListResponse> execute(GetCancelApplyListRequest request, String accessToken) throws IOException {
        BaseResponse<GetCancelApplyListResponse> response = new BaseResponse();
        request.setMethod("package.getCancelApplyList");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            GetCancelApplyListResponse getCancelApplyListResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), GetCancelApplyListResponse.class);
            response.setSuccessResponse(getCancelApplyListResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(AuditCancelApplyRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.auditCancelApply");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(AddDeclarePortRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.addDeclarePort");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<String> execute(UpdateProxyPackageWeightRequest request, String accessToken) throws IOException {
        BaseResponse<String> response = new BaseResponse();
        request.setMethod("package.updateProxyPackageWeight");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            response.setSuccessResponse("");
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }

    public BaseResponse<CreateTransferBatchResponse> execute(CreateTransferBatchRequest request, String accessToken) throws IOException {
        BaseResponse<CreateTransferBatchResponse> response = new BaseResponse();
        request.setMethod("package.createTransferBatch");
        request.addParameter(this, accessToken);
        String bodyString = JsonUtils.toJsonString(request);
        RequestBody body = RequestBody.create(JSONTYPE, bodyString);
        Request httpRequest = (new Request.Builder()).url(this.url).post(body).build();
        Response openapiResponse = Utils.okHttpClient.newCall(httpRequest).execute();
        String responseBody = openapiResponse.body().string();
        Map bodyMap = JsonUtils.parseObject(responseBody, Map.class);
        if (MapUtil.getBool(bodyMap, "success").booleanValue()) {
            CreateTransferBatchResponse createTransferBatchResponse = JsonUtils.parseObject(JsonUtils.toJsonString(bodyMap.get("data")), CreateTransferBatchResponse.class);
            response.setSuccessResponse(createTransferBatchResponse);
        } else {
            response.setFailResponse(Objects.toString(bodyMap.getOrDefault("error_msg", bodyMap.get("msg")), ""), Objects.toString(bodyMap.getOrDefault("error_code", bodyMap.get("code")), ""));
        }
        return response;
    }
}
