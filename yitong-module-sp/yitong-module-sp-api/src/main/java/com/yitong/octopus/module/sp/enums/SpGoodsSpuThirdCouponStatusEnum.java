package com.yitong.octopus.module.sp.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 *  商品状态: 状态： 1 有效 2 已使用 3 已过期 4 已作废
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SpGoodsSpuThirdCouponStatusEnum implements EnumKeyArrayValuable {

    TO_BE_ACTIVATE(0, "待激活"),
    WAITE_USE(1, "待使用"),
    USED(2, "已使用"),
    EXPIRED(3, "已过期"),
    CANCELED(4, "已作废"),
    ;

    private final Integer status;
    private final String name;

    public static final Object[] ARRAYS = Arrays.stream(values()).map(SpGoodsSpuThirdCouponStatusEnum::getStatus).toArray();

    @Override
    public Object[] array() {
        return ARRAYS;
    }

}
