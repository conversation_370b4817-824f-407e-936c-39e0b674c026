package com.yitong.octopus.module.sp.api.utils.jackson;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.yitong.octopus.framework.common.util.spring.SpringUtils;
import com.yitong.octopus.module.sp.api.sp.SpMainInfoApi;
import com.yitong.octopus.module.sp.api.sp.vo.SpMainInfoVo;
import com.yitong.octopus.module.sp.api.spu.SpGoodsSpuApi;
import com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuRespDto;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

@Slf4j
public class SpGoodsSpuListJsonSerializer extends JsonSerializer<List<Long>> {

    @Override
    public void serialize(List<Long> spuIds, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (CollectionUtil.isNotEmpty(spuIds)) {
            jsonGenerator.writeStartArray();
            for (Long id:spuIds){
                jsonGenerator.writeString(String.valueOf(id));
            }
            jsonGenerator.writeEndArray();

            try{
                List<SpGoodsSpuRespDto> list = SpringUtils.getBean(SpGoodsSpuApi.class).getSpuListByIds(spuIds);
                if (CollectionUtil.isNotEmpty(list)) {
                    jsonGenerator.writeFieldName("spuList");
                    jsonGenerator.writeStartArray();
                    for (SpGoodsSpuRespDto spu :list){
                        jsonGenerator.writeObject(spu);
                    }
                    jsonGenerator.writeEndArray();
                }
            }catch (Exception e){
                log.info("spuId json serializer error:{}",spuIds,e);
            }

        }else {
            jsonGenerator.writeNull();
        }
    }
}
