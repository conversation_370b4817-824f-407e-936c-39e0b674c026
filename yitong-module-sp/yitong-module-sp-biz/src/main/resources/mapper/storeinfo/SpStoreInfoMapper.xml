<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.storeinfo.SpStoreInfoMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getStoreInfoList"
            resultType="com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoViewRespVO">
        SELECT
            ssi.*
        FROM sp_store_info ssi
        JOIN sp_main_info  smi ON  smi.id = ssi.sp_id
        WHERE ssi.deleted = 0
        <if test="vo.spId != null">
            AND ssi.sp_id =#{vo.spId}
        </if>

        <if test="vo.id != null">
            AND ssi.id = #{vo.id}
        </if>
        <if test="vo.categoryId != null">
            AND ssi.category_id = #{vo.categoryId}
        </if>
        <if test="vo.storeName != null and vo.storeName != ''">
            AND ssi.store_name  like concat('%', #{vo.storeName}, '%')
        </if>
        <if test="vo.spProvinceId != null">
            AND ssi.sp_province_id = #{vo.spProvinceId}
        </if>
        <if test="vo.spCityId != null">
            AND ssi.sp_city_id = #{vo.spCityId}
        </if>
        <if test="vo.spCountyId != null">
            AND ssi.sp_county_id = #{vo.spCountyId}
        </if>
        <if test="vo.spStatus != null">
            AND ssi.sp_status IN
            <foreach collection="vo.spStatus" item="spStatus" open="(" close=")" separator=",">
                #{spStatus}
            </foreach>
        </if>
        <if test="vo.linkName != null and vo.linkName != ''">
            AND ssi.link_name like concat('%', #{vo.linkName}, '%')
        </if>
        <if test="vo.linkPhone != null and vo.linkPhone != ''">
            AND ssi.link_phone like concat('%', #{vo.linkPhone}, '%')
        </if>
        <if test="vo.createTime != null and vo.createTime.length >0">
            AND ssi.create_time between #{vo.createTime[0]} and #{vo.createTime[1]}
        </if>
        <if test="vo.claimStatus != null">
            AND ssi.claim_status = #{vo.claimStatus}
        </if>
        <if test="vo.mainAuditStatus != null">
            AND ssi.main_audit_status = #{vo.mainAuditStatus}
        </if>
        <if test="vo.industryAuditStatus != null">
            AND ssi.industry_audit_status = #{vo.industryAuditStatus}
        </if>
        <if test="vo.poiAuditStatus != null">
            AND ssi.poi_audit_status = #{vo.poiAuditStatus}
        </if>

        ORDER BY ssi.id DESC
    </select>

    <select id="selectStoreInfoBySpuId"
            resultType="com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO">
        SELECT
            ssi.*
        FROM sp_store_info ssi
        JOIN sp_goods_spu_store sgss ON sgss.store_id = ssi.id
        WHERE  sgss.deleted = 0 AND sgss.spu_id = #{spuId} AND ssi.sp_status = 2
    </select>

    <select id="getStoreInfoMapList"
            resultType="com.yitong.octopus.module.sp.dal.dto.SpStoreInfoDto">
        SELECT
            ssi.*
             ,ssim.map_id
             ,ssim.map_poi
             ,ssim.map_poi_url
             ,ssim.latitude
             ,ssim.longitude
        FROM sp_store_info ssi
        JOIN  sp_main_info smi ON smi.id = ssi.sp_id
        LEFT JOIN sp_store_info_map ssim ON ssim.store_id = ssi.id
        WHERE  ssi.deleted = 0
            <if test="vo.spId !=null">
                AND ssi.sp_id = #{vo.spId}
            </if>
            <if test="vo.id != null">
                AND ssi.id = #{vo.id}
            </if>
            <if test="vo.categoryId != null">
                AND ssi.category_id = #{vo.categoryId}
            </if>
            <if test="vo.storeName != null and vo.storeName != ''">
                AND ssi.store_name  like concat('%', #{vo.storeName}, '%')
            </if>
            <if test="vo.spProvinceId != null">
                AND ssi.sp_province_id = #{vo.spProvinceId}
            </if>
            <if test="vo.spCityId != null">
                AND ssi.sp_city_id = #{vo.spCityId}
            </if>
            <if test="vo.spCountyId != null">
                AND ssi.sp_county_id = #{vo.spCountyId}
            </if>
            <if test="vo.spStatus != null">
                AND ssi.sp_status = #{vo.spStatus}
            </if>
            <if test="vo.linkName != null and vo.linkName != ''">
                AND ssi.link_name like concat('%', #{vo.linkName}, '%')
            </if>
            <if test="vo.linkPhone != null and vo.linkPhone != ''">
                AND ssi.link_phone like concat('%', #{vo.linkPhone}, '%')
            </if>
            <if test="vo.claimStatus != null">
                AND ssi.claim_status = #{vo.claimStatus}
            </if>
            <if test="vo.mainAuditStatus != null">
                AND ssi.main_audit_status = #{vo.mainAuditStatus}
            </if>
            <if test="vo.industryAuditStatus != null">
                AND ssi.industry_audit_status = #{vo.industryAuditStatus}
            </if>
            <if test="vo.poiAuditStatus != null">
                AND ssi.poi_audit_status = #{vo.poiAuditStatus}
            </if>
            ORDER BY ssi.id DESC
    </select>

    <select id="getStoreInfoListBySpuId"
            resultType="com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO"
            parameterType="java.lang.Long">
        SELECT
            s.*
        FROM sp_store_info s
        JOIN sp_goods_spu_store ss ON ss.store_id = s.id
        WHERE s.deleted =0 AND ss.deleted = 0
          AND s.sp_status = 2
          AND ss.spu_id = #{spuId}
    </select>

    <update id="updateStorePayAccId">
        UPDATE sp_store_info
        SET payee_acct_id = #{payAccId}
        WHERE id = #{storeId}
    </update>

    <update id="deleteStoreGoodsSpu">
        UPDATE sp_goods_spu_store
        SET deleted = 1
        WHERE store_id = #{storeId} AND deleted =0
    </update>

    <select id="getSpStoreInfoBySpuId"
            resultType="com.yitong.octopus.module.sp.dal.dto.spstore.AppXhsMiniAppIndexStoreRespVo">
        SELECT
            ssi.id store_id
             ,ssi.store_name
             ,ssi.store_logo
             ,ssi.sp_add
             ,ssi.sp_city
             ,ssi.store_tel
             ,ssi.store_open_time_desc
             ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
            ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(#{reqVo.longitude},#{reqVo.latitude}))) as distance
        FROM sp_goods_spu_store spus
        JOIN sp_store_info ssi ON ssi.id = spus.store_id AND ssi.deleted =0
        JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
        LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
        WHERE 1=1
          AND ssi.sp_status = 2
          AND  spus.spu_id = #{reqVo.spuId} AND spus.deleted = 0
          AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
        <if test="reqVo.radius !=null and reqVo.radius > 0 ">
            HAVING distance &lt; #{reqVo.radius}
        </if>
        ORDER BY distance
    </select>

    <select id="getSpStoreCityListBySpId" resultType="java.lang.String">
        SELECT
            s.sp_city
        FROM sp_store_info s
        JOIN sp_store_channel_config scc ON s.id = scc.store_id AND scc.deleted =0
        WHERE s.sp_id = #{spId} AND s.deleted =0
        AND s.sp_status = 2
        GROUP BY s.sp_city
    </select>

    <select id="getSpStorePageBySpId" resultType="com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsRespVO">
        SELECT
            a.*
        FROM  (
            SELECT
                DISTINCT
                ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_add
                ,ssi.sp_city
                ,ssi.rating
                ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
                ,ssi.store_open_time_desc
                ,ssi.link_phone
                ,ssi.link_tel
                ,sim.longitude
                ,sim.latitude
                ,sim.map_poi
            FROM sp_store_info ssi
            JOIN sp_store_channel_config scc ON ssi.id =scc.store_id AND scc.deleted =0
            JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
            WHERE ssi.sp_id = #{spId} AND ssi.deleted =0 AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
                AND ssi.sp_status = 2
            <if test="vo.storeName != null and vo.storeName != ''">
                AND ssi.store_name  like CONCAT('%', #{vo.storeName}, '%')
            </if>
            <if test="vo.city != null and vo.city !='' ">
                AND  ssi.sp_city = #{vo.city}
            </if>
            <if test="vo.radius != null ">
                HAVING  distance &lt;= #{vo.radius}
            </if>
            ORDER BY distance
        ) a
    </select>

    <select id="getSpStorePageByStoreIdsAndAppId"  resultType="com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsRespVO">
        SELECT
            DISTINCT
            ssi.id store_id
            ,ssi.store_name
            ,ssi.store_logo
            ,ssi.sp_add
            ,ssi.sp_city
            ,ssi.rating
            ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
            ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${vo.longitude},${vo.latitude}))) as distance
            ,ssi.store_open_time_desc
            ,ssi.link_phone
            ,ssi.link_tel
            ,sim.longitude
            ,sim.latitude
            ,sim.map_poi
        FROM sp_store_info ssi
        JOIN sp_store_channel_config scc ON ssi.id =scc.store_id AND scc.deleted =0
        JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
        LEFT JOIN sp_store_info_gallery ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
        WHERE  ssi.deleted =0 AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
        AND ssi.sp_status = 2
        AND ssi.id IN
        <foreach collection="vo.storeIds" item="storeId" separator="," open="(" close=")">
            #{storeId}
        </foreach>
        <if test="vo.storeName != null and vo.storeName != ''">
            AND ssi.store_name  like CONCAT('%', #{vo.storeName}, '%')
        </if>
        <if test="vo.city != null and vo.city !='' ">
            AND  ssi.sp_city = #{vo.city}
        </if>
        <if test="vo.radius != null ">
            HAVING  distance &lt;= #{vo.radius}
        </if>
        ORDER BY distance
    </select>

</mapper>
