<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.mainmaterials.SpMainInfoMaterialsMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectByPage"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo.SpMainInfoMaterialsRespVO">
            SELECT
                m2.id
                 ,m2.sp_id
                 ,m.sp_name
                 ,m2.store_id
                 ,s.store_name
                 ,m2.spu_id
                 ,spu.full_name spu_name
                 ,m2.sku_id
                 ,sku.sale_amount
                 ,m2.type
                 ,m2.size
                 ,m2.`status`
                 ,m2.audit_msg
                 ,m2.audit_user
                 ,m2.file_name
                 ,m2.file_path
                 ,m2.file_url
                 ,m2.file_type
                 ,m2.creator
                 ,m2.create_time
                 ,m2.updater
                 ,m2.update_time
                 ,m2.deleted
                 ,m2.tenant_id
        FROM sp_main_materials m2
         JOIN sp_main_info m ON m2.sp_id = m.id
         LEFT JOIN sp_store_info s ON s.id = m2.store_id
         LEFT JOIN sp_goods_spu spu ON spu.id = m2.spu_id
         LEFT JOIN sp_goods_sku sku ON sku.id = m2.sku_id
        WHERE m2.deleted = 0
        <if test="vo.spId !=null ">
          AND m2.sp_id =  #{vo.spId}
        </if>
        <if test="vo.spSearch !=null and vo.spSearch !='' ">
            AND (m.id =  #{vo.spSearch} OR m.sp_name like concat('%', #{vo.spSearch}, '%'))
        </if>
        <if test="vo.storeId !=null ">
            AND m2.store_id =  #{vo.storeId}
        </if>
        <if test="vo.storeSearch !=null and vo.storeSearch !='' ">
            AND (s.id =  #{vo.storeSearch} OR s.store_name like concat('%', #{vo.storeSearch}, '%'))
        </if>
        <if test="vo.type !=null and vo.type !='' ">
            AND m2.type =  #{vo.type}
        </if>
        <if test="vo.fileType !=null and vo.fileType !='' ">
            AND m2.file_type =  #{vo.fileType}
        </if>
        <if test="vo.status !=null ">
            AND m2.`status` =  #{vo.status}
        </if>
        <if test="vo.createTime != null and vo.createTime.length >0 ">
            AND m2.create_time between #{vo.createTime[0]} and #{vo.createTime[1]}
        </if>
        ORDER BY m2.create_time DESC
    </select>

    <select id="selectByList"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo.SpMainInfoMaterialsRespVO"
            parameterType="com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo.SpMainInfoMaterialsExportReqVO">
        SELECT
        m2.id
        ,m2.sp_id
        ,m.sp_name
        ,m2.store_id
        ,s.store_name
        ,m2.spu_id
        ,spu.full_name spu_name
        ,m2.sku_id
        ,sku.sale_amount
        ,m2.type
        ,m2.size
        ,m2.`status`
        ,m2.audit_msg
        ,m2.audit_user
        ,m2.file_name
        ,m2.file_path
        ,m2.file_url
        ,m2.file_type
        ,m2.creator
        ,m2.create_time
        ,m2.updater
        ,m2.update_time
        ,m2.deleted
        ,m2.tenant_id
        FROM sp_main_materials m2
        JOIN sp_main_info m ON m2.sp_id = m.id
        LEFT JOIN sp_store_info s ON s.id = m2.store_id
        LEFT JOIN sp_goods_spu spu ON spu.id = m2.spu_id
        LEFT JOIN sp_goods_sku sku ON sku.id = m2.sku_id
        WHERE m2.deleted = 0
        <if test="vo.spId !=null ">
            AND m2.sp_id =  #{vo.spId}
        </if>
        <if test="vo.storeId !=null ">
            AND m2.store_id =  #{vo.storeId}
        </if>
        <if test="vo.type !=null ">
            AND m2.type =  #{vo.type}
        </if>
        <if test="vo.fileType !=null ">
            AND m2.file_type =  #{vo.fileType}
        </if>
        <if test="vo.status !=null ">
            AND m2.`status` =  #{vo.status}
        </if>
        <if test="vo.createTime != null and vo.createTime.length >0 ">
            AND m2.create_time between #{vo.createTime[0]} and #{vo.createTime[1]}
        </if>
        ORDER BY m2.create_time DESC
    </select>

    <select id="selectListByFileType"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo.SpMainInfoMaterialsRespVO">
        SELECT
        m2.id
        ,m2.sp_id
        ,m.sp_name
        ,m2.store_id
        ,s.store_name
        ,m2.spu_id
        ,spu.full_name spu_name
        ,m2.sku_id
        ,sku.sale_amount
        ,m2.type
        ,m2.size
        ,m2.`status`
        ,m2.audit_msg
        ,m2.audit_user
        ,m2.file_name
        ,m2.file_path
        ,m2.file_url
        ,m2.file_type
        ,m2.cover_url
        ,m2.creator
        ,m2.create_time
        ,m2.updater
        ,m2.update_time
        ,m2.deleted
        ,m2.tenant_id
        FROM sp_main_materials m2
        JOIN sp_main_info m ON m2.sp_id = m.id
        LEFT JOIN sp_store_info s ON s.id = m2.store_id
        LEFT JOIN sp_goods_spu spu ON spu.id = m2.spu_id
        LEFT JOIN sp_goods_sku sku ON sku.id = m2.sku_id
        WHERE m2.deleted = 0  AND m2.`status` = 1
        <if test="spId !=null">
            AND m2.sp_id =  #{spId}
        </if>
        <if test="fileType !=null">
            AND m2.file_type =  #{fileType}
        </if>
        <if test="storeId !=null">
            AND( m2.store_id =  #{storeId} OR m2.store_id IS NULL)
        </if>
        <if test="spuId !=null">
            AND( m2.spu_id =  #{spuId} OR m2.spu_id IS NULL)
        </if>
        <if test="typeList !=null and typeList.size() > 0">
            AND m2.type IN
            <foreach collection="typeList" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="spId !=null and tags!=null and tags.size() > 0">
            AND	(
            m2.id IN(
            SELECT
            DISTINCT
            r.sp_materials_id
            FROM sp_main_materials_tag_rel r
            JOIN yt_platform_tag t ON t.id = r.tag_id AND t.deleted = 0
            WHERE  r.sp_id = #{spId} AND r.tag_id IN
            <foreach collection="tags" item="tag" open="(" separator="," close=")">
                #{tag}
            </foreach>
            )
            OR
            m2.id NOT IN (
            SELECT
            DISTINCT
            r.sp_materials_id
            FROM sp_main_materials_tag_rel r
            JOIN yt_platform_tag t ON t.id = r.tag_id AND t.deleted = 0
            WHERE  r.sp_id = #{spId}
            )
            )
        </if>
        ORDER BY m2.create_time DESC
        <if test="spuId !=null">
            ,m2.spu_id DESC
        </if>
        <if test="storeId !=null">
            ,m2.store_id DESC
        </if>
    </select>


</mapper>
