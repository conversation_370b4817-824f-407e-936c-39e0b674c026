<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpGoodsSpuMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <sql id="selectPage">
        FROM sp_goods_spu sgs
        JOIN sp_main_info  smi ON smi.id = sgs.sp_id AND smi.deleted = 0
        <if test="vo.tabType==4">
            JOIN (
            SELECT
            sku.spu_id
            ,sum( sku.sale_stock )  sale_stock
            FROM sp_goods_sku sku
            WHERE sku.deleted = 0
            GROUP BY sku.spu_id
            ) s ON s.spu_id = sgs.id
        </if>
        <if test="vo.storeId != null">
            JOIN sp_goods_spu_store spu_store ON spu_store.spu_id = sgs.id AND spu_store. deleted = 0
        </if>
        WHERE sgs.deleted = 0
        <if test="vo.spuId != null">
            AND sgs.id = #{vo.spuId}
        </if>
        <if test="vo.spId != null">
            AND sgs.sp_id =#{vo.spId}
        </if>
        <if test="vo.ownerSpId != null">
            AND smi.owner_sp_id =#{vo.ownerSpId}
        </if>
        <if test="vo.storeId != null">
            AND spu_store.store_id = #{vo.storeId}
        </if>
        <if test="vo.categoryId != null">
            AND sgs.category_id = #{vo.categoryId}
        </if>
        <if test="vo.brandId != null">
            AND sgs.brand_id = #{vo.brandId}
        </if>
        <if test="vo.spuName != null and vo.spuName != ''">
            AND sgs.full_name  like concat('%', #{vo.spuName}, '%')
        </if>
        <if test="vo.createTime != null and vo.createTime.length >0 ">
            AND sgs.create_time between #{vo.createTime[0]} and #{vo.createTime[1]}
        </if>
        <if test="vo.soldDate != null and vo.soldDate.length >0 ">
            AND ssi.sold_date_from &gt;= #{vo.soldDate[0]}
            AND ssi.sold_date_to &lt;= #{vo.soldDate[1]}
        </if>
        <if test="vo.type != null">
            AND sgs.type = #{vo.type}
        </if>
        <if test="vo.featuredType != null">
            AND sgs.featured_type = #{vo.featuredType}
        </if>
        <if test="vo.tabType==0">
            AND sgs.status = 0
        </if>
        <if test="vo.tabType==1">
            AND sgs.status = 1
        </if>
        <if test="vo.tabType==2">
            AND sgs.status = 2
        </if>
        <if test="vo.tabType==3">
            AND sgs.status = 3
        </if>
        <if test="vo.tabType==4">
            AND sgs.status = 2 AND s.sale_stock &lt;= 20
        </if>
        <if test="vo.tabType==5">
            AND sgs.status = 2 AND datediff(sgs.sold_date_to,now()) &lt;=10
        </if>
    </sql>


    <select id="selectListPage" resultType="com.yitong.octopus.module.sp.dal.dataobject.goodsspu.SpGoodsSpuDO">
        SELECT
            sgs.*
        <include refid="selectPage"/>
        <if test="vo.searchParams != null and vo.searchParams != '' ">
            AND (sgs.id = #{vo.searchParams} OR sgs.full_name like concat('%', #{vo.searchParams}, '%'))
        </if>
        ORDER BY sgs.id DESC
    </select>

    <select id="selectListCount" resultType="java.lang.Long"
            parameterType="com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuPageReqVO">
        SELECT
            count(1)
        <include refid="selectPage"/>
    </select>

    <select id="getChannelConfigPageBySpId"
            resultType="com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuChannelVo">
        SELECT
            yci.`name` 'channelName'
             ,yci.`code` 'channelCode'
             ,sgsc.id 'spuChannelId'
             ,sgsc.channel_spu_id
             ,sgsc.channel_category_id
             ,sgsc.channel_category_type
             ,sgsc.`status`
             ,sgsc.audit_status
             ,sgsc.audit_result
             ,sgsc.audit_msg
             ,sgsc.on_shelve
        FROM yt_channel_info yci
        LEFT JOIN sp_goods_spu_channel sgsc  ON sgsc.channel_code = yci.`code` AND sgsc.spu_id = #{spGoodsId}
    </select>

    <select id="getSoldOutSpuCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM sp_goods_spu spu
        JOIN sp_goods_sku sku ON spu.id = sku.spu_id AND sku.deleted = 0
        WHERE spu.deleted = 0 AND spu.`status` = 2
        AND sku.sale_stock &lt;=20
    </select>

    <select id="getSoldExpireSpuCount" resultType="java.lang.Long">
        SELECT
        count(1)
        FROM sp_goods_spu spu
        WHERE spu.deleted = 0 AND spu.`status` = 2
        AND datediff(spu.sold_date_to,now()) &lt;=10
    </select>

    <resultMap id="SpGoodsSpuSimpleVOResultMap" type="com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuSimpleVO">
        <result column="spu_id" property="spuId" />
        <result column="full_name" property="fullName" />
        <result column="name" property="name" />
        <result column="flavour" property="flavour" />
        <result column="rating" property="rating" />
        <result column="market_price" property="marketPrice" />
        <result column="sale_price" property="salePrice" />
        <result column="user_num_limit" property="userNumLimit" />
        <result column="images" property="images" typeHandler="com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler" />
        <result column="main_img" property="mainImg" />
        <result column="storeNum" property="storeNum" />
        <result column="sku_attributes" property="skuAttributes" />
        <result column="remark" property="remark" />
    </resultMap>

    <select id="selectSimplePackageBySpuId" resultMap="SpGoodsSpuSimpleVOResultMap">
        SELECT
            spu.id spu_id,
            spu.sp_id,
            spu.full_name		full_name,
            spu.short_name		name,
            spu.flavour         flavour,
            spu.rating         rating,
            sku.market_amount	market_price,
            sku.sale_amount		sale_price,
            sku.user_num_limit	user_num_limit,
            spu.images,
            spu.main_img,
            spu.remark,
            (SELECT COUNT(1) FROM   sp_goods_spu_store s WHERE  s.spu_id = spu.id AND s.deleted=0) storeNum,
            sku_attributes
        FROM
            sp_goods_spu spu
         JOIN
            sp_goods_sku sku ON spu.id = sku.spu_id AND sku.deleted = 0
        WHERE spu.deleted = 0 AND spu.id = #{spuId}
    </select>

    <select id="selectSimplePackageListBySpuIds" resultMap="SpGoodsSpuSimpleVOResultMap">
        SELECT
            spu.id spu_id,
            spu.full_name		full_name,
            spu.short_name		name,
            spu.flavour         flavour,
            spu.rating         rating,
            sku.market_amount	market_price,
            sku.sale_amount		sale_price,
            sku.user_num_limit	user_num_limit,
            spu.images,
            spu.main_img,
            (SELECT COUNT(1) FROM   sp_goods_spu_store s WHERE  s.spu_id = spu.id AND s.deleted=0) storeNum,
            sku_attributes
        FROM
            sp_goods_spu spu
        JOIN  sp_goods_sku sku ON spu.id = sku.spu_id AND sku.deleted = 0
        WHERE spu.deleted = 0 AND spu.id IN
        <foreach collection="spuIds" item="spuId" open="(" separator="," close=")">
            #{spuId}
        </foreach>
    </select>

    <select id="getSkuListBySpuId"
            resultType="com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSkuSampleVo">
        SELECT
            spu.id spuId,
            spu.full_name fullName,
            spu.sp_id spId,
            sku.id skuId
        FROM sp_goods_spu spu
        JOIN sp_goods_sku sku ON spu.id = sku.spu_id AND sku.deleted = 0
        WHERE spu.deleted = 0 AND spu.id = #{spuId}
    </select>

    <resultMap id="SpGoodsSpuChannelAppVoResultMap" type="com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuChannelAppVo">
        <result column="channel_config" property="channelConfig" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>
    
    <select id="getChannelAppConfigPageBySpId" resultMap="SpGoodsSpuChannelAppVoResultMap">
        SELECT
            a.id app_id
             ,a.`name` appName
             ,a.poi_type app_poi_type
             ,IFNULL(a.config ->> '$.isCommonCategory',0)  appIsCommonCategory
             ,yci.id channel_id
             ,yci.`name` 'channelName'
             ,yci.`code` 'channelCode'
             ,sgsc.spu_id
             ,sgsc.sku_id
             ,sgsc.id 'spuChannelId'
             ,sgsc.channel_spu_id
             ,sgsc.channel_sku_id
             ,sgsc.channel_category_id
             ,sgsc.channel_category_type
             ,sgsc.`status`
             ,sgsc.audit_status
             ,sgsc.audit_result
             ,sgsc.audit_msg
             ,sgsc.on_shelve
             ,sgsc.channel_config
        FROM yt_app_info a
        JOIN yt_channel_info yci ON a.channel_id = yci.id
        LEFT JOIN sp_goods_spu_channel sgsc ON sgsc.app_id = a.id AND sgsc.spu_id =  #{spSpuId}
        WHERE 1=1
        <if test="channelCodes != null">
            AND yci.`code` IN
            <foreach collection="channelCodes" item="channelCode" open="(" close=")" separator=",">
                #{channelCode}
            </foreach>
        </if>
        <if test="appName != null and appName != '' ">
            AND a.name LIKE CONCAT('%',#{appName},'%')
        </if>
        <if test="appId != null">
            AND a.id = #{appId}
        </if>
        ORDER BY spuChannelId DESC
    </select>

    <resultMap id="AppXhsMiniAppGoodsSpuRespMap" type="com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppGoodsSpuRespVo">
        <result column="images" property="images" typeHandler="com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler" />
    </resultMap>

    <select id="getSpGoodsSpuByStoreId" resultMap="AppXhsMiniAppGoodsSpuRespMap">
        SELECT
            spu.id  spu_id
             ,spu.full_name
             ,spu.short_name
             ,sku.id sku_id
             ,sku.sale_amount
             ,sku.market_amount
             ,spu.main_img
             ,spu.images
             ,spu.remark
             ,spu.flavour
        FROM sp_goods_sku sku
         JOIN sp_goods_spu spu ON sku.spu_id = spu.id AND  spu.deleted = 0
         JOIN sp_goods_spu_channel spuc ON spuc.spu_id = spu.id AND spuc.deleted = 0
         JOIN sp_goods_spu_store sgss ON sgss.spu_id = spu.id AND sgss.deleted = 0
        WHERE sgss.store_id = #{storeId}  AND spuc.app_id = #{appId} AND spuc.on_shelve = 1
    </select>

    <select id="getSpGoodsSpuHotBySpuIds" resultMap="AppXhsMiniAppGoodsSpuRespMap">
        SELECT
            spu.id  spu_id
             ,spu.full_name
             ,spu.short_name
             ,sku.id sku_id
             ,sku.sale_amount
             ,sku.market_amount
             ,spu.main_img
             ,spu.images
             ,spu.remark
             ,spu.flavour
        FROM sp_goods_sku sku
         JOIN sp_goods_spu spu ON sku.spu_id = spu.id AND  spu.deleted = 0
         JOIN sp_goods_spu_channel spuc ON spuc.spu_id = spu.id AND spuc.deleted = 0
        WHERE spuc.app_id = #{appId}
          AND spuc.on_shelve = 1
          AND spu.id IN
        <foreach collection="spuIds" item="spuId" open="(" close=")" separator=",">
            #{spuId}
        </foreach>
    </select>

    <resultMap id="AppXhsMiniAppGoodsSpuBookRespMap" type="com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppGoodsSpuBookRespVo">
        <result column="images" property="images" typeHandler="com.yitong.octopus.framework.mybatis.core.type.StringListTypeHandler" />
    </resultMap>

    <select id="getSpGoodsBookSpuByStoreId" resultMap="AppXhsMiniAppGoodsSpuBookRespMap">
        SELECT
            DISTINCT
            spu.id  spu_id
           ,spu.full_name
           ,spu.short_name
           ,sku.id sku_id
           ,sku.sale_amount
           ,sku.market_amount
           ,spu.images
           ,spu.remark
           ,spu.flavour
           ,bi.id book_config_id
        FROM sp_goods_sku sku
         JOIN sp_goods_spu spu ON sku.spu_id = spu.id AND  spu.deleted = 0
         JOIN sp_goods_spu_store sgss ON sgss.spu_id = spu.id AND sgss.deleted = 0
         JOIN yt_booking_info bi ON bi.sp_id = spu.sp_id AND bi.deleted = 0
         JOIN yt_booking_info_spu_ref spur ON bi.id = spur.booking_id
         LEFT JOIN yt_booking_info_store_ref bis ON bi.id = bis.booking_id
        WHERE (bi.all_store = 1 AND  sgss.store_id = #{storeId}) OR  (bi.all_store = 0  AND  bis.store_id = #{storeId})
    </select>

<!--    <select id="getSpGoodsBookSpuByStoreId" resultMap="AppXhsMiniAppGoodsSpuRespMap">-->
<!--        SELECT-->
<!--            DISTINCT-->
<!--            spu.id  spu_id-->
<!--                   ,spu.full_name-->
<!--                   ,spu.short_name-->
<!--                   ,sku.id sku_id-->
<!--                   ,sku.sale_amount-->
<!--                   ,sku.market_amount-->
<!--                   ,spu.images-->
<!--                   ,spu.remark-->
<!--                   ,spu.flavour-->
<!--        FROM sp_goods_sku sku-->
<!--                 JOIN sp_goods_spu spu ON sku.spu_id = spu.id AND  spu.deleted = 0-->
<!--                 JOIN sp_goods_spu_channel spuc ON spuc.spu_id = spu.id AND spuc.deleted = 0-->
<!--                 JOIN sp_goods_spu_store sgss ON sgss.spu_id = spu.id AND sgss.deleted = 0-->
<!--                 JOIN yt_book_config bc ON bc.spu_id = spu.id-->
<!--                 LEFT JOIN yt_book_config_sp_store bss ON BSS.book_config_id = bc.id-->
<!--        WHERE  spuc.app_id =  #{appId} AND  (bc.store_limit = 0 AND  sgss.store_id = #{storeId}) OR  (bc.store_limit =1  AND  bss.store_id = #{storeId})-->
<!--    </select>-->

    <select id="getSpGoodsSkuBySpuId" resultMap="AppXhsMiniAppGoodsSpuRespMap">
        SELECT
            spu.id  spu_id
             ,spu.full_name
             ,spu.short_name
             ,sku.id sku_id
             ,sku.sale_amount
             ,sku.market_amount
             ,spu.images
             ,spu.remark
             ,spu.flavour
        FROM sp_goods_spu spu
         JOIN sp_goods_sku sku ON sku.spu_id = spu.id AND  spu.deleted = 0
         JOIN sp_goods_spu_channel spuc ON spuc.spu_id = spu.id AND spuc.deleted = 0
        WHERE spu.id = #{spuId}  AND spuc.app_id = #{appId} AND spuc.on_shelve = 1
    </select>

    <select id="getStoreLbsLimitOneListBySpuId" resultType="com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuStoreLbsRespVO">
        WITH SpuStoreDistance AS (
            SELECT
                spus.spu_id
                ,ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_city
                ,ssi.sp_add
                ,ssi.rating
                ,ssi.link_phone
                ,ssi.link_tel
                ,ssi.store_tel
                ,ssi.flavour
                ,sim.longitude
                ,sim.latitude
                ,sim.map_poi
                ,ssi.store_open_time_desc
                ,sim.tenant_id
                 ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                , FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${longitude},${latitude}))) as distance
        FROM sp_goods_spu_store spus
            JOIN sp_store_info ssi ON ssi.id = spus.store_id AND ssi.deleted =0
            JOIN sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
        WHERE 1=1
          AND spus.deleted = 0
          AND spus.spu_id IN
            <foreach collection="spuIds" item="spuId" open="(" close=")" separator=",">
                #{spuId}
            </foreach>
          AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
        ),
        RankedSpuStore AS (
            SELECT
                spu_id,
                store_id,
                store_name,
                store_logo,
                sp_city,
                sp_add,
                rating,
                link_phone,
                link_tel,
                store_tel,
                flavour,
                longitude,
                latitude,
                map_poi,
                store_open_time_desc,
                store_main_image,
                distance,
                tenant_id,
                ROW_NUMBER() OVER (PARTITION BY spu_id ORDER BY distance ASC) AS rn
            FROM SpuStoreDistance
        )
        SELECT spu_id, store_id, store_name, store_logo, sp_add, sp_city, rating,link_phone,link_tel,longitude,latitude,map_poi,store_tel,store_open_time_desc,store_main_image, distance,	tenant_id,flavour
        FROM RankedSpuStore
        WHERE rn = 1
    </select>

    <select id="getSpGoodsSpuBySpIdAndAppId" resultMap="AppXhsMiniAppGoodsSpuRespMap">
        SELECT
        spu.id  spu_id
        ,spu.full_name
        ,spu.short_name
        ,sku.id sku_id
        ,sku.sale_amount
        ,sku.market_amount
        ,spu.main_img
        ,spu.images
        ,spu.remark
        ,spu.flavour
        FROM sp_goods_sku sku
        JOIN sp_goods_spu spu ON sku.spu_id = spu.id AND  spu.deleted = 0
        JOIN sp_goods_spu_channel spuc ON spuc.spu_id = spu.id AND spuc.deleted = 0
        WHERE spuc.app_id = #{appId}
        AND spuc.on_shelve = 1
        AND spu.sp_id =#{spId}
    </select>

    <select id="getStoreNumListBySpuId" resultType="com.yitong.octopus.module.sp.api.spu.dto.SpGoodsSpuStoreNumRespVO">
        SELECT
            spu_id spuId
            ,count(1) storeNum
        FROM sp_goods_spu_store
        WHERE 1=1
        AND deleted = 0
        AND spu_id IN
        <foreach collection="spuIds" item="spuId" open="(" close=")" separator=",">
            #{spuId}
        </foreach>
        GROUP BY spu_id
    </select>

</mapper>
