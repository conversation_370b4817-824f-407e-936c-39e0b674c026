<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpSpuStoreMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getGoodsSpuStoreListBySpuId"
            resultType="com.yitong.octopus.module.sp.api.store.vo.SpGoodsSpuStoreVO">
        SELECT
         s.id store_id
         ,s.store_name
        FROM sp_goods_spu_store ss
        JOIN 	sp_store_info s ON ss.store_id  = s.id
        WHERE ss.spu_id = #{id}
    </select>

    <select id="getGoodsSpuValidStoreListBySpuId"
            resultType="com.yitong.octopus.module.sp.api.store.vo.SpGoodsSpuStoreVO">
        SELECT
            s.id store_id
             ,s.store_name
        FROM sp_goods_spu_store ss
        JOIN sp_store_info s ON ss.store_id  = s.id
        WHERE ss.spu_id = #{id} AND s.sp_status = 2
    </select>

    <select id="getSpuChannelCode" resultType="java.lang.String">
        SELECT
            c.channel_spu_id
        FROM sp_goods_spu_channel c
        WHERE c.deleted = 0
          AND c.spu_id = #{spuId}
          AND c.channel_code = #{channelCode}
    </select>

</mapper>
