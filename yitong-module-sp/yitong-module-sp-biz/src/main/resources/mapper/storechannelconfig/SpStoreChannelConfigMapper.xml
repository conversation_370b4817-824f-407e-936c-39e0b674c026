<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.storechannelconfig.SpStoreChannelConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getStoreChannelConfigListByStoreId"
            resultType="com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigVo">
        SELECT
            yci.id
             ,yci.`name`
             ,yci.`code`
             ,yci.logo
             , sscc.id spStoreChannelConfigId
             ,sscc.audit_status
             ,sscc.audit_reason
             ,sscc.audit_time
             ,sscc.channel_store_poi
             ,sscc.channel_store_name
             ,sscc.channel_store_id
             ,sscc.channel_is_group
        FROM yt_channel_info yci
                 LEFT JOIN sp_store_channel_config sscc ON yci.id = sscc.channel_id AND sscc.store_id= #{storeId}
        WHERE yci.`status`=1
        ORDER BY yci.sort desc ,yci.id desc;
    </select>

    <select id="getStoreChannelConfigPageByStoreId"
            resultType="com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigVo">
        SELECT
            yci.id
            ,yci.`name`
            ,yci.`code`
            ,yci.logo
            , sscc.id spStoreChannelConfigId
            ,sscc.audit_status
            ,sscc.audit_reason
            ,sscc.audit_time
            ,sscc.channel_store_id
            ,sscc.channel_store_poi
            ,sscc.channel_store_name
            ,sscc.channel_is_group
        FROM yt_channel_info yci
        LEFT JOIN sp_store_channel_config sscc ON yci.id = sscc.channel_id AND sscc.store_id= #{storeId}
        WHERE yci.`status`=1
        ORDER BY yci.sort desc ,yci.id desc;
    </select>

    <select id="getStoreChannelConfigByStoreIdAndChannelCode"
            resultType="com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigVo">
        SELECT
            yci.id
             ,yci.`name`
             ,yci.`code`
             ,yci.logo
             , sscc.id spStoreChannelConfigId
             ,sscc.audit_status
             ,sscc.audit_reason
             ,sscc.audit_time
             ,sscc.channel_store_id
             ,sscc.channel_store_poi
             ,sscc.channel_store_name
             ,sscc.channel_is_group
        FROM sp_store_channel_config sscc
        JOIN yt_channel_info yci ON yci.id = sscc.channel_id
        WHERE sscc.deleted = '0'
            AND sscc.store_id =#{storeId}
            AND yci.`code` = #{channelCode}
        LIMIT 1
    </select>

    <select id="getStoreChannelAppConfigPageByStoreId"
            resultType="com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelAppConfigVo">
        SELECT
            yci.id channel_id
             ,yci.`name` channel_name
             ,yci.`code` channel_code
             ,a.id app_id
             ,a.name app_name
             ,a.poi_type app_poi_type
             ,IFNULL(a.config ->> '$.isCommonCategory',0)  appIsCommonCategory
             ,sscc.sp_id
             ,sscc.category_id
             ,sscc.category_name
             ,sscc.channel_store_poi
             ,sscc.channel_result
             ,sscc.audit_status
             ,sscc.audit_reason
             ,sscc.audit_time
             ,sscc.id
        FROM yt_app_info a
         JOIN yt_channel_info yci ON a.channel_id = yci.id AND yci.deleted = 0  AND yci.`status` = 1
         LEFT JOIN sp_store_channel_config sscc ON a.id = sscc.app_id  AND  sscc.store_id = #{storeId}
        WHERE a.`status` = 1
        <if test="channelCodes != null">
            AND yci.`code` IN
            <foreach collection="channelCodes" item="channelCode" open="(" close=")" separator=",">
                #{channelCode}
            </foreach>
        </if>
        <if test="appName != null and appName != '' ">
            AND a.name LIKE CONCAT('%',#{appName},'%')
        </if>
        <if test="appId != null">
            AND a.id = #{appId}
        </if>
        ORDER BY a.sort desc,yci.sort desc ,yci.id desc
    </select>

    <select id="getChannelAppConfigPageByAppId"
            resultType="com.yitong.octopus.module.sp.dal.dto.spstore.AppXhsMiniAppIndexStoreRespVo">
        SELECT
            a.*
        FROM  (
            SELECT
                ssi.id store_id
                ,ssi.store_name
                ,ssi.store_logo
                ,ssi.sp_city
                ,ssi.sp_add
                ,(SELECT ssig.image_url FROM sp_store_info_gallery ssig WHERE ssig.store_id = ssi.id AND ssig.image_type =1 AND ssig.deleted = 0 LIMIT 1 ) store_main_image
                ,FLOOR(st_distance_sphere(point(sim.longitude,sim.latitude),point(${reqVo.longitude},${reqVo.latitude}))) as distance
            FROM sp_store_channel_config scc
            JOIN sp_store_info ssi ON scc.store_id = ssi.id  AND ssi.deleted =0
            JOIN  sp_store_info_map sim ON sim.store_id = ssi.id AND sim.deleted = 0
            LEFT JOIN sp_store_info_gallery  ssig ON ssig.store_id = ssi.id  AND ssig.image_type =1 AND ssig.deleted = 0
            WHERE scc.app_id = #{appId}
                AND sim.latitude IS NOT NULL AND sim.longitude IS NOT NULL
            <if test="reqVo.storeName != null and reqVo.storeName != ''">
                AND ssi.store_name  like CONCAT('%', #{reqVo.storeName}, '%')
            </if>
            <if test="reqVo.city != null and reqVo.city !='' ">
                AND  ssi.sp_city = #{reqVo.city}
            </if>
            <if test="reqVo.categoryId != null and reqVo.categoryId !='' ">
                AND  scc.category_id = #{reqVo.categoryId}
            </if>
            <if test="reqVo.radius != null ">
                HAVING  distance &lt;= #{reqVo.radius}
            </if>

     ) a
    </select>
</mapper>
