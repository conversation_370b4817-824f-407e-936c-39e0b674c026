<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.mainchannelconfig.SpMainChannelConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getChannelConfigListBySpId"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelConfigVo">
        SELECT
          yci.id
         ,yci.`name`
         ,yci.`code`
         ,smcc.sp_id
         ,smcc.category_id
         ,smcc.category_name
         ,smcc.channel_sp_id
         ,smcc.audit_status
         ,smcc.audit_reason
         ,smcc.audit_time
         , smcc.id spChannelConfigId
        FROM yt_channel_info yci
        LEFT JOIN sp_main_channel_config smcc ON yci.id = smcc.channel_id AND  smcc.sp_id= #{spId}
        WHERE yci.`status` = 1
        ORDER BY yci.sort desc ,yci.id desc
    </select>

    <select id="getChannelConfigPageBySpId"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelConfigVo">
        SELECT
            yci.id
            ,yci.`name`
            ,yci.`code`
            ,smcc.sp_id
            ,smcc.category_id
            ,smcc.category_name
            ,smcc.channel_sp_id
            ,smcc.audit_status
            ,smcc.audit_reason
            ,smcc.audit_time
            ,smcc.id spChannelConfigId
            ,tmct.access_token_valid_time accessTokenValidTime
            ,tmct.refresh_msg accessTokenRefreshMsg
        FROM yt_channel_info yci
        LEFT JOIN sp_main_channel_config smcc ON yci.id = smcc.channel_id AND  smcc.sp_id = #{spId}
        LEFT JOIN t_merchant_channel_token tmct ON smcc.sp_id = tmct.merchant_id AND yci.`code` = tmct.channel_code
        WHERE yci.`status` = 1
        ORDER BY yci.sort desc ,yci.id desc
    </select>

    <select id="getChannelAppConfigPageBySpId"
            resultType="com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelAppConfigVo">
        SELECT
            yci.id channel_id
             ,yci.`name` channel_name
             ,yci.`code` channel_code
             ,a.id app_id
             ,a.name app_name
             ,smcc.category_id
             ,smcc.category_name
             ,smcc.channel_sp_id
             ,smcc.channel_result
             ,smcc.audit_status
             ,smcc.audit_reason
             ,smcc.audit_time
             ,smcc.sp_id
             ,smcc.id
        FROM yt_app_info a
        JOIN yt_channel_info yci ON a.channel_id = yci.id AND yci.deleted = 0  AND yci.`status` = 1
        LEFT JOIN sp_main_channel_config smcc ON a.id = smcc.app_id  AND  smcc.sp_id = #{spId}
        WHERE a.`status` = 1
        <if test="channelCodes != null">
            AND yci.`code` IN 
            <foreach collection="channelCodes" item="channelCode" open="(" close=")" separator=",">
                #{channelCode}
            </foreach>
        </if>
        ORDER BY a.sort desc,yci.sort desc ,yci.id desc
    </select>
</mapper>
