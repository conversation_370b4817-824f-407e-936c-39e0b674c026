<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.goodsspustockrationing.GoodsSpuStockRationingItemMapper">

    <select id="getGoodsSpuStockRationingBeforeItemByRationingId" resultType="java.lang.Long">
        SELECT IFNULL(SUM(total_stock),0) total
        FROM sp_goods_spu_stock_rationing_item
        WHERE rationing_id = #{rationingId} AND status !=0 AND DATE_FORMAT(day,'%Y-%m-%d') &lt; DATE_FORMAT(NOW(),'%Y-%m-%d')
    </select>

</mapper>