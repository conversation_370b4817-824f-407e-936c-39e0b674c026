<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.goodsspustockrationing.GoodsSpuStockRationingMapper">

    <select id="getGoodsSpuStockRationingByCurrent"
            resultType="com.yitong.octopus.module.sp.dal.dto.GoodsSpuStockRationingDto">
        SELECT
            r.spu_id spuId
            ,r.channel_code channelCode
            ,ri.real_total_stock totalStock
        FROM sp_goods_spu_stock_rationing r
        JOIN sp_goods_spu_stock_rationing_item ri ON r.id = ri.rationing_id AND ri.deleted =0
        WHERE r.deleted = 0
          AND DATE_FORMAT(r.start_time,'%Y-%m-%d')  &lt;= DATE_FORMAT( NOW(),'%Y-%m-%d')
          AND DATE_FORMAT(r.end_time,'%Y-%m-%d') >= DATE_FORMAT( NOW(),'%Y-%m-%d')
          AND DATE_FORMAT( ri.`day`,'%Y-%m-%d') = DATE_FORMAT( NOW(),'%Y-%m-%d')
          AND r.`status` = 1 AND ri.`status` = 1
    </select>

    <update id="openGoodsSpuStockRation">
        UPDATE sp_goods_spu_stock_rationing
        SET `status` = 1
        WHERE NOW() BETWEEN start_time AND end_time AND `status` =0
    </update>

    <update id="endGoodsSpuStockRation">
        UPDATE sp_goods_spu_stock_rationing
        SET `status` = 2
        WHERE NOW()  > end_time AND `status` = 1
    </update>

</mapper>