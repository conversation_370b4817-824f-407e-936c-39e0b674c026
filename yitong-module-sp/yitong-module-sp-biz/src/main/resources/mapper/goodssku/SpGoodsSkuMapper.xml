<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.goodssku.SpGoodsSkuMapper">
    <select id="selectSkuSampleBySkuId"
            resultType="com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuSampleRespVO">
        SELECT
          sku.id
         ,sku.spu_id
         ,spu.sp_id
         ,sku.bill_stock
         ,sku.occupy_stock
         ,sku.sale_stock
         ,sku.sale_amount sale_price
         ,sku.market_amount market_price
         ,sku.settle_amount settle_price
         ,IFNULL(sku.`name`,spu.full_name) `name`
         ,(SELECT count(1) store_total  FROM  sp_goods_spu_store  WHERE spu_id = sku.spu_id ) store_total
        FROM sp_goods_sku sku
        JOIN sp_goods_spu spu ON sku.spu_id = spu.id
        WHERE sku.id  = #{skuId}
    </select>

<!--    .eqIfPresent(SpGoodsSkuDO::getSpId, reqVO.getSpId())-->
<!--    .eqIfPresent(SpGoodsSkuDO::getStoreId, reqVO.getStoreId())-->
<!--    .eqIfPresent(SpGoodsSkuDO::getSpuId, reqVO.getSpuId())-->
<!--    .betweenIfPresent(SpGoodsSkuDO::getCreateTime, reqVO.getCreateTime())-->
<!--    .selectAll(SpGoodsSkuDO.class)-->
<!--    .innerJoin(SpMainInfoDO.class,SpMainInfoDO::getId,SpGoodsSkuDO::getSpId)-->
<!--    .orderByDesc(SpGoodsSkuDO::getId);-->
    <select id="selectGoodsSkuPage" resultType="com.yitong.octopus.module.sp.dal.dataobject.goodssku.SpGoodsSkuDO">
        SELECT
        t.*
        FROM sp_goods_sku  t
        INNER JOIN sp_goods_spu t1 ON (t1.id = t.spu_id AND t1.deleted=0)
        WHERE  t.deleted=0
        <if test="vo.id !=null">
            AND t.id = #{vo.id}
        </if>
        <if test="vo.spId !=null">
            AND t.sp_id = #{vo.spId}
        </if>
        <if test="vo.spuId !=null">
            AND t.spu_id = #{vo.spuId}
        </if>
        <if test="vo.spuName !=null and vo.spuName!=''">
            AND t1.full_name LIKE CONCAT('%', #{vo.spuName},'%')
        </if>
        <if test="vo.createTime !=null and vo.createTime.length > 0 ">
            and t.create_time between #{vo.createTime[0],javaType=java.time.LocalDateTime} and #{vo.createTime[1],javaType=java.time.LocalDateTime}
        </if>
        ORDER BY t.id DESC
    </select>

    <select id="selectPageSample"
            resultType="com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuPageRespVO">
        SELECT
            t.*
            ,t1.full_name spuName
            ,t1.sold_date_to soldDateTo
        FROM sp_goods_sku  t
        INNER JOIN sp_goods_spu t1 ON (t1.id = t.spu_id AND t1.deleted=0)
        WHERE  t.deleted=0
            <if test="vo.id !=null">
                AND t.id = #{vo.id}
            </if>
            <if test="vo.spId !=null">
                AND t.sp_id = #{vo.spId}
            </if>
            <if test="vo.spuId !=null">
                AND t.spu_id = #{vo.spuId}
            </if>
            <if test="vo.spuName !=null and vo.spuName!=''">
                AND t1.full_name LIKE CONCAT('%', #{vo.spuName},'%')
            </if>
        ORDER BY t.id DESC
    </select>

    <update id="updateSkuStockBySkuId">
        UPDATE sp_goods_sku
        SET bill_stock = sale_stock + #{stock}
          , sale_stock = sale_stock + #{stock}
        WHERE id = #{skuId} AND deleted = 0
        <if test="stock &lt; 0">
            AND sale_stock >= ABS(#{stock})
        </if>
    </update>

    <update id="setSkuStockBySkuId">
        UPDATE sp_goods_sku
        SET bill_stock = #{stock}
        , sale_stock = #{stock}
        WHERE id = #{skuId} AND deleted = 0
    </update>

    <update id="lockStockBySku">
        UPDATE sp_goods_sku
        SET sale_stock = sale_stock - #{quantity}
        WHERE id = #{skuId} AND sale_stock > 0 AND sale_stock >= #{quantity}  AND deleted = 0
    </update>

    <update id="releaseStockBySku">
        UPDATE sp_goods_sku
        SET sale_stock = sale_stock + #{quantity}
        WHERE id = #{skuId} AND deleted = 0
    </update>
</mapper>
