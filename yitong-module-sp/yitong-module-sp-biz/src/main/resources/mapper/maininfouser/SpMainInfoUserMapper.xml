<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.sp.dal.mysql.maininfouser.SpMainInfoUserMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectSpMainInfoUserPage"
            resultType="com.yitong.octopus.module.sp.controller.admin.maininfouser.vo.SpMainInfoUserRespVO">
            SELECT
                tu.*
                 ,ssi.store_name
            FROM t_user tu
            LEFT JOIN sp_store_info  ssi ON ssi.id = tu.store_id AND ssi.deleted = 0
            WHERE 1=1
            <if test="vo.spId !=null ">
                AND tu.merchant_id = #{vo.spId}
            </if>
            <if test="vo.name != null and vo.name != ''">
                AND tu.name  like concat('%', #{name}, '%')
            </if>
            <if test="vo.mobile != null and vo.mobile != ''">
                AND tu.mobile  like concat('%', #{vo.mobile}, '%')
            </if>
            <if test="vo.status !=null ">
                AND tu.status = #{vo.status}
            </if>
            <if test="vo.type !=null ">
                AND tu.type = #{vo.type}
            </if>
            <if test="vo.storeId !=null ">
                AND tu.store_id = #{vo.storeId}
            </if>
            ORDER BY tu.id DESC
    </select>

    <select id="getMainInfoUserListByStoreId"
            resultType="com.yitong.octopus.module.sp.dal.dataobject.maininfouser.SpMainInfoUserDO">
            SELECT
                *
            FROM t_user
            WHERE merchant_id = #{spId}
              AND (store_id =  #{storeId} OR store_id is NULL)
    </select>

    <select id="selectCanRedeemUserByCouponCode"
            resultType="com.yitong.octopus.module.sp.dal.dataobject.maininfouser.SpMainInfoUserDO"
            parameterType="java.lang.String">
        SELECT
            u.id
             ,u.`name`
             ,u.type
             ,u.merchant_id spId
             ,ss.store_id
        FROM sp_goods_spu_store ss
         JOIN t_coupon c ON c.spu_id = ss.spu_id
         LEFT JOIN t_user u  ON u.merchant_id = c.merchant_id
        WHERE ss.deleted = 0
          AND  c.`code`=  #{couponCode}
          AND ((u.type = 1 AND u.store_id IS NULL) OR (u.store_id = ss.store_id ))
        ORDER BY u.type desc
        LIMIT 1
    </select>

    <select id="selectCanRedeemUserByStoreId"
            resultType="com.yitong.octopus.module.sp.dal.dataobject.maininfouser.SpMainInfoUserDO"
            parameterType="java.lang.Long">
        SELECT
            u.id
             ,u.`name`
             ,u.merchant_id spId
             ,u.store_id
        FROM t_user u
         JOIN sp_store_info si  ON u.merchant_id = si.sp_id
        WHERE  u.`status` = 1
          AND  u.type = 1
          AND  si.id = #{storeId}
        LIMIT 1
    </select>
</mapper>
