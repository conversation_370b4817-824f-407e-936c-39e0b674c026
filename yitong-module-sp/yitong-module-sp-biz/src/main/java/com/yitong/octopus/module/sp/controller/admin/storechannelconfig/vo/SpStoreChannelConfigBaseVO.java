package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 服务商门店-渠道配置信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreChannelConfigBaseVO {

    /**
     * appId
     */
    @Schema(description = "appId", example = "1")
    private Long appId;

    @Schema(description = "门店ID", example = "4888")
    private Long storeId;

    @Schema(description = "平台渠道ID", example = "19503")
    @NotNull(message = "平台渠道ID不能为空")
    private Long channelId;

    @Schema(description = "配置信息值", required = true)
    @NotBlank(message = "配置信息值不能为空")
    private String configValue;
}
