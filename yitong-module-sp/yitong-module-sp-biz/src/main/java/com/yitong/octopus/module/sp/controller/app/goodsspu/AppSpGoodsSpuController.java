package com.yitong.octopus.module.sp.controller.app.goodsspu;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.app.goodsspu.vo.AppSpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.app.goodsspu.vo.AppSpGoodsSpuRespVO;
import com.yitong.octopus.module.sp.controller.app.goodsspu.vo.AppSpGoodsSpuStoreRespVO;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspu.SpGoodsSpuDO;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO;
import com.yitong.octopus.module.sp.service.goodssku.SpGoodsSkuService;
import com.yitong.octopus.module.sp.service.goodsspu.SpGoodsSpuService;
import com.yitong.octopus.module.sp.service.storeinfo.SpStoreInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import java.util.List;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

@Tag(name = "前端 - 商品spu")
@RestController
@RequestMapping("/sp/goods-spu")
@Validated
@Slf4j
public class AppSpGoodsSpuController {

	@Resource
	private SpGoodsSpuService goodsSpuService;

	@Resource
	private SpGoodsSkuService spGoodsSkuService;

	@Resource
	private SpStoreInfoService spStoreInfoService;

	@GetMapping("/get")
	@Operation(summary = "获得商品spu")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
	@PermitAll
	public CommonResult<AppSpGoodsSpuRespVO> getGoodsSpu(@RequestParam("id") Long id) {
		SpGoodsSpuDO spu = goodsSpuService.getGoodsSpu(id);
		if (ObjectUtil.isNull(spu)){
			return success(null);
		}
		AppSpGoodsSpuRespVO vo = BeanUtil.toBean(spu,AppSpGoodsSpuRespVO.class);
		vo.setMainImage(spu.getMainImg());
		vo.setRotationImages(spu.getRotationImg());
		List<SpGoodsSkuRespVO> skuList= spGoodsSkuService.getGoodsSkuListBySpuId(spu.getId());
		vo.setSkus(BeanUtils.copyToList(skuList, AppSpGoodsSkuRespVO.class));
		List<SpStoreInfoDO>  spStoreInfoList = spStoreInfoService.getStoreInfoListBySpuId(id);
		vo.setStoreList(BeanUtils.copyToList(spStoreInfoList, AppSpGoodsSpuStoreRespVO.class));
		return success(vo);
	}
}
