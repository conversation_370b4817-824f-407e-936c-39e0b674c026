package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingDO;
import com.yitong.octopus.module.sp.service.goodsspustockrationing.GoodsSpuStockRationingService;

@Tag(name = "管理后台 - 商品库存供给")
@RestController
@RequestMapping("/sp/goods-spu-stock-rationing")
@Validated
public class GoodsSpuStockRationingController {

    @Resource
    private GoodsSpuStockRationingService goodsSpuStockRationingService;

    @PostMapping("/create")
    @Operation(summary = "创建商品库存供给")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:create')")
    public CommonResult<Long> createGoodsSpuStockRationing(@Valid @RequestBody GoodsSpuStockRationingSaveReqVO createReqVO) {
        return success(goodsSpuStockRationingService.createGoodsSpuStockRationing(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品库存供给")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:update')")
    public CommonResult<Boolean> updateGoodsSpuStockRationing(@Valid @RequestBody GoodsSpuStockRationingSaveReqVO updateReqVO) {
        goodsSpuStockRationingService.updateGoodsSpuStockRationing(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品库存供给")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:delete')")
    public CommonResult<Boolean> deleteGoodsSpuStockRationing(@RequestParam("id") Long id) {
        goodsSpuStockRationingService.deleteGoodsSpuStockRationing(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品库存供给")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:query')")
    public CommonResult<GoodsSpuStockRationingRespVO> getGoodsSpuStockRationing(@RequestParam("id") Long id) {
        GoodsSpuStockRationingDO goodsSpuStockRationing = goodsSpuStockRationingService.getGoodsSpuStockRationing(id);
        return success(BeanUtils.toBean(goodsSpuStockRationing, GoodsSpuStockRationingRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品库存供给分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:query')")
    public CommonResult<PageResult<GoodsSpuStockRationingRespVO>> getGoodsSpuStockRationingPage(@Valid GoodsSpuStockRationingPageReqVO pageReqVO) {
        PageResult<GoodsSpuStockRationingDO> pageResult = goodsSpuStockRationingService.getGoodsSpuStockRationingPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GoodsSpuStockRationingRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品库存供给 Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing:export')")
    public void exportGoodsSpuStockRationingExcel(@Valid GoodsSpuStockRationingPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GoodsSpuStockRationingDO> list = goodsSpuStockRationingService.getGoodsSpuStockRationingPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品库存供给.xls", "数据", GoodsSpuStockRationingRespVO.class,
                        BeanUtils.toBean(list, GoodsSpuStockRationingRespVO.class));
    }

}