package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 主体素材 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoMaterialsExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("商家名称ID")
    private Long spId;

    @ExcelProperty("商家名称")
    private String spName;

    @ExcelProperty("门店ID")
    private Long storeId;

    @ExcelProperty("门店名称")
    private String storeName;

    @ExcelProperty("类型")
    private Integer type;

    @ExcelProperty("文件类型")
    private Integer fileType;

    @ExcelProperty("状态")
    private Integer status;

    @ExcelProperty("审核意见")
    private String auditMsg;

    @ExcelProperty("审核人")
    private String auditUser;

    @ExcelProperty("文件url")
    private String fileUrl;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
