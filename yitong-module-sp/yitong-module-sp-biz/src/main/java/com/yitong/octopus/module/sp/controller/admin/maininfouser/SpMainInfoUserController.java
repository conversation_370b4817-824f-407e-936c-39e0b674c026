package com.yitong.octopus.module.sp.controller.admin.maininfouser;

import com.yitong.octopus.module.sp.enums.SpMainAccountStatusEnum;
import com.yitong.octopus.module.sp.enums.SpMainAccountTypeEnum;
import com.yitong.octopus.module.system.controller.admin.user.vo.user.UserUpdateStatusReqVO;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.maininfouser.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfouser.SpMainInfoUserDO;
import com.yitong.octopus.module.sp.convert.spmaininfouser.SpMainInfoUserConvert;
import com.yitong.octopus.module.sp.service.maininfouser.SpMainInfoUserService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 商户子账号")
@RestController
@RequestMapping("/sp/main-info-user")
@Validated
public class SpMainInfoUserController {

    @Resource
    private SpMainInfoUserService mainInfoUserService;

    @PostMapping("/create")
    @Operation(summary = "创建商户子账号")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:create')")
    public CommonResult<Long> createMainInfoUser(@Valid @RequestBody SpMainInfoUserCreateReqVO createReqVO) {
        return success(mainInfoUserService.createMainInfoUser(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商户子账号")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:update')")
    public CommonResult<Boolean> updateMainInfoUser(@Valid @RequestBody SpMainInfoUserUpdateReqVO updateReqVO) {
        mainInfoUserService.updateMainInfoUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商户子账号")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:delete')")
    public CommonResult<Boolean> deleteMainInfoUser(@RequestParam("id") Long id) {
        mainInfoUserService.deleteMainInfoUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商户子账号")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:query')")
    public CommonResult<SpMainInfoUserRespVO> getMainInfoUser(@RequestParam("id") Long id) {
        SpMainInfoUserDO mainInfoUser = mainInfoUserService.getMainInfoUser(id);
        return success(SpMainInfoUserConvert.INSTANCE.convert(mainInfoUser));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商户子账号列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:query')")
    public CommonResult<List<SpMainInfoUserRespVO>> getMainInfoUserList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoUserDO> list = mainInfoUserService.getMainInfoUserList(ids);
        return success(SpMainInfoUserConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商户子账号分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:query')")
    public CommonResult<PageResult<SpMainInfoUserRespVO>> getMainInfoUserPage(@Valid SpMainInfoUserPageReqVO pageVO) {
        return success(mainInfoUserService.getMainInfoUserPage(pageVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商户核销账号 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:export')")
    public void exportMainInfoUserExcel(@Valid SpMainInfoUserExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoUserDO> list = mainInfoUserService.getMainInfoUserList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoUserExcelVO> datas = SpMainInfoUserConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商户核销账号.xls", "数据", SpMainInfoUserExcelVO.class, datas);
    }

    @PutMapping("/update-status")
    @Operation(summary = "修改商户核销账号状态")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:update')")
    public CommonResult<Boolean> updateUserStatus(@Valid @RequestBody UserUpdateStatusReqVO reqVO) {
        mainInfoUserService.updateUserStatus(reqVO.getId(), reqVO.getStatus());
        return success(true);
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入商户核销账号模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<SpMainInfoUserImportExcelVO> list = Arrays.asList(
                SpMainInfoUserImportExcelVO.builder().spId(1L).name(SpMainAccountTypeEnum.SP_ADMIN.getName()).mobile("***********").password("***********")
                        .status(SpMainAccountStatusEnum.ENABLE.getStatus())
                        .type(SpMainAccountTypeEnum.SP_ADMIN.getType())
                        .build(),
                SpMainInfoUserImportExcelVO.builder().spId(1L).name(SpMainAccountTypeEnum.SP_STORE_ADMIN.getName()).mobile("***********").password("***********")
                        .status(SpMainAccountStatusEnum.ENABLE.getStatus())
                        .type(SpMainAccountTypeEnum.SP_STORE_ADMIN.getType())
                        .storeId(2L)
                        .build(),
                SpMainInfoUserImportExcelVO.builder().spId(1L).name(SpMainAccountTypeEnum.SP_STORE_USER.getName()).mobile("***********").password("***********")
                        .status(SpMainAccountStatusEnum.ENABLE.getStatus())
                        .type(SpMainAccountTypeEnum.SP_STORE_USER.getType())
                        .storeId(2L)
                        .build()

        );
        // 输出
        ExcelUtils.write(response, "商户核销账号导入模板.xls", "商户核销账号列表", SpMainInfoUserImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入商户核销账号")
    @Parameters({
        @Parameter(name = "file", description = "Excel 文件", required = true),
        @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:import')")
    public CommonResult<SpMainInfoUserImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                      @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<SpMainInfoUserImportExcelVO> list = ExcelUtils.read(file, SpMainInfoUserImportExcelVO.class);
        return success(mainInfoUserService.importUserList(list, updateSupport));
    }

    @GetMapping("/get-store-users")
    @Operation(summary = "获得商户子账号")
    @Parameter(name = "spId", description = "商户id", required = true, example = "1024")
    @Parameter(name = "storeId", description = "门店Id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-user:query')")
    public CommonResult<List<SpMainInfoUserSimpleRespVO>> getMainInfoUserByStoreId(@RequestParam("spId") Long spId,@RequestParam("storeId") Long storeId) {
        List<SpMainInfoUserDO> mainInfoUser = mainInfoUserService.getMainInfoUserListByStoreId(spId,storeId);
        return success(SpMainInfoUserConvert.INSTANCE.convertSampleList(mainInfoUser));
    }
}
