package com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品Spu三方券码新增/修改 Request VO")
@Data
public class GoodsSpuThirdCouponSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25547")
    private Long id;

    @Schema(description = "商品ID", example = "20446")
    private Long spuId;

    @Schema(description = "券码")
    private String couponCode;

    @Schema(description = "状态：0无效 1有效", example = "1")
    private Integer status;

    @Schema(description = "核销开始时间")
    private LocalDateTime validDateFrom;

    @Schema(description = "核销开始时间")
    private LocalDateTime validDateTo;

}