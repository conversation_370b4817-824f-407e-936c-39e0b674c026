//package com.yitong.octopus.module.sp.controller.admin.maininfopropertiesrel.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.*;
//import java.time.LocalDateTime;
//import com.alibaba.excel.annotation.*;
//
//@Schema(description = "管理后台 - 商家属性关系 Response VO")
//@Data
//@ExcelIgnoreUnannotated
//public class MainInfoPropertiesRelRespVO {
//
//    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23868")
//    @ExcelProperty("ID")
//    private Long id;
//
//    @Schema(description = "商家ID", example = "19721")
//    @ExcelProperty("商家ID")
//    private Long spId;
//
//    @Schema(description = "属性ID", example = "537")
//    @ExcelProperty("属性ID")
//    private Long propertyId;
//
//    @Schema(description = "属性值ID", example = "5102")
//    @ExcelProperty("属性值ID")
//    private Long valueId;
//
//    @Schema(description = "属性值名称", example = "李四")
//    @ExcelProperty("属性值名称")
//    private String valueName;
//
//    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("创建时间")
//    private LocalDateTime createTime;
//
//}