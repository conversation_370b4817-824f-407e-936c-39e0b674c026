package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 服务商门店-渠道配置信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigCreateReqVO extends SpStoreChannelConfigBaseVO {

}
