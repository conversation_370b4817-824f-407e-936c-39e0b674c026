//package com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.storeinfoopeninghours.SpStoreInfoOpeningHoursDO;
//import com.yitong.octopus.module.sp.convert.storeinfoopeninghours.SpStoreInfoOpeningHoursConvert;
//import com.yitong.octopus.module.sp.service.storeinfoopeninghours.SpStoreInfoOpeningHoursService;
//
//@Tag(name = "管理后台 - 商家门店营业时间")
//@RestController
//@RequestMapping("/sp/store-info-opening-hours")
//@Validated
//public class SpStoreInfoOpeningHoursController {
//
//    @Resource
//    private SpStoreInfoOpeningHoursService storeInfoOpeningHoursService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家门店营业时间")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:create')")
//    public CommonResult<Long> createStoreInfoOpeningHours(@Valid @RequestBody SpStoreInfoOpeningHoursCreateReqVO createReqVO) {
//        return success(storeInfoOpeningHoursService.createStoreInfoOpeningHours(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家门店营业时间")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:update')")
//    public CommonResult<Boolean> updateStoreInfoOpeningHours(@Valid @RequestBody SpStoreInfoOpeningHoursUpdateReqVO updateReqVO) {
//        storeInfoOpeningHoursService.updateStoreInfoOpeningHours(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家门店营业时间")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:delete')")
//    public CommonResult<Boolean> deleteStoreInfoOpeningHours(@RequestParam("id") Long id) {
//        storeInfoOpeningHoursService.deleteStoreInfoOpeningHours(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家门店营业时间")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:query')")
//    public CommonResult<SpStoreInfoOpeningHoursRespVO> getStoreInfoOpeningHours(@RequestParam("id") Long id) {
//        SpStoreInfoOpeningHoursDO storeInfoOpeningHours = storeInfoOpeningHoursService.getStoreInfoOpeningHours(id);
//        return success(SpStoreInfoOpeningHoursConvert.INSTANCE.convert(storeInfoOpeningHours));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商家门店营业时间列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:query')")
//    public CommonResult<List<SpStoreInfoOpeningHoursRespVO>> getStoreInfoOpeningHoursList(@RequestParam("ids") Collection<Long> ids) {
//        List<SpStoreInfoOpeningHoursDO> list = storeInfoOpeningHoursService.getStoreInfoOpeningHoursList(ids);
//        return success(SpStoreInfoOpeningHoursConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家门店营业时间分页")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:query')")
//    public CommonResult<PageResult<SpStoreInfoOpeningHoursRespVO>> getStoreInfoOpeningHoursPage(@Valid SpStoreInfoOpeningHoursPageReqVO pageVO) {
//        PageResult<SpStoreInfoOpeningHoursDO> pageResult = storeInfoOpeningHoursService.getStoreInfoOpeningHoursPage(pageVO);
//        return success(SpStoreInfoOpeningHoursConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家门店营业时间 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-opening-hours:export')")
//    @OperateLog(type = EXPORT)
//    public void exportStoreInfoOpeningHoursExcel(@Valid SpStoreInfoOpeningHoursExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SpStoreInfoOpeningHoursDO> list = storeInfoOpeningHoursService.getStoreInfoOpeningHoursList(exportReqVO);
//        // 导出 Excel
//        List<SpStoreInfoOpeningHoursExcelVO> datas = SpStoreInfoOpeningHoursConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商家门店营业时间.xls", "数据", SpStoreInfoOpeningHoursExcelVO.class, datas);
//    }
//
//}
