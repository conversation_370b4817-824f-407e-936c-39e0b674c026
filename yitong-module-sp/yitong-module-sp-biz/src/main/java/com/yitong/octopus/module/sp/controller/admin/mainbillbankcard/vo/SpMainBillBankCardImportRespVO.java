package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 商家核销账号导入 Response VO")
@Data
@Builder
public class SpMainBillBankCardImportRespVO {

    @Schema(description = "创建成功", required = true)
    private List<String> createList;

    @Schema(description = "更新成功", required = true)
    private List<String> updateList;

    @Schema(description = "导入失败的用户集合,key 为用户名，value 为失败原因", required = true)
    private Map<String, String> failureList;

}
