package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 服务商渠道配置信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainChannelConfigViewReqVO extends SpMainChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "12200")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "审核状态 0 待审核 1 未通过 2 已通过", example = "1")
    private Integer auditStatus;

    @Schema(description = "审批人", example = "6434")
    private String auditUser;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;

    @Schema(description = "渠道商户ID", example = "axddd1233")
    private String channelSpId;


    @Schema(description = "渠道返回结果", example = "{'success:true'}")
    private String channelResult;
}
