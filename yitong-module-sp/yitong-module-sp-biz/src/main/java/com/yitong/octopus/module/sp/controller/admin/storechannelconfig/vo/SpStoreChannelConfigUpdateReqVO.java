package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.*;
import javax.validation.constraints.*;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 服务商门店-渠道配置信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigUpdateReqVO extends SpStoreChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "14253")
    @NotNull(message = "ID不能为空")
    private Long id;

}
