//package com.yitong.octopus.module.sp.controller.admin.storeinfomap;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.storeinfomap.SpStoreInfoMapDO;
//import com.yitong.octopus.module.sp.convert.storeinfomap.SpStoreInfoMapConvert;
//import com.yitong.octopus.module.sp.service.storeinfomap.SpStoreInfoMapService;
//
//@Tag(name = "管理后台 - 商家门店地图信息")
//@RestController
//@RequestMapping("/sp/store-info-map")
//@Validated
//public class SpStoreInfoMapController {
//
//    @Resource
//    private SpStoreInfoMapService storeInfoMapService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家门店地图信息")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:create')")
//    public CommonResult<Long> createStoreInfoMap(@Valid @RequestBody SpStoreInfoMapCreateReqVO createReqVO) {
//        return success(storeInfoMapService.createStoreInfoMap(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家门店地图信息")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:update')")
//    public CommonResult<Boolean> updateStoreInfoMap(@Valid @RequestBody SpStoreInfoMapUpdateReqVO updateReqVO) {
//        storeInfoMapService.updateStoreInfoMap(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家门店地图信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:delete')")
//    public CommonResult<Boolean> deleteStoreInfoMap(@RequestParam("id") Long id) {
//        storeInfoMapService.deleteStoreInfoMap(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家门店地图信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:query')")
//    public CommonResult<SpStoreInfoMapRespVO> getStoreInfoMap(@RequestParam("id") Long id) {
//        SpStoreInfoMapDO storeInfoMap = storeInfoMapService.getStoreInfoMap(id);
//        return success(SpStoreInfoMapConvert.INSTANCE.convert(storeInfoMap));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商家门店地图信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:query')")
//    public CommonResult<List<SpStoreInfoMapRespVO>> getStoreInfoMapList(@RequestParam("ids") Collection<Long> ids) {
//        List<SpStoreInfoMapDO> list = storeInfoMapService.getStoreInfoMapList(ids);
//        return success(SpStoreInfoMapConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家门店地图信息分页")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:query')")
//    public CommonResult<PageResult<SpStoreInfoMapRespVO>> getStoreInfoMapPage(@Valid SpStoreInfoMapPageReqVO pageVO) {
//        PageResult<SpStoreInfoMapDO> pageResult = storeInfoMapService.getStoreInfoMapPage(pageVO);
//        return success(SpStoreInfoMapConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家门店地图信息 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-map:export')")
//    @OperateLog(type = EXPORT)
//    public void exportStoreInfoMapExcel(@Valid SpStoreInfoMapExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SpStoreInfoMapDO> list = storeInfoMapService.getStoreInfoMapList(exportReqVO);
//        // 导出 Excel
//        List<SpStoreInfoMapExcelVO> datas = SpStoreInfoMapConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商家门店地图信息.xls", "数据", SpStoreInfoMapExcelVO.class, datas);
//    }
//
//}
