package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.sp.enums.SpMainInfoMaterialsStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 主体素审核 Request VO")
@Data
@ToString(callSuper = true)
public class SpMainInfoMaterialsAuditReqVO {

    @Schema(description = "id列表", required = true, example = "8821")
    @NotNull(message = "id列表不能为空")
    private List<Long> ids;

    @Schema(description = "状态", example = "2")
    @NotNull(message = "状态不能为空")
    @InEnum(SpMainInfoMaterialsStatusEnum.class)
    private Integer status;

    @Schema(description = "审核意见")
    private String auditMsg;
}
