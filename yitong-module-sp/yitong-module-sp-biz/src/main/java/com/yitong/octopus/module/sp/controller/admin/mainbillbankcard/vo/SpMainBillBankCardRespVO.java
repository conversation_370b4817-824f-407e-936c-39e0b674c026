package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import com.yitong.octopus.module.platform.controller.admin.bank.vo.PlatformBankSampleRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoSimpleRespVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoRespSampleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体结算银行卡 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainBillBankCardRespVO extends SpMainBillBankCardBaseVO {

    @Schema(description = "id", required = true, example = "17403")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "状态", required = true, example = "2")
    private Integer status;

    @Schema(description = "银行信息")
    private PlatformBankSampleRespVO platformBankVo;

    @Schema(description = "服务商商家信息")
    private SpMainInfoSimpleRespVO spMainInfoVo;

    @Schema(description = "门店信息")
    private SpStoreInfoRespSampleVO spStoreInfoVo;

}
