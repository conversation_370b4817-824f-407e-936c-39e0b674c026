package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpGoodsSpuNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainInfoJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainNameJsonSerializer;
import com.yitong.octopus.module.sp.api.utils.jackson.SpStoreNameJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体素材 Response VO")
@Data
@ToString(callSuper = true)
public class SpMainInfoMaterialsRespVO  {

    @Schema(description = "id", required = true, example = "8821")
    private Long id;


    @Schema(description = "商家名称ID", example = "10336")
    @JsonSerialize(using = SpMainNameJsonSerializer.class)
    private Long spId;

//    @Schema(description = "商家名称", example = "王五")
//    private String spName;

    @Schema(description = "门店ID", example = "12719")
    @JsonSerialize(using = SpStoreNameJsonSerializer.class)
    private Long storeId;

//    @Schema(description = "门店名称", example = "王五")
//    private String storeName;

    @Schema(description = "类型", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    private String type;

    @Schema(description = "文件类型", required = true, example = "2")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "审核意见")
    private String auditMsg;

    @Schema(description = "审核人")
    private String auditUser;

    @Schema(description = "文件url", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "文件url不能为空")
    private String fileUrl;

    @Schema(description = "视频封面url", required = true, example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    /**
     * 商品spuId
     */
    @Schema(description = "商品spuId")
    @JsonSerialize(using = SpGoodsSpuNameJsonSerializer.class)
    private Long spuId;

    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    private Long skuId;

//    /**
//     * 商品名称
//     */
//    @Schema(description = "商品名称")
//    private String spuName;

    /**
     * 商品售价
     */
    @Schema(description = "商品售价")
    private Long saleAmount;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;
}
