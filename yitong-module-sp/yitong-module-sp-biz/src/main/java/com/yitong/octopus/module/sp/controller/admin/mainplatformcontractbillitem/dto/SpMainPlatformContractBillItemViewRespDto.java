package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.dto;

import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo.SpMainPlatformContractBillItemBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 主体平台合同结算信息view resp DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemViewRespDto extends SpMainPlatformContractBillItemBaseVO {

    @Schema(description = "id", required = true, example = "4718")
    private Long id;

}
