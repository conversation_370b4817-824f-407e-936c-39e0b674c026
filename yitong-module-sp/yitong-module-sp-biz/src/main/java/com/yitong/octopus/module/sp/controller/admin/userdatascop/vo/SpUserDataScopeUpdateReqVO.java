package com.yitong.octopus.module.sp.controller.admin.userdatascop.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 用户与商家权限数据权限更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpUserDataScopeUpdateReqVO extends SpUserDataScopeBaseVO {

    @Schema(description = "id", required = true, example = "27884")
    @NotNull(message = "id不能为空")
    private Long id;

}
