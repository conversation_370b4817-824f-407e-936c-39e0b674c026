package com.yitong.octopus.module.sp.service.maininfoauditenterprise;

import java.util.*;
import javax.validation.*;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoauditenterprise.SpMainInfoAuditEnterpriseDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 主体认证信息（企业） Service 接口
 *
 * <AUTHOR>
 */
public interface SpMainInfoAuditEnterpriseService {

    /**
     * 创建主体认证信息（企业）
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMainInfoAuditEnterprise(@Valid SpMainInfoAuditEnterpriseCreateReqVO createReqVO);

    /**
     * 更新主体认证信息（企业）
     *
     * @param updateReqVO 更新信息
     */
    void updateMainInfoAuditEnterprise(@Valid SpMainInfoAuditEnterpriseUpdateReqVO updateReqVO);

    /**
     * 删除主体认证信息（企业）
     *
     * @param id 编号
     */
    void deleteMainInfoAuditEnterprise(Long id);


    /**
     * 删除主体认证信息（企业）
     *
     * @param spId 编号
     */
    void deleteMainInfoAuditEnterpriseBySpId(Long spId);

    /**
     * 获得主体认证信息（企业）
     *
     * @param id 编号
     * @return 主体认证信息（企业）
     */
    SpMainInfoAuditEnterpriseDO getMainInfoAuditEnterprise(Long id);

    /**
     * 获得主体认证信息（企业）
     *
     * @param mainId 主体编号
     * @return 主体认证信息（企业）
     */
    SpMainInfoAuditEnterpriseViewRespVO getMainInfoAuditEnterpriseByMainId(Long mainId);

    /**
     * 获得主体认证信息（企业）列表
     *
     * @param ids 编号
     * @return 主体认证信息（企业）列表
     */
    List<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterpriseList(Collection<Long> ids);

    /**
     * 获得主体认证信息（企业）分页
     *
     * @param pageReqVO 分页查询
     * @return 主体认证信息（企业）分页
     */
    PageResult<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterprisePage(SpMainInfoAuditEnterprisePageReqVO pageReqVO);

    /**
     * 获得主体认证信息（企业）列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 主体认证信息（企业）列表
     */
    List<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterpriseList(SpMainInfoAuditEnterpriseExportReqVO exportReqVO);

}
