package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店审核记录创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoAuditLogCreateReqVO extends SpStoreInfoAuditLogBaseVO {

    @Schema(description = "主体Id", example = "10052")
    private Long spId;

    @Schema(description = "主体名称", example = "王五")
    private String spName;

    @Schema(description = "审批人", example = "5765")
    private Long auditUserId;


    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;
}
