package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoauditpersonal.SpMainInfoAuditPersonalDO;
import com.yitong.octopus.module.sp.convert.maininfoauditpersonal.SpMainInfoAuditPersonalConvert;
import com.yitong.octopus.module.sp.service.maininfoauditpersonal.SpMainInfoAuditPersonalService;

@Tag(name = "管理后台 - 主体认证信息-个人")
@RestController
@RequestMapping("/sp/main-info-audit-personal")
@Validated
public class SpMainInfoAuditPersonalController {

    @Resource
    private SpMainInfoAuditPersonalService mainInfoAuditPersonalService;

    @PostMapping("/create")
    @Operation(summary = "创建主体认证信息-个人")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:create')")
    public CommonResult<Long> createMainInfoAuditPersonal(@Valid @RequestBody SpMainInfoAuditPersonalCreateReqVO createReqVO) {
        return success(mainInfoAuditPersonalService.createMainInfoAuditPersonal(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体认证信息-个人")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:update')")
    public CommonResult<Boolean> updateMainInfoAuditPersonal(@Valid @RequestBody SpMainInfoAuditPersonalUpdateReqVO updateReqVO) {
        mainInfoAuditPersonalService.updateMainInfoAuditPersonal(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体认证信息-个人")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:delete')")
    public CommonResult<Boolean> deleteMainInfoAuditPersonal(@RequestParam("id") Long id) {
        mainInfoAuditPersonalService.deleteMainInfoAuditPersonal(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体认证信息-个人")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:query')")
    public CommonResult<SpMainInfoAuditPersonalViewRespVO> getMainInfoAuditPersonal(@RequestParam("id") Long id) {
        SpMainInfoAuditPersonalDO mainInfoAuditPersonal = mainInfoAuditPersonalService.getMainInfoAuditPersonal(id);
        return success(SpMainInfoAuditPersonalConvert.INSTANCE.convertView(mainInfoAuditPersonal));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体认证信息-个人列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:query')")
    public CommonResult<List<SpMainInfoAuditPersonalRespVO>> getMainInfoAuditPersonalList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoAuditPersonalDO> list = mainInfoAuditPersonalService.getMainInfoAuditPersonalList(ids);
        return success(SpMainInfoAuditPersonalConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体认证信息-个人分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:query')")
    public CommonResult<PageResult<SpMainInfoAuditPersonalRespVO>> getMainInfoAuditPersonalPage(@Valid SpMainInfoAuditPersonalPageReqVO pageVO) {
        PageResult<SpMainInfoAuditPersonalDO> pageResult = mainInfoAuditPersonalService.getMainInfoAuditPersonalPage(pageVO);
        return success(SpMainInfoAuditPersonalConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体认证信息-个人 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-personal:export')")
    public void exportMainInfoAuditPersonalExcel(@Valid SpMainInfoAuditPersonalExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoAuditPersonalDO> list = mainInfoAuditPersonalService.getMainInfoAuditPersonalList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoAuditPersonalExcelVO> datas = SpMainInfoAuditPersonalConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体认证信息-个人.xls", "数据", SpMainInfoAuditPersonalExcelVO.class, datas);
    }

}
