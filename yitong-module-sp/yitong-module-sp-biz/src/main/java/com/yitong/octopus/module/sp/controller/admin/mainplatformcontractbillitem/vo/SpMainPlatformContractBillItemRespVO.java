package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体平台合同结算信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemRespVO extends SpMainPlatformContractBillItemBaseVO {

    @Schema(description = "id", required = true, example = "4718")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
