package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.SpMainInfoAqiViewRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.SpMainInfoAuditEnterpriseViewRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo.SpMainInfoAuditPersonalViewRespVO;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo.SpMainPlatformContractViewRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 主体基本信息view Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoViewRespVO extends SpMainInfoBaseVO {

    @Schema(description = "id", required = true, example = "27033")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "所在省id", required = true, example = "7635")
    @NotNull(message = "所在省id不能为空")
    private Integer spProvinceId;

    @Schema(description = "所在市id", required = true, example = "772")
    @NotNull(message = "所在市id不能为空")
    private Integer spCityId;

    @Schema(description = "所在县id", required = true, example = "13418")
    @NotNull(message = "所在县id不能为空")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "6052")
    private Integer spTownId;

    @Schema(description = "联系人手机", required = true)
    @NotNull(message = "联系人手机不能为空")
    private String linkPhone;

    @Schema(description = "联系人电话")
    private String linkTel;

    @Schema(description = "联系人邮箱")
    private String linkEmail;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "个人认证", example = "你说的对")
    private SpMainInfoAuditPersonalViewRespVO auditPersonalVo;

    @Schema(description = "企业认证", example = "你说的对")
    private SpMainInfoAuditEnterpriseViewRespVO auditEnterpriseVo;

    @Schema(description = "主体资质信息", example = "你说的对")
    private SpMainInfoAqiViewRespVO aqiInfoVo;

    @Schema(description = "商标注册证", example = "你说的对")
    private SpMainInfoAqiViewRespVO trcInfoVo;

    @Schema(description = "合同信息", example = "你说的对")
    private SpMainPlatformContractViewRespVO contractVo;

    @Schema(description = "归属服务商/商家", example = "你说的对")
    private SpMainInfoRespVO ownerVo;
}
