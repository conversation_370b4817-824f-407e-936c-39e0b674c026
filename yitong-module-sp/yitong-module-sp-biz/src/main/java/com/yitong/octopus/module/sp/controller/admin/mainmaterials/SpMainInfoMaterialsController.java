package com.yitong.octopus.module.sp.controller.admin.mainmaterials;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoBatchTagRelSaveReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoTagRelSaveReqVO;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainmaterials.SpMainInfoMaterialsDO;
import com.yitong.octopus.module.sp.convert.mainmaterials.SpMainInfoMaterialsConvert;
import com.yitong.octopus.module.sp.service.mainmaterials.SpMainInfoMaterialsService;

@Tag(name = "管理后台 - 主体素材")
@RestController
@RequestMapping("/sp/main-info-materials")
@Validated
public class SpMainInfoMaterialsController {

    @Resource
    private SpMainInfoMaterialsService mainInfoMaterialsService;

    @PostMapping("/create")
    @Operation(summary = "创建主体素材")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:create')")
    public CommonResult<Boolean> createMainInfoMaterials(@Valid @RequestBody SpMainInfoMaterialsCreateReqVO createReqVO) {
        mainInfoMaterialsService.createMainInfoMaterials(createReqVO);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体素材")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:update')")
    public CommonResult<Boolean> updateMainInfoMaterials(@Valid @RequestBody SpMainInfoMaterialsUpdateReqVO updateReqVO) {
        mainInfoMaterialsService.updateMainInfoMaterials(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体素材")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:delete')")
    public CommonResult<Boolean> deleteMainInfoMaterials(@RequestParam("id") Long id) {
        mainInfoMaterialsService.deleteMainInfoMaterials(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体素材")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:query')")
    public CommonResult<SpMainInfoMaterialsRespVO> getMainInfoMaterials(@RequestParam("id") Long id) {
        SpMainInfoMaterialsDO mainInfoMaterials = mainInfoMaterialsService.getMainInfoMaterials(id);
        return success(BeanUtil.toBean(mainInfoMaterials,SpMainInfoMaterialsRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体素材列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:query')")
    public CommonResult<List<SpMainInfoMaterialsRespVO>> getMainInfoMaterialsList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoMaterialsDO> list = mainInfoMaterialsService.getMainInfoMaterialsList(ids);
        return success(SpMainInfoMaterialsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体素材分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:query')")
    public CommonResult<PageResult<SpMainInfoMaterialsRespVO>> getMainInfoMaterialsPage(@Valid SpMainInfoMaterialsPageReqVO pageVO) {
        PageResult<SpMainInfoMaterialsRespVO> pageResult = mainInfoMaterialsService.getMainInfoMaterialsPage(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体素材 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:export')")
    public void exportMainInfoMaterialsExcel(@Valid SpMainInfoMaterialsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoMaterialsRespVO> list = mainInfoMaterialsService.getMainInfoMaterialsList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoMaterialsExcelVO> datas = SpMainInfoMaterialsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体素材.xls", "数据", SpMainInfoMaterialsExcelVO.class, datas);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核主体素材")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:audit')")
    public CommonResult<Boolean> auditMainInfoMaterials(@Valid @RequestBody SpMainInfoMaterialsAuditReqVO auditReqVO) {
        mainInfoMaterialsService.auditMainInfoMaterials(auditReqVO);
        return success(true);
    }

    /**
     *商家素材打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/makeLabel")
    @Operation(summary = "商家素材打标签")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:tags')")
    public CommonResult<Boolean>  makeLabel(@Valid @RequestBody SpMainInfoTagRelSaveReqVO tagRelSaveReqVO) {
        mainInfoMaterialsService.makeLabel(tagRelSaveReqVO);
        return success(true);
    }

    /**
     *商家素材打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/batchMakeLabel")
    @Operation(summary = "商家素材批量打标签")
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:tags')")
    public CommonResult<Boolean>  batchMakeLabel(@Valid @RequestBody SpMainInfoBatchTagRelSaveReqVO tagRelSaveReqVO) {
        mainInfoMaterialsService.batchMakeLabel(tagRelSaveReqVO);
        return success(true);
    }

    @GetMapping("/tag-list")
    @Operation(summary = "获得素材标签列表")
    @Parameter(name = "reqVO", description = "编号列表", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-materials:query')")
    public CommonResult<List<PlatformTagGroupVo>> getTagList(@Valid SpMainMaterialsTagReqVO reqVO) {
        List<PlatformTagGroupVo>list = mainInfoMaterialsService.getTagList(reqVO);
        return success(list);
    }

}
