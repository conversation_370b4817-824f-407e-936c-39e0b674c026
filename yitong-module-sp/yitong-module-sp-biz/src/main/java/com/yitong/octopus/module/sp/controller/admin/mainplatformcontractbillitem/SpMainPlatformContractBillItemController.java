package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainplatformcontractbillitem.SpMainPlatformContractBillItemDO;
import com.yitong.octopus.module.sp.convert.mainplatformcontractbillitem.SpMainPlatformContractBillItemConvert;
import com.yitong.octopus.module.sp.service.mainplatformcontractbillitem.SpMainPlatformContractBillItemService;

@Tag(name = "管理后台 - 主体平台合同结算信息")
@RestController
@RequestMapping("/sp/main-platform-contract-bill-item")
@Validated
public class SpMainPlatformContractBillItemController {

    @Resource
    private SpMainPlatformContractBillItemService mainPlatformContractBillItemService;

    @PostMapping("/create")
    @Operation(summary = "创建主体平台合同结算信息")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:create')")
    public CommonResult<Long> createMainPlatformContractBillItem(@Valid @RequestBody SpMainPlatformContractBillItemCreateReqVO createReqVO) {
        return success(mainPlatformContractBillItemService.createMainPlatformContractBillItem(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体平台合同结算信息")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:update')")
    public CommonResult<Boolean> updateMainPlatformContractBillItem(@Valid @RequestBody SpMainPlatformContractBillItemUpdateReqVO updateReqVO) {
        mainPlatformContractBillItemService.updateMainPlatformContractBillItem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体平台合同结算信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:delete')")
    public CommonResult<Boolean> deleteMainPlatformContractBillItem(@RequestParam("id") Long id) {
        mainPlatformContractBillItemService.deleteMainPlatformContractBillItem(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体平台合同结算信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:query')")
    public CommonResult<SpMainPlatformContractBillItemViewRespVO> getMainPlatformContractBillItem(@RequestParam("id") Long id) {
        SpMainPlatformContractBillItemDO mainPlatformContractBillItem = mainPlatformContractBillItemService.getMainPlatformContractBillItem(id);
        return success(SpMainPlatformContractBillItemConvert.INSTANCE.convertView(mainPlatformContractBillItem));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体平台合同结算信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:query')")
    public CommonResult<List<SpMainPlatformContractBillItemRespVO>> getMainPlatformContractBillItemList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainPlatformContractBillItemDO> list = mainPlatformContractBillItemService.getMainPlatformContractBillItemList(ids);
        return success(SpMainPlatformContractBillItemConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体平台合同结算信息分页")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:query')")
    public CommonResult<PageResult<SpMainPlatformContractBillItemRespVO>> getMainPlatformContractBillItemPage(@Valid SpMainPlatformContractBillItemPageReqVO pageVO) {
        PageResult<SpMainPlatformContractBillItemDO> pageResult = mainPlatformContractBillItemService.getMainPlatformContractBillItemPage(pageVO);
        return success(SpMainPlatformContractBillItemConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体平台合同结算信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract-bill-item:export')")
    public void exportMainPlatformContractBillItemExcel(@Valid SpMainPlatformContractBillItemExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainPlatformContractBillItemDO> list = mainPlatformContractBillItemService.getMainPlatformContractBillItemList(exportReqVO);
        // 导出 Excel
        List<SpMainPlatformContractBillItemExcelVO> datas = SpMainPlatformContractBillItemConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体平台合同结算信息.xls", "数据", SpMainPlatformContractBillItemExcelVO.class, datas);
    }

}
