package com.yitong.octopus.module.sp.service.storeinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.util.MyBatisUtils;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.module.platform.api.tag.PlatformTagApi;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;
import com.yitong.octopus.module.platform.service.brand.PlatformBrandService;
import com.yitong.octopus.module.platform.service.category.PlatformCategoryService;
import com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsPageReqVO;
import com.yitong.octopus.module.sp.api.store.vo.SpStoreLbsRespVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo.SpStoreInfoAqiCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo.SpStoreInfoAqiUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo.SpStoreInfoAuditLogCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo.SpStoreInfoGalleryCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo.SpStoreInfoLabelCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo.SpStoreInfoMapCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo.SpStoreInfoMapUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.vo.SpStoreInfoOpeningHoursCreateReqVO;
import com.yitong.octopus.module.sp.convert.maininfo.SpMainInfoConvert;
import com.yitong.octopus.module.sp.dal.dataobject.maininfo.SpMainInfoDO;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoTagRelDO;
import com.yitong.octopus.module.sp.dal.dto.SpStoreInfoDto;
import com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppSpGoodSpuInfoPageReqVo;
import com.yitong.octopus.module.sp.dal.dto.spgoods.AppXhsMiniAppStoreBySpuPageReqVo;
import com.yitong.octopus.module.sp.dal.dto.spstore.AppXhsMiniAppIndexStoreRespVo;
import com.yitong.octopus.module.sp.enums.AuditTypeEnum;
import com.yitong.octopus.module.sp.enums.SpCommonStatusEnum;
import com.yitong.octopus.module.sp.enums.SpStoreStatusEnum;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.sp.service.storeinfoaqi.SpStoreInfoAqiService;
import com.yitong.octopus.module.sp.service.storeinfoauditlog.SpStoreInfoAuditLogService;
import com.yitong.octopus.module.sp.service.storeinfogallery.SpStoreInfoGalleryService;
import com.yitong.octopus.module.sp.service.storeinfolabel.SpStoreInfoLabelService;
import com.yitong.octopus.module.sp.service.storeinfomap.SpStoreInfoMapService;
import com.yitong.octopus.module.sp.service.storeinfoopeninghours.SpStoreInfoOpeningHoursService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.sp.convert.storeinfo.SpStoreInfoConvert;
import com.yitong.octopus.module.sp.dal.mysql.storeinfo.SpStoreInfoMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.framework.common.util.collection.CollectionUtils.convertMap;
import static com.yitong.octopus.module.platform.enums.ErrorCodeConstants.TAG_SELECT_DELETED;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 商家门店信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpStoreInfoServiceImpl extends ServiceImpl<SpStoreInfoMapper,SpStoreInfoDO> implements SpStoreInfoService {

    @Lazy
    @Resource
    private PlatformBrandService platformBrandService;

    @Lazy
    @Resource
    private PlatformCategoryService platformCategoryService;

    @Lazy
    @Resource
    private SpStoreInfoAqiService spStoreInfoAqiService;

    @Lazy
    @Resource
    private SpStoreInfoGalleryService spStoreInfoGalleryService;

    @Lazy
    @Resource
    private SpStoreInfoLabelService spStoreInfoLabelService;

    @Lazy
    @Resource
    private SpStoreInfoMapService spStoreInfoMapService;

    @Lazy
    @Resource
    private SpMainInfoService spMainInfoService;

    @Lazy
    @Resource
    private SpStoreInfoAuditLogService spStoreInfoAuditLogService;

    @Lazy
    @Resource
    private SpStoreInfoOpeningHoursService spStoreInfoOpeningHoursService;

    @Lazy
    @Resource
    private PlatformTagApi platformTagApi;

    @Lazy
    @Resource
    private SpStoreInfoTagRelService spStoreInfoTagRelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createStoreInfo(SpStoreInfoCreateReqVO reqVO) {
        // 插入
        SpStoreInfoDO storeInfo = BeanUtil.toBean(reqVO,SpStoreInfoDO.class);
        //初始化草稿状态
        storeInfo.setSpStatus(SpCommonStatusEnum.DRAFT.getStatus());
        validateStoreSameExists(storeInfo);
        getBaseMapper().insert(storeInfo);

        //门店地图资质
        SpStoreInfoMapCreateReqVO spStoreInfoMapCreateReqVO = BeanUtil.toBean(reqVO.getSpStoreInfoMapDto(),SpStoreInfoMapCreateReqVO.class);
        spStoreInfoMapCreateReqVO.setStoreId(storeInfo.getId());
        spStoreInfoMapCreateReqVO.setSpId(storeInfo.getSpId());
        spStoreInfoMapService.createStoreInfoMap(spStoreInfoMapCreateReqVO);

        //门店资质
        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoAqiDtoList())){
            List<SpStoreInfoAqiCreateReqVO> spStoreInfoLabelCreateReqVOList = reqVO.getSpStoreInfoAqiDtoList().stream().map(
                    dto -> {
                        SpStoreInfoAqiCreateReqVO vo = BeanUtil.toBean(dto,SpStoreInfoAqiCreateReqVO.class);
                        vo.setStoreId(storeInfo.getId());
                        return vo;
                    }
            ).collect(Collectors.toList());
            spStoreInfoAqiService.createSpStoreInfoAqiDtoWithList(spStoreInfoLabelCreateReqVOList);
        }

        //门店标签
        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoLabelDtoList())){
            List<SpStoreInfoLabelCreateReqVO> spStoreInfoLabelCreateReqVOList = reqVO.getSpStoreInfoLabelDtoList().stream().map(
               dto -> (SpStoreInfoLabelCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoLabelCreateReqVO.class).setStoreId(storeInfo.getId()).setSpId(storeInfo.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoLabelService.createStoreInfoLabels(spStoreInfoLabelCreateReqVOList);
        }

        //门店图片
        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoGalleryDtoList())){
            List<SpStoreInfoGalleryCreateReqVO> storeInfoGalleryRespVOS = reqVO.getSpStoreInfoGalleryDtoList().stream().map(
                    dto -> (SpStoreInfoGalleryCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoGalleryCreateReqVO.class).setStoreId(storeInfo.getId()).setSpId(storeInfo.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoGalleryService.createSpStoreInfoGalleryWithList(storeInfoGalleryRespVOS);
        }

        //门店营业时间
        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoOpeningHoursDtoList())){
            List<SpStoreInfoOpeningHoursCreateReqVO> storeInfoGalleryRespVOS = reqVO.getSpStoreInfoOpeningHoursDtoList().stream().map(
                    dto -> (SpStoreInfoOpeningHoursCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoOpeningHoursCreateReqVO.class).setStoreId(storeInfo.getId()).setSpId(storeInfo.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoOpeningHoursService.createSpStoreInfoOpeningHoursWithList(storeInfoGalleryRespVOS);
        }
        // 返回
        return storeInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStoreInfo(SpStoreInfoUpdateReqVO reqVO) {
        SpStoreInfoDO storeInfoDO = getBaseMapper().selectById(reqVO.getId());
        if (ObjectUtil.isNull(storeInfoDO)) {
            throw exception(STORE_INFO_NOT_EXISTS);
        }

        // 更新
        SpStoreInfoDO updateObj = SpStoreInfoConvert.INSTANCE.convert(reqVO);
        validateStoreSameExists(updateObj);
        getBaseMapper().updateById(updateObj);

        //更新门店地图资质
        SpStoreInfoMapUpdateReqVO mapUpdateReqVO = BeanUtil.toBean(reqVO.getSpStoreInfoMapDto(),SpStoreInfoMapUpdateReqVO.class);
        mapUpdateReqVO.setStoreId(updateObj.getId());
        mapUpdateReqVO.setSpId(updateObj.getSpId());
        spStoreInfoMapService.updateStoreInfoMap(mapUpdateReqVO);

        //门店资质
        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoAqiDtoList())){
            List<SpStoreInfoAqiUpdateReqVO> spStoreInfoAqiUpdateReqVOS = reqVO.getSpStoreInfoAqiDtoList().stream().map(
                dto -> (SpStoreInfoAqiUpdateReqVO)BeanUtil.toBean(dto,SpStoreInfoAqiUpdateReqVO.class).setStoreId(updateObj.getId())
            ).collect(Collectors.toList());
            spStoreInfoAqiService.updateSpStoreInfoAqiDtoWithList(updateObj.getId(),spStoreInfoAqiUpdateReqVOS);
        }

        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoLabelDtoList())){
            //门店标签
            spStoreInfoLabelService.deleteStoreInfoLabelByStoreId(updateObj.getId());
            List<SpStoreInfoLabelCreateReqVO> spStoreInfoLabelCreateReqVOList = reqVO.getSpStoreInfoLabelDtoList().stream().map(
                    dto ->(SpStoreInfoLabelCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoLabelCreateReqVO.class).setStoreId(updateObj.getId()).setSpId(updateObj.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoLabelService.createStoreInfoLabels(spStoreInfoLabelCreateReqVOList);
        }

        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoGalleryDtoList())){
            //门店图片
            spStoreInfoGalleryService.deleteSpStoreInfoGalleryByStoreId(updateObj.getId());
            List<SpStoreInfoGalleryCreateReqVO> storeInfoGalleryRespVOS = reqVO.getSpStoreInfoGalleryDtoList().stream().map(
                    dto -> (SpStoreInfoGalleryCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoGalleryCreateReqVO.class).setStoreId(updateObj.getId()).setSpId(updateObj.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoGalleryService.createSpStoreInfoGalleryWithList(storeInfoGalleryRespVOS);
        }

        if (CollUtil.isNotEmpty(reqVO.getSpStoreInfoOpeningHoursDtoList())){
            //门店营业时间
            spStoreInfoOpeningHoursService.deleteSpStoreInfoOpeningHoursByStoreId(updateObj.getId());
            List<SpStoreInfoOpeningHoursCreateReqVO> storeInfoGalleryRespVOS = reqVO.getSpStoreInfoOpeningHoursDtoList().stream().map(
                    dto -> (SpStoreInfoOpeningHoursCreateReqVO)BeanUtil.toBean(dto,SpStoreInfoOpeningHoursCreateReqVO.class).setStoreId(updateObj.getId()).setSpId(updateObj.getSpId())
            ).collect(Collectors.toList());
            spStoreInfoOpeningHoursService.createSpStoreInfoOpeningHoursWithList(storeInfoGalleryRespVOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditStoreInfo(SpStoreInfoAuditReqVO vo) {
        if (CollectionUtil.isEmpty(vo.getStoreIds())){
            return;
        }
        vo.getStoreIds().forEach(storeId ->{
            SpStoreInfoDO infoDO = getBaseMapper().selectById(storeId);
            if (ObjectUtil.isNull(infoDO)) {
                throw exception(STORE_INFO_NOT_EXISTS);
            }
            //非待审核的无法审批
            if(!SpStoreStatusEnum.PENDING_APPROVAL.getStatus().equals(infoDO.getSpStatus())){
                throw exception(STORE_INFO_NOTE_AUDIT_EXISTS);
            }
            //更新状态
            if (AuditTypeEnum.FAIL.getStatus().equals(vo.getAuditStatus())){
                infoDO.setSpStatus(SpStoreStatusEnum.DRAFT.getStatus());
            }else if (AuditTypeEnum.SUCCESS.getStatus().equals(vo.getAuditStatus())){
                infoDO.setSpStatus(SpStoreStatusEnum.SUCCESS.getStatus());
            }else {
                throw exception(STORE_INFO_NOTE_AUDIT_EXISTS);
            }
            getBaseMapper().updateById(infoDO);

            //记录审核日志
            SpStoreInfoAuditLogCreateReqVO logVo = BeanUtil.toBean(vo,SpStoreInfoAuditLogCreateReqVO.class);
            logVo.setSpId(infoDO.getSpId());
            SpMainInfoDO mainInfoDO = spMainInfoService.getMainInfo(infoDO.getSpId());
            logVo.setSpName(mainInfoDO.getSpName());
            logVo.setAuditUserId(SecurityFrameworkUtils.getLoginUser().getId());
            logVo.setAuditTime(DateTime.now().toLocalDateTime());
            spStoreInfoAuditLogService.createStoreInfoAuditLog(logVo);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStoreInfo(Long id) {
        // 校验存在
        validateStoreInfoExists(id);
        // 删除
        getBaseMapper().deleteById(id);
        spStoreInfoAqiService.deleteStoreInfoAqiByStoreId(id);
        spStoreInfoMapService.deleteStoreInfoMapByStoreId(id);
        spStoreInfoLabelService.deleteStoreInfoLabelByStoreId(id);
        spStoreInfoGalleryService.deleteSpStoreInfoGalleryByStoreId(id);
    }

    private void validateStoreInfoExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(STORE_INFO_NOT_EXISTS);
        }
    }

    @Override
    public SpStoreInfoRespVO getStoreInfo(Long id) {
        SpStoreInfoDO spStoreInfoDO = getBaseMapper().selectById(id);
        SpStoreInfoRespVO viewRespVO = BeanUtil.toBean(spStoreInfoDO,SpStoreInfoRespVO.class);
        viewRespVO.setPlatformBrandRespVO(platformBrandService.getBrandById(spStoreInfoDO.getBrandId()));
        viewRespVO.setPlatformCategoryRespVO(platformCategoryService.getCategoryById(id));
        viewRespVO.setSpMainInfoRespVO(SpMainInfoConvert.INSTANCE.convert(spMainInfoService.getMainInfo(spStoreInfoDO.getSpId())));
        return viewRespVO;
    }

    @Override
    public SpStoreInfoDO getStoreInfoByStoreName(String storeName) {
        return getBaseMapper().selectOne(new LambdaQueryWrapperX<SpStoreInfoDO>().eq(SpStoreInfoDO::getStoreName,storeName));
    }

    @Override
    public SpStoreInfoRespSampleVO getStoreInfoSampleById(Long id) {
        SpStoreInfoDO spStoreInfoDO = getBaseMapper().selectById(id);
        return BeanUtil.toBean(spStoreInfoDO,SpStoreInfoRespSampleVO.class);
    }

    @Override
    public SpStoreInfoViewRespVO getStoreInfoById(Long id) {
        SpStoreInfoDO spStoreInfoDO = getBaseMapper().selectById(id);
        SpStoreInfoViewRespVO viewRespVO = BeanUtil.toBean(spStoreInfoDO,SpStoreInfoViewRespVO.class);
        viewRespVO.setSpStoreInfoAqiDtoList(spStoreInfoAqiService.getStoreInfoAqiByStoreId(id));
        viewRespVO.setSpStoreInfoMapDto(spStoreInfoMapService.getStoreInfoMapByStoreId(id));
        viewRespVO.setSpStoreInfoLabelDtoList(spStoreInfoLabelService.getStoreInfoLabelListByStoreId(id));
        viewRespVO.setSpStoreInfoGalleryDtoList(spStoreInfoGalleryService.getSpStoreInfoGalleryByStoreId(id));
        viewRespVO.setPlatformBrandRespVO(platformBrandService.getBrandById(spStoreInfoDO.getBrandId()));
        viewRespVO.setPlatformCategoryRespVO(platformCategoryService.getCategoryById(id));
        viewRespVO.setSpMainInfoRespVO(SpMainInfoConvert.INSTANCE.convert(spMainInfoService.getMainInfo(spStoreInfoDO.getSpId())));
        viewRespVO.setSpStoreInfoOpeningHoursDtoList(spStoreInfoOpeningHoursService.getSpStoreInfoOpeningHoursByStoreId(id));
        return viewRespVO;
    }

    @Override
    public List<SpStoreInfoDO> getStoreInfoList(Collection<Long> ids) {
        return getBaseMapper().selectBatchIds(ids);
    }

    @Override
    public PageResult<SpStoreInfoViewRespVO> getStoreInfoPage(SpStoreInfoPageReqVO pageReqVO) {
        IPage<SpStoreInfoViewRespVO> page =  getBaseMapper().getStoreInfoList(MyBatisUtils.buildPage(pageReqVO),pageReqVO);
        PageResult<SpStoreInfoViewRespVO> result = new PageResult<>(page.getRecords(), page.getTotal());
        result.getList().forEach(o ->{
            o.setSpStoreInfoAqiDtoList(spStoreInfoAqiService.getStoreInfoAqiByStoreId(o.getId()));
            o.setSpStoreInfoMapDto(spStoreInfoMapService.getStoreInfoMapByStoreId(o.getId()));
            o.setSpStoreInfoLabelDtoList(spStoreInfoLabelService.getStoreInfoLabelListByStoreId(o.getId()));
            o.setSpStoreInfoGalleryDtoList(spStoreInfoGalleryService.getSpStoreInfoGalleryByStoreId(o.getId()));
            o.setPlatformBrandRespVO(platformBrandService.getBrandById(o.getBrandId()));
            o.setPlatformCategoryRespVO(platformCategoryService.getCategoryById(o.getCategoryId()));
        });
        return result;
    }

    @Override
    public List<SpStoreInfoDO> getStoreInfoList(SpStoreInfoPageReqVO exportReqVO) {
        return getBaseMapper().selectList(exportReqVO);
    }

    @Override
    public List<SpStoreInfoDO> getStoreInfoListBySample(SpStoreInfoPageSampleReqVO vo) {
        return getBaseMapper().selectList(new LambdaQueryWrapperX<SpStoreInfoDO>()
                .eqIfPresent(SpStoreInfoDO::getSpId, vo.getSpId())
                .eqIfPresent(SpStoreInfoDO::getSpStatus, vo.getStatus())
                .likeIfPresent(SpStoreInfoDO::getStoreName, vo.getStoreName())
        );
    }

    @Override
    public void validateSpStoreList(Long spId, Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得商家信息
        List<SpStoreInfoDO> posts = getBaseMapper().selectBatchIds(ids);
        Map<Long, SpStoreInfoDO> spStoreMap = convertMap(posts, SpStoreInfoDO::getId);
        // 校验
        ids.forEach(id -> {
            SpStoreInfoDO storeInfoDO = spStoreMap.get(id);
            if (storeInfoDO == null) {
                throw exception(STORE_INFO_NOT_EXISTS);
            }
            if (!storeInfoDO.getSpId().equals(spId)) {
                throw exception(STORE_INFO_SP_NOT_EXISTS);
            }
            if (!SpCommonStatusEnum.SUCCESS.getStatus().equals(storeInfoDO.getSpStatus())) {
                throw exception(STORE_INFO_NOT_ENABLE, storeInfoDO.getStoreName());
            }
        });
    }

    /**
     * 检查门店名称唯一性
     * @param storeDo
     */
    private void validateStoreSameExists(SpStoreInfoDO storeDo) {
        List<SpStoreInfoDO> dbStoreList = getBaseMapper().selectList(new LambdaQueryWrapperX<SpStoreInfoDO>()
                .eq(SpStoreInfoDO::getStoreName,storeDo.getStoreName())
                .neIfPresent(SpStoreInfoDO::getId, storeDo.getId())
        );
        if (CollUtil.isNotEmpty(dbStoreList)) {
            throw exception(STORE_INFO_NAME_EXISTS);
        }
    }

    @Override
    public List<SpStoreInfoDto> getStoreInfoMapList(SpStoreInfoPageReqVO exportReqVO) {
        return getBaseMapper().getStoreInfoMapList(exportReqVO);
    }

    @Override
    public void updateStorePayAccId(Long storeId, Long payAccId) {
        getBaseMapper().updateStorePayAccId(storeId, payAccId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disableStoreInfo(Long id) {
        SpStoreInfoDO dbInfoDo = getBaseMapper().selectById(id);
        if (ObjectUtil.isNull(dbInfoDo)) {
            throw exception(STORE_INFO_NOT_EXISTS);
        }
        if (!SpStoreStatusEnum.SUCCESS.getStatus().equals(dbInfoDo.getSpStatus())){
            throw exception(STORE_INFO_STATUS_NOT_SUCCESS);
        }
        dbInfoDo.setSpStatus(SpStoreStatusEnum.DISABLE.getStatus());
        getBaseMapper().updateById(dbInfoDo);
        getBaseMapper().deleteStoreGoodsSpu(id);
    }

    @Override
    public void enableStoreInfo(Long id) {
        SpStoreInfoDO dbInfoDo = getBaseMapper().selectById(id);
        if (ObjectUtil.isNull(dbInfoDo)) {
            throw exception(STORE_INFO_NOT_EXISTS);
        }
        if (!SpStoreStatusEnum.DISABLE.getStatus().equals(dbInfoDo.getSpStatus())){
            throw exception(STORE_INFO_STATUS_NOT_DISABLE);
        }
        dbInfoDo.setSpStatus(SpStoreStatusEnum.SUCCESS.getStatus());
        getBaseMapper().updateById(dbInfoDo);
    }

    @Override
    public Long getStoreValidCountBySpId(Long spId) {
        return getBaseMapper().selectCount(
                new LambdaQueryWrapperX<SpStoreInfoDO>()
                        .eq(SpStoreInfoDO::getSpId,spId)
                        .eq(SpStoreInfoDO::getSpStatus,SpStoreStatusEnum.SUCCESS.getStatus())
        );
    }

    @Override
    public void auditStoreInfoQual(SpStoreInfoAuditQualReqVO auditReqVO) {
        if (ObjectUtil.isNull(auditReqVO.getMainAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getIndustryAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getPoiAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getClaimStatus())){
            return;
        }
        LambdaUpdateWrapper<SpStoreInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getMainAuditStatus()),SpStoreInfoDO::getMainAuditStatus,auditReqVO.getMainAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getIndustryAuditStatus()),SpStoreInfoDO::getIndustryAuditStatus,auditReqVO.getIndustryAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getPoiAuditStatus()),SpStoreInfoDO::getPoiAuditStatus,auditReqVO.getPoiAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getClaimStatus()),SpStoreInfoDO::getClaimStatus,auditReqVO.getClaimStatus());
        updateWrapper.in(SpStoreInfoDO::getId,auditReqVO.getIds());
        getBaseMapper().update(updateWrapper);
    }

    @Override
    public void makeLabel(SpStoreInfoTagRelSaveReqVO tagRelSaveReqVO) {
        SpStoreInfoDO storeInfo = getBaseMapper().selectById(tagRelSaveReqVO.getId());
        if (ObjectUtil.isNull(storeInfo)){
            throw exception(GOODS_SPU_NOT_EXISTS);
        }
        //移除相关标签
        List<PlatformTagVo> removeTag = tagRelSaveReqVO.getRemoveTag();
        if (CollectionUtil.isNotEmpty(removeTag)){
            spStoreInfoTagRelService.remove(new LambdaQueryWrapperX<SpStoreInfoTagRelDO>()
                    .eq(SpStoreInfoTagRelDO::getStoreId,tagRelSaveReqVO.getId())
                    .in(SpStoreInfoTagRelDO::getTagId,removeTag.stream().map(PlatformTagVo::getId).collect(Collectors.toList()))
            );
        }

        //新增的标签
        List<PlatformTagVo> addTag = tagRelSaveReqVO.getAddTag();
        if (CollectionUtil.isNotEmpty(addTag)){
            List<SpStoreInfoTagRelDO> tags = addTag.stream().map(tag ->{
                //检查标签的有效性
                PlatformTagVo platformTagVo = platformTagApi.getTagById(tag.getId());
                if (ObjectUtil.isNull(platformTagVo)){
                    throw exception(TAG_SELECT_DELETED);
                }
                SpStoreInfoTagRelDO tagRel = new SpStoreInfoTagRelDO();
                tagRel.setTagId(tag.getId());
                tagRel.setStoreId(storeInfo.getId());
                tagRel.setSpId(storeInfo.getSpId());
                return tagRel;
            }).collect(Collectors.toList());
            spStoreInfoTagRelService.saveOrUpdateBatch(tags);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchMakeLabel(SpStoreInfoBatchTagRelSaveReqVO tagRelSaveReqVO) {
        tagRelSaveReqVO.getTags().forEach(tag -> makeLabel(tag));
    }

    @Override
    public List<PlatformTagGroupVo> getTagList(SpStoreInfoTagReqVO reqVO) {
        List<SpStoreInfoTagRelDO> goodsSpuTagList = spStoreInfoTagRelService.getTagRelBySpuId(reqVO.getId());
        if (CollectionUtil.isEmpty(goodsSpuTagList)){
            return ListUtil.empty();
        }
        return platformTagApi.getTagGroupByIds(goodsSpuTagList.stream().map(SpStoreInfoTagRelDO::getTagId).collect(Collectors.toList()),reqVO.getGroupId());
    }

    @Override
    public List<SpStoreInfoDO> getStoreInfoListBySpuId(Long spuId) {
        return getBaseMapper().getStoreInfoListBySpuId(spuId);
    }

    @Override
    public SpStoreInfoDO getSpStoreInfoByStoreId(Long storeId) {
        return getBaseMapper().selectById(storeId);
    }

    @Override
    public List<AppXhsMiniAppIndexStoreRespVo> getSpStoreInfoBySpuId(AppXhsMiniAppSpGoodSpuInfoPageReqVo req) {
        req.setPageSize(PageParam.PAGE_SIZE_NONE);
        IPage<AppXhsMiniAppIndexStoreRespVo> page =  getBaseMapper().getSpStoreInfoBySpuId(MyBatisUtils.buildPage(req),req);
        return page.getRecords();
    }

    @Override
    public PageResult<AppXhsMiniAppIndexStoreRespVo> getSpStoreInfoPageBySpuId(AppXhsMiniAppStoreBySpuPageReqVo req) {
        IPage<AppXhsMiniAppIndexStoreRespVo> page =  getBaseMapper().getSpStoreInfoBySpuId(MyBatisUtils.buildPage(req),req);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<String> getSpStoreCityListBySpId(Long spId) {
        return getBaseMapper().getSpStoreCityListBySpId(spId);
    }

    @Override
    public PageResult<SpStoreLbsRespVO> getSpStorePageBySpId(SpStoreLbsPageReqVO reqVo,Long spId) {
        IPage<SpStoreLbsRespVO> page =  getBaseMapper().getSpStorePageBySpId(MyBatisUtils.buildPage(reqVo),reqVo,spId);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    @Override
    public List<SpStoreLbsRespVO> getSpStoreListHotByStoreIdsAndAppId(SpStoreLbsPageReqVO reqVo, Long appId) {
        return getBaseMapper().getSpStorePageByStoreIdsAndAppId(reqVo,appId);
    }

    @Override
    public List<SpStoreInfoDO> getStoreInfoListBySpId(Long spId) {
        return getBaseMapper().selectList(SpStoreInfoDO::getSpId,spId);
    }

}
