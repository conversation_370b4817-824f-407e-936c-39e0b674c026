package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 门店渠道审核
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpStoreChannelConfigImportExcelAuditVO {

    @ExcelProperty("门店Id")
    private String storeId;

    @ExcelProperty("渠道编码")
    private String channelCode;

    @ExcelProperty("是否开店成功,2:成功,3:失败")
    private Integer status;

    @ExcelProperty("认领失败原因")
    private String resultFailReason;

    @ExcelProperty("小红书POIID")
    private String resultPoiId;

    @ExcelProperty("小红书门店名称")
    private String resultPoiName;

    @ExcelProperty("是否上团购")
    private Boolean resultIsGroup;

}
