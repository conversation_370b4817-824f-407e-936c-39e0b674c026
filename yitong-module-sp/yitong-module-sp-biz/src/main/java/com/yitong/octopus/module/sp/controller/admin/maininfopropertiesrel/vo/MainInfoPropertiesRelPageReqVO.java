//package com.yitong.octopus.module.sp.controller.admin.maininfopropertiesrel.vo;
//
//import lombok.*;
//import io.swagger.v3.oas.annotations.media.Schema;
//import com.yitong.octopus.framework.common.pojo.PageParam;
//import org.springframework.format.annotation.DateTimeFormat;
//import java.time.LocalDateTime;
//
//import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
//
//@Schema(description = "管理后台 - 商家属性关系分页 Request VO")
//@Data
//@EqualsAndHashCode(callSuper = true)
//@ToString(callSuper = true)
//public class MainInfoPropertiesRelPageReqVO extends PageParam {
//
//    @Schema(description = "商家ID", example = "19721")
//    private Long spId;
//
//    @Schema(description = "属性ID", example = "537")
//    private Long propertyId;
//
//    @Schema(description = "属性值ID", example = "5102")
//    private Long valueId;
//
//    @Schema(description = "属性值名称", example = "李四")
//    private String valueName;
//
//    @Schema(description = "创建时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime[] createTime;
//
//}