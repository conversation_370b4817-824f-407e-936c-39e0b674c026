package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商户子账号 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoUserRespVO extends SpMainInfoUserBaseVO {

    @Schema(description = "主键", required = true, example = "19328")
    private Long id;

    @Schema(description = "门店名称", example = "19328")
    private String storeName;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime crtTime;

}
