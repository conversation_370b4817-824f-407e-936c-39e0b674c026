package com.yitong.octopus.module.sp.controller.admin.userdatascop.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户与商家权限数据权限 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpUserDataScopeRespVO extends SpUserDataScopeBaseVO {

    @Schema(description = "id", required = true, example = "27884")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
