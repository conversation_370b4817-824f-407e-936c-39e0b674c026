package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体认证信息（企业）view Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditEnterpriseViewRespVO extends SpMainInfoAuditEnterpriseBaseVO {

    @Schema(description = "ID", required = true, example = "4375")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "法人身份证号")
    private String idCard;

    @Schema(description = "法人身份证照片-人像")
    private String idCardFront;

    @Schema(description = "法人身份证照片-国徽")
    private String idCardBack;

    @Schema(description = "法人身份证证件有效开始时间")
    private Long idCardStartTime;

    @Schema(description = "法人身份证证件有效结束时间")
    private Long idCardEndTime;

    /**
     * 法人身份证所在省id
     */
    @Schema(description = "法人身份证所在省id")
    private Integer idCardProvinceId;
    /**
     * 法人身份证所在市id
     */
    @Schema(description = "法人身份证所在市id")
    private Integer idCardCityId;
    /**
     * 法人身份证所在县id
     */
    @Schema(description = "法人身份证所在县id")
    private Integer idCardCountyId;
    /**
     * 法人身份证所在镇id
     */
    @Schema(description = "法人身份证所在镇id")
    private Integer idCardTownId;
    /**
     * 法人身份证所在省
     */
    @Schema(description = "法人身份证所在省")
    private String idCardProvince;
    /**
     * 法人身份证所在市
     */
    @Schema(description = "法人身份证所在市")
    private String idCardCity;
    /**
     * 法人身份证所在县
     */
    @Schema(description = "法人身份证所在县")
    private String idCardCounty;
    /**
     * 法人身份证所在镇
     */
    @Schema(description = "法人身份证所在镇")
    private String idCardTown;
    /**
     * 营业执照详细地址
     */
    @Schema(description = "营业执照详细地址")
    private String idCardAdd;

    @Schema(description = "营业执照所在省id", example = "12516")
    private Integer licenseProvinceId;

    @Schema(description = "营业执照所在市id", example = "7046")
    private Integer licenseCityId;

    @Schema(description = "营业执照所在县id", example = "26735")
    private Integer licenseCountyId;

    @Schema(description = "营业执照所在镇id", example = "31759")
    private Integer licenseTownId;

    @Schema(description = "营业执照详细地址")
    private String licenseAdd;

    @Schema(description = "成立日期")
    private String establishDate;

    @Schema(description = "营业执照有效期开始")
    private Long licenseStart;

    @Schema(description = "营业执照有效期结束")
    private Long licenseEnd;

    @Schema(description = "法定经营范围")
    private String scope;

    @Schema(description = "银行开户账号")
    private String bankNumber;

    @Schema(description = "开户银行所在省id", example = "20528")
    private Integer bankProvinceId;

    @Schema(description = "开户银行所在市id", example = "10226")
    private Integer bankCityId;

    @Schema(description = "开户银行所在县id", example = "23775")
    private Integer bankCountyId;

    @Schema(description = "开户银行所在镇id", example = "5934")
    private Integer bankTownId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
