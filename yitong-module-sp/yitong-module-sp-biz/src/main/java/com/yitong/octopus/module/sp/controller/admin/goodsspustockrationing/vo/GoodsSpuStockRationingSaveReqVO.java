package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.*;

@Schema(description = "管理后台 - 商品库存供给新增/修改 Request VO")
@Data
public class GoodsSpuStockRationingSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31520")
    private Long id;

    @Schema(description = "商品ID", example = "11530")
    private Long spuId;

    @Schema(description = "总库存数")
    private Integer totalStock;
    /**
     * 所属渠道
     */
    @Schema(description = "所属渠道")
    private String channelCode;

    @Schema(description = "是否动态库存")
    private Boolean isDynamicStock;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

}