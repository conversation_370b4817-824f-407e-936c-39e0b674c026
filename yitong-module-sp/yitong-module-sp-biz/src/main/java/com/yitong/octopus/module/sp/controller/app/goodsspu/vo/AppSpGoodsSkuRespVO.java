package com.yitong.octopus.module.sp.controller.app.goodsspu.vo;

import com.yitong.octopus.module.sp.api.sku.vo.SkuPackageContent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "前端 - 商品sku Response VO")
@Data
@ToString(callSuper = true)
public class AppSpGoodsSkuRespVO {

    @Schema(description = "主键", required = true, example = "25294")
    private Long id;

    private Long spId;
    
    private Long spuId;

    @Schema(description = "套餐内容", required = true)
    @NotNull(message = "套餐内容不能为空")
    @Valid
    private List<SkuPackageContent> packages;

    @Schema(description = "SKU图片")
    private String[] images;

    @Schema(description = "适用人数")
    private Integer userNumLimit;

}
