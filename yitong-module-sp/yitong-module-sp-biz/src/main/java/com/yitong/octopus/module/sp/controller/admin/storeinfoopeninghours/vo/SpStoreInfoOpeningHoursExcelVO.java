package com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商家门店营业时间 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoOpeningHoursExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("门店Id")
    private Long storeId;

    @ExcelProperty("星期")
    private String days;

    @ExcelProperty("开始时间")
    private String startTime;

    @ExcelProperty("结束时间")
    private String endTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
