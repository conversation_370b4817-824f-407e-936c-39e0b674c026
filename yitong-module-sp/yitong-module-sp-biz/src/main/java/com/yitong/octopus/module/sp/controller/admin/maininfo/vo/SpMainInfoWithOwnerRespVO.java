package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 主体基本信息Simple Response VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SpMainInfoWithOwnerRespVO {

    @Schema(description = "服务商/商家Id", required = true, example = "27033")
    private Long id;

    @Schema(description = "服务商名称", required = true, example = "王五")
    private String spName;

    private SpMainInfoWithOwnerRespVO ownerSp;

}
