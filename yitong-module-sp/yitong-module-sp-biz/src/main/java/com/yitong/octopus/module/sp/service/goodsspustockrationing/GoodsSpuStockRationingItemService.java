package com.yitong.octopus.module.sp.service.goodsspustockrationing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品库存供给明细 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsSpuStockRationingItemService extends IService<GoodsSpuStockRationingItemDO> {

    /**
     * 创建商品库存供给明细
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGoodsSpuStockRationingItem(@Valid GoodsSpuStockRationingItemSaveReqVO createReqVO);

    /**
     * 更新商品库存供给明细
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsSpuStockRationingItem(@Valid GoodsSpuStockRationingItemSaveReqVO updateReqVO);

    /**
     * 删除商品库存供给明细
     *
     * @param id 编号
     */
    void deleteGoodsSpuStockRationingItem(Long id);

    /**
     * 获得商品库存供给明细
     *
     * @param id 编号
     * @return 商品库存供给明细
     */
    GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItem(Long id);

    /**
     * 获得商品库存供给明细分页
     *
     * @param pageReqVO 分页查询
     * @return 商品库存供给明细分页
     */
    PageResult<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemPage(GoodsSpuStockRationingItemPageReqVO pageReqVO);


    /**
     * 获得商品库存供给明细【当前且待生效】
     *
     * @param rationingId 供给计划Id
     * @return 商品库存供给明细
     */
    GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItemCurrentByRationingId(Long rationingId);

    /**
     * 获得商品库存供给明细【之前且生效中】
     *
     * @param rationingId 供给计划Id
     * @return 商品库存供给明细
     */
    GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItemBeforeByRationingId(Long rationingId);

    /**
     * 获得商品库存供给总数【当前时间之前】
     *
     * @param rationingId 编号
     * @return 商品库存供给明细
     */
    Long getGoodsSpuStockRationingBeforeItemByRationingId(Long rationingId);

    /**
     * 获取商品配给项
     * @return
     */
    List<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemList();


    /**
     * 获取商品配给项
     * @return
     */
    List<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemListByRationingId(Long rationingId);


}