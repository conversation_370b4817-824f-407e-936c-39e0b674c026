package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo;

import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 商家资质信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoAqiExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("商家门店ID")
    private Long storeId;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "资质类型", converter = DictConvert.class)
    @DictFormat("sp_store_info_aqi_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer aqiType;

    @ExcelProperty("资质编号")
    private String aqiNumber;

    @ExcelProperty(value = "资质有效期是否长期有效 0 否，1 是", converter = DictConvert.class)
    @DictFormat("sp_store_info_aqi_is_long") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer aqiIsLong;

    @ExcelProperty("资质有效期开始")
    private Long aqiStart;

    @ExcelProperty("资质有效期结束")
    private Long aqiEnd;

    @ExcelProperty("资质电子版")
    private String aqiImg1;

    @ExcelProperty("资质电子版2")
    private String aqiImg2;

    @ExcelProperty("资质发证机构")
    private String aqiOrg;

    @ExcelProperty(value = "资质状态", converter = DictConvert.class)
    @DictFormat("sp_store_info_aqi_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer aqiStatus;

}
