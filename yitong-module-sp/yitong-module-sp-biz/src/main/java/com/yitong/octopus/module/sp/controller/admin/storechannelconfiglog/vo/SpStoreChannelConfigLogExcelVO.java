package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 商家门店渠道配置信息表日志 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreChannelConfigLogExcelVO {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("配置ID")
    private Long configId;

    @ExcelProperty("门店ID")
    private Long storeId;

    @ExcelProperty("平台渠道ID")
    private Long channelId;

    @ExcelProperty("配置信息值")
    private String configValue;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("sp_store_aduit_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer auditStatus;

    @ExcelProperty("审批人")
    private Long auditUserId;

    @ExcelProperty("审批备注")
    private String auditReason;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
