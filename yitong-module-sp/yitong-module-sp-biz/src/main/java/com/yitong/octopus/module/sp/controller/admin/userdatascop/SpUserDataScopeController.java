package com.yitong.octopus.module.sp.controller.admin.userdatascop;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.HttpServletResponse;
import javax.validation.*;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.module.sp.controller.admin.userdatascop.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.userdatascop.SpUserDataScopeDO;
import com.yitong.octopus.module.sp.convert.userdatascop.SpUserDataScopConvert;
import com.yitong.octopus.module.sp.service.userdatascop.SpUserDataScopeService;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

@Tag(name = "管理后台 - 用户与商家权限数据权限")
@RestController
@RequestMapping("/sp/user-data-scope")
@Validated
public class SpUserDataScopeController {

    @Resource
    private SpUserDataScopeService spUserDataScopeService;

    @PostMapping("/create")
    @Operation(summary = "创建用户与商家权限数据权限")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scopee:create')")
    public CommonResult<Long> createUserDataScope(@Valid @RequestBody SpUserDataScopeCreateReqVO createReqVO) {
        return success(spUserDataScopeService.createUserDataScope(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户与商家权限数据权限")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scopee:update')")
    public CommonResult<Boolean> updateUserDataScope(@Valid @RequestBody SpUserDataScopeUpdateReqVO updateReqVO) {
        spUserDataScopeService.updateUserDataScope(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户与商家权限数据权限")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:user-data-scopee:delete')")
    public CommonResult<Boolean> deleteUserDataScope(@RequestParam("id") Long id) {
        spUserDataScopeService.deleteUserDataScope(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户与商家权限数据权限")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scopee:query')")
    public CommonResult<SpUserDataScopeRespVO> getUserDataScope(@RequestParam("id") Long id) {
        SpUserDataScopeDO userDataScope = spUserDataScopeService.getUserDataScope(id);
        return success(SpUserDataScopConvert.INSTANCE.convert(userDataScope));
    }


    @GetMapping("/list")
    @Operation(summary = "获得用户与商家权限数据权限列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scope:query')")
    public CommonResult<List<SpUserDataScopeRespVO>> getUserDataScopList(@RequestParam("ids") Collection<Long> ids) {
        List<SpUserDataScopeDO> list = spUserDataScopeService.getUserDataScopeList(ids);
        return success(SpUserDataScopConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户与商家权限数据权限分页")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scope:query')")
    public CommonResult<PageResult<SpUserDataScopeRespVO>> getUserDataScopPage(@Valid SpUserDataScopePageReqVO pageVO) {
        PageResult<SpUserDataScopeDO> pageResult = spUserDataScopeService.getUserDataScopePage(pageVO);
        return success(SpUserDataScopConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户与商家权限数据权限 Excel")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scope:export')")
    public void exportUserDataScopeExcel(@Valid SpUserDataScopeExportReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<SpUserDataScopeDO> list = spUserDataScopeService.getUserDataScopeList(exportReqVO);
        // 导出 Excel
        List<SpUserDataScopExcelVO> datas = SpUserDataScopConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "用户与商家权限数据权限.xls", "数据", SpUserDataScopExcelVO.class, datas);
    }

    @Operation(summary = "获得管理员拥有的角色编号列表")
    @Parameter(name = "userId", description = "用户编号", required = true)
    @GetMapping("/get-user-user-sp-data-scope")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scope:assign-user-sp-data-scope')")
    public CommonResult<SpUserDataScopeRespVO> listAdminRoles(@RequestParam("userId") Long userId) {
        SpUserDataScopeDO spUserDataScopeDO = spUserDataScopeService.getUserDataScopeByUserId(userId);
        return success(SpUserDataScopConvert.INSTANCE.convert(spUserDataScopeDO));
    }

    @PostMapping("/assign-sp-data-scope")
    @Operation(summary = "赋予角色数据权限")
    @PreAuthorize("@ss.hasPermission('sp:user-data-scope:assign-user-sp-data-scope')")
    public CommonResult<Boolean> assignRoleDataScope(@Valid @RequestBody PermissionAssignSpDataScopeReqVO reqVO) {
        spUserDataScopeService.assignUserSpDataScope(reqVO);
        return success(true);
    }

}
