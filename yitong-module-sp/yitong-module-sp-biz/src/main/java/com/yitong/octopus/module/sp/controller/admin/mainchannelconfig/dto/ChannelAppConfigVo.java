package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 服务商/商家渠道配置信息
 * <AUTHOR>
 */
@Data
public class ChannelAppConfigVo {

    /**
     * id
     */
    @Schema(description = "平台渠道Id")
    private String channelId;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 渠道编码
     */
    @Schema(description = "渠道编码")
    private String channelCode;

    /**
     * appId
     */
    @Schema(description = "应用名称")
    private Long appId;

    /**
     * appId
     */
    @Schema(description = "应用名称")
    private String appName;

    /**
     * 商家/服务商ID
     */
    @Schema(description = "商家/服务商ID")
    private String spId;
    /**
     * 渠道类目
     */
    @Schema(description = "渠道类目")
    private String categoryId;

    /**
     * 渠道类目
     */
    @Schema(description = "渠道类目名称")
    private String categoryName;

    /**
     * 开通的渠道Id
     */
    @Schema(description = "开通的渠道Id,若没有则表示未开通")
    private String id;

    /**
     * 渠道店铺ID
     */
    @Schema(description = "渠道店铺ID")
    private String channelSpId;

    /**
     * 渠道返回结果-渠道审核成功之后
     */
    private String channelResult;

    /**
     * 审核状态 0 待审核 1 未通过 2 已通过
     */
    private Integer auditStatus;

    /**
     * 审批备注
     */
    private String auditReason;

    /**
     * 审批时间
     */
    private LocalDateTime auditTime;

    /**
     * 审批人
     */
    private String auditUser;

}
