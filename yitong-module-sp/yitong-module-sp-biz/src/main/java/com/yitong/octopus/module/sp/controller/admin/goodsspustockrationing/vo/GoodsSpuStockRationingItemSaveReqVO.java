package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品库存供给明细新增/修改 Request VO")
@Data
public class GoodsSpuStockRationingItemSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17931")
    private Long id;

    @Schema(description = "商品供给ID", example = "22759")
    private Long rationingId;

//    @Schema(description = "商品ID", example = "6550")
//    private Long spuId;

    @Schema(description = "所属天")
    private LocalDateTime day;

    @Schema(description = "状态", example = "2")
    private Boolean status;

    @Schema(description = "预计总库存数")
    private Integer totalStock;

//    @Schema(description = "所属天售真是库存数")
//    private Integer realTotalStock;
//
//    @Schema(description = "当天售卖数")
//    private Integer saleTotal;

}