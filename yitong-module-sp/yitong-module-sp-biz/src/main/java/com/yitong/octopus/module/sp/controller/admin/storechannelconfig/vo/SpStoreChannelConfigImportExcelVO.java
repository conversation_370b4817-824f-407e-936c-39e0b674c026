package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import com.yitong.octopus.module.sp.enums.DictTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 商家核销账号 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpStoreChannelConfigImportExcelVO {

    @ExcelProperty("门店Id")
    private String storeId;

    @ExcelProperty("渠道编码")
    private String channelCode;

}
