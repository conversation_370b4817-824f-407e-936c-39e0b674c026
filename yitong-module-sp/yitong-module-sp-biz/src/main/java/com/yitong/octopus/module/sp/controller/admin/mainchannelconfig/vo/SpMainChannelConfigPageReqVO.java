package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 渠道分页
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 服务商渠道配置信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainChannelConfigPageReqVO extends PageParam {

    @Schema(description = "审核状态 0 待审核 1 未通过 2 已通过", example = "1")
    private Integer auditStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
