package com.yitong.octopus.module.sp.service.goodsspustockrationing;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingItemDO;
import com.yitong.octopus.module.sp.dal.dto.GoodsSpuStockRationingDto;
import com.yitong.octopus.module.sp.enums.SpStockRationingStatusEnum;
import com.yitong.octopus.module.trade.api.coupon.TradeCouponApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import com.yitong.octopus.module.channel.enums.ChannelCodeEnum;
import java.time.LocalDateTime;
import java.util.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.sp.dal.mysql.goodsspustockrationing.GoodsSpuStockRationingMapper;

import javax.annotation.Resource;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 商品库存供给 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class GoodsSpuStockRationingServiceImpl extends ServiceImpl<GoodsSpuStockRationingMapper,GoodsSpuStockRationingDO>  implements GoodsSpuStockRationingService {

    @Resource
    private TradeCouponApi tradeCouponApi;

    @Resource
    private GoodsSpuStockRationingItemService goodsSpuStockRationingItemService;

    @Override
    public Long createGoodsSpuStockRationing(GoodsSpuStockRationingSaveReqVO createReqVO) {
        // 插入
        GoodsSpuStockRationingDO goodsSpuStockRationing = BeanUtils.toBean(createReqVO, GoodsSpuStockRationingDO.class);
        if (StringUtils.isEmpty(goodsSpuStockRationing.getChannelCode())){
            goodsSpuStockRationing.setChannelCode(ChannelCodeEnum.XHS.getCode());
        }
        LocalDateTime now = LocalDateTime.now();
        if (createReqVO.getEndTime().isBefore(now)
                || createReqVO.getEndTime().isBefore(createReqVO.getStartTime())
        ){
            throw exception(GOODS_SPU_STOCK_RATIONING_DATE_NOT_VALID);
        }
        if (createReqVO.getStartTime().isBefore(now)){
            goodsSpuStockRationing.setStatus(SpStockRationingStatusEnum.TO_BE_EFFECTIVE.getStatus());
        }else {
            goodsSpuStockRationing.setStatus(SpStockRationingStatusEnum.TAKING_EFFECT.getStatus());
        }
        getBaseMapper().insert(goodsSpuStockRationing);
        // 返回
        return goodsSpuStockRationing.getId();
    }

    @Override
    public void updateGoodsSpuStockRationing(GoodsSpuStockRationingSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsSpuStockRationingExists(updateReqVO.getId());
        // 更新
        GoodsSpuStockRationingDO updateObj = BeanUtils.toBean(updateReqVO, GoodsSpuStockRationingDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteGoodsSpuStockRationing(Long id) {
        // 校验存在
        validateGoodsSpuStockRationingExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateGoodsSpuStockRationingExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(GOODS_SPU_STOCK_RATIONING_NOT_EXISTS);
        }
    }

    @Override
    public GoodsSpuStockRationingDO getGoodsSpuStockRationing(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<GoodsSpuStockRationingDO> getGoodsSpuStockRationingPage(GoodsSpuStockRationingPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public List<GoodsSpuStockRationingDto> getGoodsSpuStockRationingByCurrent() {
        return getBaseMapper().getGoodsSpuStockRationingByCurrent();
    }

    @Override
    public List<GoodsSpuStockRationingDO> getGoodsSpuDynamicStockRationingByCurrent() {
        LocalDateTime now = LocalDateTime.now();
        return getBaseMapper().selectList(new LambdaQueryWrapperX<GoodsSpuStockRationingDO>()
                .lt(GoodsSpuStockRationingDO::getStartTime,now)
                .gt(GoodsSpuStockRationingDO::getEndTime,now)
//                .eq(GoodsSpuStockRationingDO::getIsDynamicStock,true)
                .eq(GoodsSpuStockRationingDO::getStatus,true));
    }

    @Override
    public int changeGoodsSpuStockRationingStatus() {
        int result =  getBaseMapper().openGoodsSpuStockRation();
        result += getBaseMapper().endGoodsSpuStockRation();
        return result;
    }

    @Override
    public void initDynamicStock() {
        // 查询配给计划
        List<GoodsSpuStockRationingDO> spuStockRationingList = getGoodsSpuDynamicStockRationingByCurrent();
        if (CollectionUtil.isEmpty(spuStockRationingList)) {
            log.warn("GoodsDynamicStockAutoJob empty");
        }
        log.info("GoodsDynamicStockAutoJob size:{}", spuStockRationingList.size());
        for (GoodsSpuStockRationingDO goodsSpuStock : spuStockRationingList) {
            try {
                // 根据配给计划，查询当前时间的且状态为“生效中”的计划
                GoodsSpuStockRationingItemDO currentStockRationingItem = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItemCurrentByRationingId(goodsSpuStock.getId());
                if (ObjectUtil.isNull(currentStockRationingItem)) {
                    // 当前没有计划则继续。
                    continue;
                }
                // 获取配给计划设置的库存数
                Long totalStock = currentStockRationingItem.getTotalStock();
                // 动态库存进行计算
                if (goodsSpuStock.getIsDynamicStock()) {
                    //获取统计时间范围
                    LocalDateTime start = LocalDateTimeUtils.initDayStart(goodsSpuStock.getStartTime());
                    // 当前时间往前一天
//                    LocalDateTime end = LocalDateTimeUtils.initDayEnd(LocalDateTime.now().plusDays(-1));
                    LocalDateTime end = LocalDateTime.now();
                    if (start.isBefore(end)) {
                        //统计在周期内的不包含当天的销量情况
                        Long saleTotal = tradeCouponApi.getSaleTotalBySpuIdAndDateRange(goodsSpuStock.getSpuId(), start, end);
                        //获取当前时间之前的计划数
                        Long preTotalStock = goodsSpuStockRationingItemService.getGoodsSpuStockRationingBeforeItemByRationingId(goodsSpuStock.getId());
                        if (preTotalStock > saleTotal) {
                            totalStock = totalStock + preTotalStock - saleTotal;
                        }
                        // 校验设置库存数 + 已销售数 是否大于 总库存数
                        if (totalStock + saleTotal > goodsSpuStock.getTotalStock()) {
                            // 减小到可用库存范围内
                            totalStock = totalStock - (totalStock + saleTotal - goodsSpuStock.getTotalStock());
                        }
                        // 如果总库存已经消耗完毕,则不执行该条设置。【即：库存配给项，状态设置为已完成】
                        if (totalStock <= 0) {
                            currentStockRationingItem.setStatus(SpStockRationingStatusEnum.FINISH.getStatus());
                        }
                        // 更新剩余库存总数
                        goodsSpuStock.setRemainingStock(goodsSpuStock.getTotalStock() - saleTotal);
                        updateById(goodsSpuStock);
                    }
                    //处理之前的生效的记录，若有，则更新销售总数和状态为已完成。
                    GoodsSpuStockRationingItemDO beforeGoodsSpuStock = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItemBeforeByRationingId(goodsSpuStock.getId());
                    if (ObjectUtil.isNotNull(beforeGoodsSpuStock)) {
                        beforeGoodsSpuStock.setStatus(SpStockRationingStatusEnum.FINISH.getStatus());
                        //获取销量
                        //统计在周期内的不包含当天的销量情况
                        Long saleTotal = tradeCouponApi.getSaleTotalBySpuIdAndDateRange(goodsSpuStock.getSpuId(), LocalDateTimeUtils.initDayStart(beforeGoodsSpuStock.getDay()), end);
                        //统计这段时间的销量
                        beforeGoodsSpuStock.setSaleTotal(saleTotal);
                        goodsSpuStockRationingItemService.updateById(beforeGoodsSpuStock);
                    }
                }
                //设置状态为生效中
                currentStockRationingItem.setStatus(SpStockRationingStatusEnum.TAKING_EFFECT.getStatus());
                // 设置真实库存总数
                currentStockRationingItem.setRealTotalStock(totalStock);
                // 更新库存
                goodsSpuStockRationingItemService.updateById(currentStockRationingItem);
            } catch (Exception e) {
                log.error("GoodsSpuStockRationing syncStock error:{}", JSONUtil.toJsonStr(goodsSpuStock));
            }
        }
    }

}