package com.yitong.octopus.module.sp.controller.admin.industry;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.platform.dal.dataobject.category.PlatformCategoryDO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.sp.controller.admin.industry.vo.IndustryPageReqVO;
import com.yitong.octopus.module.sp.controller.admin.industry.vo.IndustryRespVO;
import com.yitong.octopus.module.sp.controller.admin.industry.vo.IndustryTreeReqVO;
import com.yitong.octopus.module.sp.controller.admin.industry.vo.IndustryTreeRespVO;
import com.yitong.octopus.module.sp.convert.idustry.IndustryConvert;
import com.yitong.octopus.module.sp.dal.dataobject.industry.IndustryDO;
import com.yitong.octopus.module.sp.dal.mysql.industry.IndustryMapper;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import static com.yitong.octopus.framework.common.constant.TreeConstants.TREE_ROOT_ID;
import static com.yitong.octopus.framework.common.constant.TreeConstants.TREE_ROOT_NAME;

@Tag(name = "管理后台 - 行业")
@RestController
@RequestMapping("/sp/industry")
@Validated
public class IndustryController {

	@Resource
	private IndustryMapper industryMapper;

	@GetMapping("/get")
	@Operation(summary = "获得行业")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
//	@PreAuthorize("@ss.hasPermission('sp:industry:query')")
	@PreAuthenticated
	public CommonResult<IndustryRespVO> get(@RequestParam("id") Long id) {
		IndustryDO industry = industryMapper.selectById(id);
		IndustryRespVO rspVo = IndustryConvert.INSTANCE.convertResp(industry);
		return CommonResult.success(rspVo);
	}

	@GetMapping("/list")
	@Operation(summary = "获得行业列表")
	@Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//	@PreAuthorize("@ss.hasPermission('sp:industry:query')")
	@PreAuthenticated
	public CommonResult<List<IndustryRespVO>> get(@RequestParam("ids") List<Long> ids) {
		List<IndustryDO> list = industryMapper.selectListByIds(ids);
		List<IndustryRespVO> result = IndustryConvert.INSTANCE.convertResp(list);
		return CommonResult.success(result);
	}

	@GetMapping("/page")
	@Operation(summary = "获得行业分页")
//	@PreAuthorize("@ss.hasPermission('sp:industry:query')")
	@PreAuthenticated
	public CommonResult<PageResult<IndustryRespVO>> getGoodsSkuPage(@Valid IndustryPageReqVO pageVO) {
		PageResult<IndustryDO> page = industryMapper.selectPage(pageVO,
				new LambdaQueryWrapperX<IndustryDO>().eqIfPresent(IndustryDO::getParentId, pageVO.getParentId()));
		PageResult<IndustryRespVO> result = IndustryConvert.INSTANCE.convertResp(page);
		return CommonResult.success(result);
	}

	@GetMapping("/tree")
	@Operation(summary = "获得行业层级结构")
	@PreAuthenticated
	public CommonResult<Tree<String>> getTree(@Valid IndustryTreeReqVO vo) {
		List<IndustryDO> list = industryMapper.selectListByParentId(vo.getId());
		//转换成树形
		List<TreeNode<String>> treeNodeList = list.stream().map(
				o ->new TreeNode<String>().setId(String.valueOf(o.getId()))
						.setParentId(String.valueOf(o.getParentId()))
						.setName(o.getName())
		).collect(Collectors.toList());
		Tree<String> tree = TreeUtil.buildSingle(treeNodeList,TREE_ROOT_ID);
		if (tree.getId().equals(TREE_ROOT_ID)){
			tree.setName(TREE_ROOT_NAME);
		}
		return CommonResult.success(tree);

//		Iterator<Long> iterator = map.keySet().iterator();
//		while (iterator.hasNext()) {
//			Long id = iterator.next();
//			IndustryTreeRespVO rspVo = map.get(id);
//			if (rspVo.getParentId() != null) {
//				IndustryTreeRespVO parentVo = map.get(rspVo.getParentId());
//				if (parentVo != null) {
//					parentVo.getChildren().add(rspVo);
//					iterator.remove();
//				}
//			}
//		}
//		return CommonResult.success(new ArrayList<>(map.values()));
	}
}
