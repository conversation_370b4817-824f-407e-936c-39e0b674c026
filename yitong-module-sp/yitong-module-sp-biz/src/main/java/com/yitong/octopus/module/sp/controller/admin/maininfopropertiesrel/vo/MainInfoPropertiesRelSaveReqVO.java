//package com.yitong.octopus.module.sp.controller.admin.maininfopropertiesrel.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.*;
//
//@Schema(description = "管理后台 - 商家属性关系新增/修改 Request VO")
//@Data
//public class MainInfoPropertiesRelSaveReqVO {
//
//    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23868")
//    private Long id;
//
//    @Schema(description = "商家ID", example = "19721")
//    private Long spId;
//
//    @Schema(description = "属性ID", example = "537")
//    private Long propertyId;
//
//    @Schema(description = "属性值ID", example = "5102")
//    private Long valueId;
//
//    @Schema(description = "属性值名称", example = "李四")
//    private String valueName;
//
//}