//package com.yitong.octopus.module.sp.controller.admin.storeinfogallery;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.storeinfogallery.SpStoreInfoGalleryDO;
//import com.yitong.octopus.module.sp.convert.storeinfogallery.SpStoreInfoGalleryConvert;
//import com.yitong.octopus.module.sp.service.storeinfogallery.SpStoreInfoGalleryService;
//
//@Tag(name = "管理后台 - 商家门店图片")
//@RestController
//@RequestMapping("/sp/store-info-gallery")
//@Validated
//public class SpStoreInfoGalleryController {
//
//    @Resource
//    private SpStoreInfoGalleryService storeInfoGalleryService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家门店图片")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:create')")
//    public CommonResult<Long> createStoreInfoGallery(@Valid @RequestBody SpStoreInfoGalleryCreateReqVO createReqVO) {
//        return success(storeInfoGalleryService.createStoreInfoGallery(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家门店图片")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:update')")
//    public CommonResult<Boolean> updateStoreInfoGallery(@Valid @RequestBody SpStoreInfoGalleryUpdateReqVO updateReqVO) {
//        storeInfoGalleryService.updateStoreInfoGallery(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家门店图片")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:delete')")
//    public CommonResult<Boolean> deleteStoreInfoGallery(@RequestParam("id") Long id) {
//        storeInfoGalleryService.deleteStoreInfoGallery(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家门店图片")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:query')")
//    public CommonResult<SpStoreInfoGalleryRespVO> getStoreInfoGallery(@RequestParam("id") Long id) {
//        SpStoreInfoGalleryDO storeInfoGallery = storeInfoGalleryService.getStoreInfoGallery(id);
//        return success(SpStoreInfoGalleryConvert.INSTANCE.convert(storeInfoGallery));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商家门店图片列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:query')")
//    public CommonResult<List<SpStoreInfoGalleryRespVO>> getStoreInfoGalleryList(@RequestParam("ids") Collection<Long> ids) {
//        List<SpStoreInfoGalleryDO> list = storeInfoGalleryService.getStoreInfoGalleryList(ids);
//        return success(SpStoreInfoGalleryConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家门店图片分页")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:query')")
//    public CommonResult<PageResult<SpStoreInfoGalleryRespVO>> getStoreInfoGalleryPage(@Valid SpStoreInfoGalleryPageReqVO pageVO) {
//        PageResult<SpStoreInfoGalleryDO> pageResult = storeInfoGalleryService.getStoreInfoGalleryPage(pageVO);
//        return success(SpStoreInfoGalleryConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家门店图片 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-gallery:export')")
//    @OperateLog(type = EXPORT)
//    public void exportStoreInfoGalleryExcel(@Valid SpStoreInfoGalleryExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SpStoreInfoGalleryDO> list = storeInfoGalleryService.getStoreInfoGalleryList(exportReqVO);
//        // 导出 Excel
//        List<SpStoreInfoGalleryExcelVO> datas = SpStoreInfoGalleryConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商家门店图片.xls", "数据", SpStoreInfoGalleryExcelVO.class, datas);
//    }
//
//}
