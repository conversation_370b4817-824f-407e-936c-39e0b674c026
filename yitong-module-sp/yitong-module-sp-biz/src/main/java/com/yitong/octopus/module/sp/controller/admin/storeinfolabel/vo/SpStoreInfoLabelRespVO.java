package com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商家门店标签关联 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoLabelRespVO extends SpStoreInfoLabelBaseVO {

    @Schema(description = "id", required = true, example = "30013")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
