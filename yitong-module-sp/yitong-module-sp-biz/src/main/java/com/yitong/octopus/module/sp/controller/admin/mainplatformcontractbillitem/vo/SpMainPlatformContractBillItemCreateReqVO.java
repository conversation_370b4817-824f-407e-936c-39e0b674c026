package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体平台合同结算信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemCreateReqVO extends SpMainPlatformContractBillItemBaseVO {

}
