package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelAppConfigVo;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelConfigPageVO;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto.ChannelConfigVo;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.xhsminiapp.SpMainXhsMiniAppChannelConfigRespVO;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.xhsminiapp.SpMainXhsMiniAppChannelConfigSaveReqVO;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.xhsminiapp.SpMainXhsMiniAppChannelConfigUpdateReqVO;
import com.yitong.octopus.module.sp.service.mainchannelconfig.SpMainXhsMiniAppChannelConfigService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainchannelconfig.SpMainChannelConfigDO;
import com.yitong.octopus.module.sp.convert.channelconfig.SpChannelConfigConvert;
import com.yitong.octopus.module.sp.service.mainchannelconfig.SpMainChannelConfigService;

@Tag(name = "管理后台 - 服务商/商家渠道配置信息")
@RestController
@RequestMapping("/sp/main-channel-config")
@Validated
public class SpMainChannelConfigController {

    @Resource
    private SpMainChannelConfigService channelConfigService;
    @Resource
    private SpMainXhsMiniAppChannelConfigService xhsMiniAppChannelConfigService;


    @PostMapping("/create")
    @Operation(summary = "创建服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:create')")
    public CommonResult<Long> createChannelConfig(@Valid @RequestBody SpMainChannelConfigCreateReqVO createReqVO) {
        return success(channelConfigService.createOrUpdateChannelConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:update')")
    public CommonResult<Boolean> updateChannelConfig(@Valid @RequestBody SpMainChannelConfigUpdateReqVO updateReqVO) {
        channelConfigService.updateChannelConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务商渠道配置信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:delete')")
    public CommonResult<Boolean> deleteChannelConfig(@RequestParam("id") Long id) {
        channelConfigService.deleteChannelConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务商渠道配置信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<SpMainChannelConfigViewReqVO> getChannelConfig(@RequestParam("id") Long id) {
        SpMainChannelConfigDO channelConfig = channelConfigService.getChannelConfig(id);
        return success(SpChannelConfigConvert.INSTANCE.convert(channelConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得服务商渠道配置信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<List<SpMainChannelConfigRespVO>> getChannelConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainChannelConfigDO> list = channelConfigService.getChannelConfigList(ids);
        return success(SpChannelConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务商渠道配置信息分页")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<PageResult<SpMainChannelConfigViewReqVO>> getChannelConfigPage(@Valid SpMainChannelConfigPageReqVO pageVO) {
        PageResult<SpMainChannelConfigDO> pageResult = channelConfigService.getChannelConfigPage(pageVO);
        return success(SpChannelConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出服务商渠道配置信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:export')")
    public void exportChannelConfigExcel(@Valid SpMainChannelConfigExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainChannelConfigDO> list = channelConfigService.getChannelConfigList(exportReqVO);
        // 导出 Excel
        List<SpMainChannelConfigExcelVO> datas = SpChannelConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "服务商渠道配置信息.xls", "数据", SpMainChannelConfigExcelVO.class, datas);
    }

    @GetMapping("/channel-list")
    @Operation(summary = "获得服务商渠道配置信息列表")
    @Parameter(name = "spId", description = "服务商Id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<List<ChannelConfigVo>> getChannelConfigListBySpId(@RequestParam("spId") Long spId) {
        List<ChannelConfigVo> list = channelConfigService.getChannelConfigListBySpId(spId);
        return success(list);
    }

    @GetMapping("/channel-page")
    @Operation(summary = "获得服务商渠道配置信息列表分页")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<PageResult<ChannelConfigVo>> getChannelConfigPageBySpId(@Valid ChannelConfigPageVO pageVO) {
        PageResult<ChannelConfigVo> page = channelConfigService.getChannelConfigPageBySpId(pageVO);
        return success(page);
    }

    @GetMapping("/channel-app-page")
    @Operation(summary = "获得服务商渠道配置信息列表分页")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:query')")
    public CommonResult<PageResult<ChannelAppConfigVo>> getChannelConfigAppPageBySpId(@Valid ChannelConfigPageVO pageVO) {
        PageResult<ChannelAppConfigVo> page = channelConfigService.getChannelAppConfigPageBySpId(pageVO);
        return success(page);
    }

    // ################################################# 小红书本地生活小程序 ##########################################################
    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PostMapping("/channel-xhsminiapp-create")
    @Operation(summary = "创建服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:create')")
    public CommonResult<Long> createChannelXhsMiniAppChannelConfig(@Valid @RequestBody SpMainXhsMiniAppChannelConfigSaveReqVO reqVO) {
        return success(xhsMiniAppChannelConfigService.createXhsMiniAppChannelConfig(reqVO));
    }

    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PutMapping("/channel-xhsminiapp-update")
    @Operation(summary = "更新服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:update')")
    public CommonResult<Boolean> updateChannelXhsMiniAppChannelConfig(@Valid @RequestBody SpMainXhsMiniAppChannelConfigUpdateReqVO reqVO) {
        xhsMiniAppChannelConfigService.updateXhsMiniAppChannelConfig(reqVO);
        return success(true);
    }

    /**
     * 获取小红书本地生活小程序
     * @param id
     * @return
     */
    @GetMapping("/channel-xhsminiapp-get")
    @Operation(summary = "获取服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:create')")
    public CommonResult<SpMainXhsMiniAppChannelConfigRespVO> getChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        return success(BeanUtil.toBean(xhsMiniAppChannelConfigService.getById(id),SpMainXhsMiniAppChannelConfigRespVO.class));
    }

    /**
     * 保存小红书本地生活小程序
     * @param id
     * @return
     */
    @PutMapping("/channel-xhsminiapp-upsert")
    @Operation(summary = "提交审核服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:update')")
    public CommonResult<Boolean> upsertChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        xhsMiniAppChannelConfigService.upsertChannelConfig(id);
        return success(true);
    }

    /**
     * 保存小红书本地生活小程序
     * @param id
     * @return
     */
    @PutMapping("/channel-xhsminiapp-unbind")
    @Operation(summary = "解绑服务商渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:main-channel-config:update')")
    public CommonResult<Boolean> unbindChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        xhsMiniAppChannelConfigService.unbindChannelConfig(id);
        return success(true);
    }
}
