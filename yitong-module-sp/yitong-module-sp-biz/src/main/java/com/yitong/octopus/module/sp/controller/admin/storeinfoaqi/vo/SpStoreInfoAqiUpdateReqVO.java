package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家资质信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoAqiUpdateReqVO extends SpStoreInfoAqiBaseVO {

    @Schema(description = "id", required = true, example = "32297")
    @NotNull(message = "id不能为空")
    private Long id;

}
