package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi;

import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 服务商渠道配置信息创建【小红书本地生活小程序】POI Request VO")
@Data
@ToString(callSuper = true)
public class SpStoreXhsMiniAppChannelPOIConfigSaveReqVO {

    /**
     * appId
     */
    @Schema(description = "appId", required = true, example = "1")
    @NotNull(message = "appId 不能为空")
    private Long appId;

    @Schema(description = "平台渠道ID", required = true, example = "5575")
    @NotEmpty(message = "平台渠道ID不能为空")
    private String channelId;

    @Schema(description = "门店ID", required = true, example = "29498")
    @NotNull(message = "门店Id不能为空")
    private Long storeId;

    @NotEmpty(message = "渠道商户门店名称不能为空")
    @Schema(description = "渠道商户门店名称", required = true, example = "true")
    private String channelStoreName;

    @NotEmpty(message = "渠道绑定POI不能为空")
    @Schema(description = "渠道绑定POI", required = true, example = "true")
    private String channelStorePoi;

    /**
     * 小红书渠道字典
     * {
     *    xhsUserId: 111,
     *    xhsUserName: '小红书',
     * }
     */
    @Schema(description = "配置信息", required = true, example = "{name:'张三',age:18}")
    @NotEmpty(message = "配置信息不能为空")
    @JsonRawValue
    private String configValue;

}
