package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 门店审核记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoAuditLogUpdateReqVO extends SpStoreInfoAuditLogBaseVO {

    @Schema(description = "id", required = true, example = "2648")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
