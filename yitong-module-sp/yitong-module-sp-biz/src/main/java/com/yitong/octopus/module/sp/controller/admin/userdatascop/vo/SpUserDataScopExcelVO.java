package com.yitong.octopus.module.sp.controller.admin.userdatascop.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Set;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 用户与商家权限数据权限 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpUserDataScopExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("用户ID")
    private Long userId;

    @ExcelProperty("数据范围（1：全部数据权限 2：自定数据权限 3：本商家/服务商数据权限 4：本服务商商家及以下数据权限）")
    private Integer dataScope;

    @ExcelProperty("数据范围(指定部门数组)")
    private Set<Long> dataScopeSpIds;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
