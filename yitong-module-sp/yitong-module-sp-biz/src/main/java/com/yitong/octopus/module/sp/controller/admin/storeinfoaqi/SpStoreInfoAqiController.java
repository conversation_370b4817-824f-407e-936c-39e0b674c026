//package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.storeinfoaqi.SpStoreInfoAqiDO;
//import com.yitong.octopus.module.sp.convert.storeinfoaqi.SpStoreInfoAqiConvert;
//import com.yitong.octopus.module.sp.service.storeinfoaqi.SpStoreInfoAqiService;
//
//@Tag(name = "管理后台 - 商家资质信息")
//@RestController
//@RequestMapping("/sp/store-info-aqi")
//@Validated
//public class SpStoreInfoAqiController {
//
//    @Resource
//    private SpStoreInfoAqiService storeInfoAqiService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家资质信息")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:create')")
//    public CommonResult<Long> createStoreInfoAqi(@Valid @RequestBody SpStoreInfoAqiCreateReqVO createReqVO) {
//        return success(storeInfoAqiService.createStoreInfoAqi(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家资质信息")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:update')")
//    public CommonResult<Boolean> updateStoreInfoAqi(@Valid @RequestBody SpStoreInfoAqiUpdateReqVO updateReqVO) {
//        storeInfoAqiService.updateStoreInfoAqi(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家资质信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:delete')")
//    public CommonResult<Boolean> deleteStoreInfoAqi(@RequestParam("id") Long id) {
//        storeInfoAqiService.deleteStoreInfoAqi(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家资质信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:query')")
//    public CommonResult<SpStoreInfoAqiRespVO> getStoreInfoAqi(@RequestParam("id") Long id) {
//        SpStoreInfoAqiDO storeInfoAqi = storeInfoAqiService.getStoreInfoAqi(id);
//        return success(SpStoreInfoAqiConvert.INSTANCE.convert(storeInfoAqi));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商家资质信息列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:query')")
//    public CommonResult<List<SpStoreInfoAqiRespVO>> getStoreInfoAqiList(@RequestParam("ids") Collection<Long> ids) {
//        List<SpStoreInfoAqiDO> list = storeInfoAqiService.getStoreInfoAqiList(ids);
//        return success(SpStoreInfoAqiConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家资质信息分页")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:query')")
//    public CommonResult<PageResult<SpStoreInfoAqiRespVO>> getStoreInfoAqiPage(@Valid SpStoreInfoAqiPageReqVO pageVO) {
//        PageResult<SpStoreInfoAqiDO> pageResult = storeInfoAqiService.getStoreInfoAqiPage(pageVO);
//        return success(SpStoreInfoAqiConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家资质信息 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-aqi:export')")
//    @OperateLog(type = EXPORT)
//    public void exportStoreInfoAqiExcel(@Valid SpStoreInfoAqiExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SpStoreInfoAqiDO> list = storeInfoAqiService.getStoreInfoAqiList(exportReqVO);
//        // 导出 Excel
//        List<SpStoreInfoAqiExcelVO> datas = SpStoreInfoAqiConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商家资质信息.xls", "数据", SpStoreInfoAqiExcelVO.class, datas);
//    }
//
//}
