package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigViewDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.dto.SpStoreInfoAqiDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfogallery.dto.SpStoreInfoGalleryDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.dto.SpStoreInfoLabelDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfomap.dto.SpStoreInfoMapDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.dto.SpStoreInfoOpeningHoursDto;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoCreateReqVO extends SpStoreInfoBaseVO {

    @Schema(description = "所在省id", required = true, example = "8918")
    @NotNull(message = "所在省id不能为空")
    private Integer spProvinceId;

    @Schema(description = "所在市id", required = true, example = "12694")
    @NotNull(message = "所在市id不能为空")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "10837")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "27127")
    private Integer spTownId;

    /**
     * 商家门店Logo
     */
    @Schema(description = "商家门店Logo", example = "http://www.xxx.com")
    private String storeLogo;

    @Schema(description = "资质信息列表")
    private List<SpStoreInfoAqiDto> spStoreInfoAqiDtoList;

    @Schema(description = "门店图片墙列表")
    private List<SpStoreInfoGalleryDto> spStoreInfoGalleryDtoList;

    @Schema(description = "门店标签列表")
    private List<SpStoreInfoLabelDto> spStoreInfoLabelDtoList;

    @Schema(description = "门店渠道列表")
    private List<SpStoreChannelConfigViewDto> spStoreChannelConfigViewDtoList;

    @Schema(description = "门店地图")
    private  SpStoreInfoMapDto spStoreInfoMapDto;

    @Schema(description = "门店营业时间")
    private List<SpStoreInfoOpeningHoursDto> spStoreInfoOpeningHoursDtoList;

//    @Schema(description = "门店营业时间JSON", example = "周一：10：00 - 13：00")
//    private String storeOpenTime;

    @Schema(description = "门店营业时间描述", example = "周一：10：00 - 13：00")
    private String storeOpenTimeDesc;

    @Schema(description = "营业执照号")
    private String licenseNum;

    @Schema(description = "营业执照所在省")
    private String licenseProvince;

    @Schema(description = "营业执照所在市")
    private String licenseCity;

    @Schema(description = "营业执照所在县")
    private String licenseCounty;

    @Schema(description = "营业执照所在镇")
    private String licenseTown;

    @Schema(description = "营业执照电子版")
    private String licenseImg;

    @Schema(description = "营业执照是否长期有效", required = true)
    private Integer licenseIsLong;

    @Schema(description = "营业执照有效期开始")
    private Long licenseStart;

    @Schema(description = "营业执照有效期结束")
    private Long licenseEnd;
}
