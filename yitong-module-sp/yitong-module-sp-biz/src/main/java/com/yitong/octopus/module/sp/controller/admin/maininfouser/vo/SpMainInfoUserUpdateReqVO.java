package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商户子账号更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoUserUpdateReqVO extends SpMainInfoUserBaseVO {

    @Schema(description = "主键", required = true, example = "19328")
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "商户ID", required = true, example = "21695")
    @NotNull(message = "商户ID不能为空")
    private Long spId;

}
