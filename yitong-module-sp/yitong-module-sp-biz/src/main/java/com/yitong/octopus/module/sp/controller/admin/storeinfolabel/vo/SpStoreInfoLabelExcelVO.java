package com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商家门店标签关联 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoLabelExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店ID")
    private Long storeId;

    @ExcelProperty("标签Id")
    private Long labelId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
