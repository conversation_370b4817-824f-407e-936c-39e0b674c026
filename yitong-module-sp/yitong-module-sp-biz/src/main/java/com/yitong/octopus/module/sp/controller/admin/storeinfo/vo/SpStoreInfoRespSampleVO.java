package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息 Response VO")
@Data
@ToString(callSuper = true)
public class SpStoreInfoRespSampleVO {

    @Schema(description = "id", required = true, example = "25053")
    private Long id;

    @Schema(description = "主体Id", required = true, example = "11434")
    @NotNull(message = "主体Id不能为空")
    private Long spId;

    @Schema(description = "商家门店名称", required = true, example = "王五")
    @NotNull(message = "商家门店名称不能为空")
    private String storeName;

    @Schema(description = "状态", example = "2")
    private Integer spStatus;

    @Schema(description = "所在省", example = "2")
    private String spProvince;

    @Schema(description = "所在市", example = "2")
    private String spCity;

    @Schema(description = "所在县", example = "2")
    private String spCounty;

    @Schema(description = "所在镇", example = "2")
    private String spTown;

    @Schema(description = "详细地址", example = "2")
    private String spAdd;

    @Schema(description = "商家门店Logo", example = "2")
    private String storeLogo;

    @Schema(description = "门店主图", example = "http://www.baidu.com")
    private String mainImage;
}
