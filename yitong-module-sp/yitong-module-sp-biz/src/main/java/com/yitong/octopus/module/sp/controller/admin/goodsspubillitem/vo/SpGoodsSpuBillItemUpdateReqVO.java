package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商品spu结算信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuBillItemUpdateReqVO extends SpGoodsSpuBillItemBaseVO {

    @Schema(description = "主键", required = true, example = "17611")
    private Long id;

}
