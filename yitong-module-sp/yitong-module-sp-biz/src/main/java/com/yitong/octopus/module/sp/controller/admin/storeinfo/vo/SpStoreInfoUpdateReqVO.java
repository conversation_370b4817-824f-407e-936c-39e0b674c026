package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.dto.SpStoreInfoAqiDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfogallery.dto.SpStoreInfoGalleryDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.dto.SpStoreInfoLabelDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfomap.dto.SpStoreInfoMapDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.dto.SpStoreInfoOpeningHoursDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoUpdateReqVO extends SpStoreInfoBaseVO {

    @Schema(description = "id", required = true, example = "25053")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "所在省id", example = "8918")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "12694")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "10837")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "27127")
    private Integer spTownId;

    @Schema(description = "资质信息列表")
    private List<SpStoreInfoAqiDto> spStoreInfoAqiDtoList;

    @Schema(description = "门店图片墙列表")
    private List<SpStoreInfoGalleryDto> spStoreInfoGalleryDtoList;

    @Schema(description = "门店标签列表")
    private List<SpStoreInfoLabelDto> spStoreInfoLabelDtoList;

    @Schema(description = "门店营业时间")
//    @NotNull(message = "门店营业时间不能为空")
    private List<SpStoreInfoOpeningHoursDto> spStoreInfoOpeningHoursDtoList;

    @Schema(description = "门店地图")
    SpStoreInfoMapDto spStoreInfoMapDto;

    @Schema(description = "营业执照号")
    private String licenseNum;

    @Schema(description = "营业执照所在省")
    private String licenseProvince;

    @Schema(description = "营业执照所在市")
    private String licenseCity;

    @Schema(description = "营业执照所在县")
    private String licenseCounty;

    @Schema(description = "营业执照所在镇")
    private String licenseTown;

    @Schema(description = "营业执照电子版")
    private String licenseImg;

    @Schema(description = "营业执照是否长期有效")
    private Integer licenseIsLong;

    @Schema(description = "营业执照有效期开始")
    private Long licenseStart;

    @Schema(description = "营业执照有效期结束")
    private Long licenseEnd;

    @Schema(description = "门店营业时间描述", example = "周一：10：00 - 13：00")
    private String storeOpenTimeDesc;

}
