package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoauditenterprise.SpMainInfoAuditEnterpriseDO;
import com.yitong.octopus.module.sp.convert.maininfoauditenterprise.SpMainInfoAuditEnterpriseConvert;
import com.yitong.octopus.module.sp.service.maininfoauditenterprise.SpMainInfoAuditEnterpriseService;

@Tag(name = "管理后台 - 主体认证信息（企业）")
@RestController
@RequestMapping("/sp/main-info-audit-enterprise")
@Validated
public class SpMainInfoAuditEnterpriseController {

    @Resource
    private SpMainInfoAuditEnterpriseService mainInfoAuditEnterpriseService;

    @PostMapping("/create")
    @Operation(summary = "创建主体认证信息（企业）")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:create')")
    public CommonResult<Long> createMainInfoAuditEnterprise(@Valid @RequestBody SpMainInfoAuditEnterpriseCreateReqVO createReqVO) {
        return success(mainInfoAuditEnterpriseService.createMainInfoAuditEnterprise(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体认证信息（企业）")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:update')")
    public CommonResult<Boolean> updateMainInfoAuditEnterprise(@Valid @RequestBody SpMainInfoAuditEnterpriseUpdateReqVO updateReqVO) {
        mainInfoAuditEnterpriseService.updateMainInfoAuditEnterprise(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体认证信息（企业）")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:delete')")
    public CommonResult<Boolean> deleteMainInfoAuditEnterprise(@RequestParam("id") Long id) {
        mainInfoAuditEnterpriseService.deleteMainInfoAuditEnterprise(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体认证信息（企业）")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:query')")
    public CommonResult<SpMainInfoAuditEnterpriseViewRespVO> getMainInfoAuditEnterprise(@RequestParam("id") Long id) {
        SpMainInfoAuditEnterpriseDO mainInfoAuditEnterprise = mainInfoAuditEnterpriseService.getMainInfoAuditEnterprise(id);
        return success(SpMainInfoAuditEnterpriseConvert.INSTANCE.convertView(mainInfoAuditEnterprise));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体认证信息（企业）列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:query')")
    public CommonResult<List<SpMainInfoAuditEnterpriseRespVO>> getMainInfoAuditEnterpriseList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoAuditEnterpriseDO> list = mainInfoAuditEnterpriseService.getMainInfoAuditEnterpriseList(ids);
        return success(SpMainInfoAuditEnterpriseConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体认证信息（企业）分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:query')")
    public CommonResult<PageResult<SpMainInfoAuditEnterpriseRespVO>> getMainInfoAuditEnterprisePage(@Valid SpMainInfoAuditEnterprisePageReqVO pageVO) {
        PageResult<SpMainInfoAuditEnterpriseDO> pageResult = mainInfoAuditEnterpriseService.getMainInfoAuditEnterprisePage(pageVO);
        return success(SpMainInfoAuditEnterpriseConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体认证信息（企业） Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-enterprise:export')")
    public void exportMainInfoAuditEnterpriseExcel(@Valid SpMainInfoAuditEnterpriseExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoAuditEnterpriseDO> list = mainInfoAuditEnterpriseService.getMainInfoAuditEnterpriseList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoAuditEnterpriseExcelVO> datas = SpMainInfoAuditEnterpriseConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体认证信息（企业）.xls", "数据", SpMainInfoAuditEnterpriseExcelVO.class, datas);
    }

}
