package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体平台合同Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractRespVO extends SpMainPlatformContractBaseVO {

    @Schema(description = "id", required = true, example = "10360")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
