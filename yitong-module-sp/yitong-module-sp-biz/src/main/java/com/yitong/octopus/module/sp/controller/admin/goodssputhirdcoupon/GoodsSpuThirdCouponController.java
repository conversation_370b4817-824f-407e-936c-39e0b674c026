package com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon;

import cn.hutool.core.util.RandomUtil;
import com.yitong.octopus.framework.common.vo.CommonsStatusUpdateVo;
import com.yitong.octopus.framework.excel.core.util.ExcelImportRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuImportRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuSoldDateImportExcelVO;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.time.LocalDateTime;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.framework.apilog.core.annotation.ApiAccessLog;
import static com.yitong.octopus.framework.apilog.core.enums.OperateTypeEnum.*;

import com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodssputhirdcoupon.GoodsSpuThirdCouponDO;
import com.yitong.octopus.module.sp.service.goodssputhirdcoupon.GoodsSpuThirdCouponService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 商品Spu三方券码")
@RestController
@RequestMapping("/sp/goods-spu-third-coupon")
@Validated
public class GoodsSpuThirdCouponController {

    @Resource
    private GoodsSpuThirdCouponService goodsSpuThirdCouponService;

    @PostMapping("/create")
    @Operation(summary = "创建商品Spu三方券码")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:create')")
    public CommonResult<Long> createGoodsSpuThirdCoupon(@Valid @RequestBody GoodsSpuThirdCouponSaveReqVO reqVO) {
        return success(goodsSpuThirdCouponService.createGoodsSpuThirdCoupon(reqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品Spu三方券码")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:update')")
    public CommonResult<Boolean> updateGoodsSpuThirdCoupon(@Valid @RequestBody GoodsSpuThirdCouponSaveReqVO reqVO) {
        goodsSpuThirdCouponService.updateGoodsSpuThirdCoupon(reqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新商品Spu三方券码状态")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:update')")
    public CommonResult<Boolean> updateGoodsSpuThirdCouponStatus(@Valid @RequestBody CommonsStatusUpdateVo reqVO) {
        goodsSpuThirdCouponService.updateGoodsSpuThirdCouponStatus(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品Spu三方券码")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:delete')")
    public CommonResult<Boolean> deleteGoodsSpuThirdCoupon(@RequestParam("id") Long id) {
        goodsSpuThirdCouponService.deleteGoodsSpuThirdCoupon(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品Spu三方券码")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:query')")
    public CommonResult<GoodsSpuThirdCouponRespVO> getGoodsSpuThirdCoupon(@RequestParam("id") Long id) {
        GoodsSpuThirdCouponDO goodsSpuThirdCoupon = goodsSpuThirdCouponService.getGoodsSpuThirdCoupon(id);
        return success(BeanUtils.toBean(goodsSpuThirdCoupon, GoodsSpuThirdCouponRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品Spu三方券码分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:query')")
    public CommonResult<PageResult<GoodsSpuThirdCouponRespVO>> getGoodsSpuThirdCouponPage(@Valid GoodsSpuThirdCouponPageReqVO pageReqVO) {
        PageResult<GoodsSpuThirdCouponDO> pageResult = goodsSpuThirdCouponService.getGoodsSpuThirdCouponPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GoodsSpuThirdCouponRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品Spu三方券码 Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportGoodsSpuThirdCouponExcel(@Valid GoodsSpuThirdCouponPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GoodsSpuThirdCouponDO> list = goodsSpuThirdCouponService.getGoodsSpuThirdCouponPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品Spu三方券码.xls", "数据", GoodsSpuThirdCouponRespVO.class,
                        BeanUtils.toBean(list, GoodsSpuThirdCouponRespVO.class));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入商品三方券码模板")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:import')")
    public void importSoldDateTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        LocalDateTime from = LocalDateTime.now();
        LocalDateTime to = from.plusDays(1);
        List<GoodsSpuThirdCouponSaveImportVO> list = Arrays.asList(
                GoodsSpuThirdCouponSaveImportVO.builder().spuId(1000000000L).couponCode(RandomUtil.randomString(12)).validDateFrom(from).validDateTo(to).build(),
                GoodsSpuThirdCouponSaveImportVO.builder().spuId(1000000001L).couponCode(RandomUtil.randomString(12)).validDateFrom(from).validDateTo(to).build()
        );
        // 输出
        ExcelUtils.write(response, "商品三方券码入模板.xls", "商品三方券码列表", GoodsSpuThirdCouponSaveImportVO.class, list);
    }

    @PostMapping("/goods-coupon-import")
    @Operation(summary = "导入商品三方券码")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true)
    })
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-third-coupon:import')")
    public CommonResult<ExcelImportRespVO> importSoldDateExcel(@RequestParam("file") MultipartFile file) throws Exception {
        List<GoodsSpuThirdCouponSaveImportVO> list = ExcelUtils.read(file, GoodsSpuThirdCouponSaveImportVO.class);
        return success(goodsSpuThirdCouponService.importGoodsSpuThirdCouponExcel(list));
    }

}