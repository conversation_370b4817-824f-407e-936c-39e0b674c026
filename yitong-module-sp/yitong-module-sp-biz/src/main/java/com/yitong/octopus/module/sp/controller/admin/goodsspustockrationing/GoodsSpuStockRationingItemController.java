package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingItemDO;
import com.yitong.octopus.module.sp.service.goodsspustockrationing.GoodsSpuStockRationingItemService;

@Tag(name = "管理后台 - 商品库存供给明细")
@RestController
@RequestMapping("/sp/goods-spu-stock-rationing-item")
@Validated
public class GoodsSpuStockRationingItemController {

    @Resource
    private GoodsSpuStockRationingItemService goodsSpuStockRationingItemService;

    @PostMapping("/create")
    @Operation(summary = "创建商品库存供给明细")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:create')")
    public CommonResult<Long> createGoodsSpuStockRationingItem(@Valid @RequestBody GoodsSpuStockRationingItemSaveReqVO createReqVO) {
        return success(goodsSpuStockRationingItemService.createGoodsSpuStockRationingItem(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品库存供给明细")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:update')")
    public CommonResult<Boolean> updateGoodsSpuStockRationingItem(@Valid @RequestBody GoodsSpuStockRationingItemSaveReqVO updateReqVO) {
        goodsSpuStockRationingItemService.updateGoodsSpuStockRationingItem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品库存供给明细")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:delete')")
    public CommonResult<Boolean> deleteGoodsSpuStockRationingItem(@RequestParam("id") Long id) {
        goodsSpuStockRationingItemService.deleteGoodsSpuStockRationingItem(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品库存供给明细")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:query')")
    public CommonResult<GoodsSpuStockRationingItemRespVO> getGoodsSpuStockRationingItem(@RequestParam("id") Long id) {
        GoodsSpuStockRationingItemDO goodsSpuStockRationingItem = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItem(id);
        return success(BeanUtils.toBean(goodsSpuStockRationingItem, GoodsSpuStockRationingItemRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品库存供给明细分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:query')")
    public CommonResult<PageResult<GoodsSpuStockRationingItemRespVO>> getGoodsSpuStockRationingItemPage(@Valid GoodsSpuStockRationingItemPageReqVO pageReqVO) {
        PageResult<GoodsSpuStockRationingItemDO> pageResult = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItemPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, GoodsSpuStockRationingItemRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品库存供给明细列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:query')")
    public CommonResult<List<GoodsSpuStockRationingItemRespVO>> getGoodsSpuStockRationingItemList(@RequestParam("rationingId") Long rationingId) {
        List<GoodsSpuStockRationingItemDO> list = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItemListByRationingId(rationingId);
        return success(BeanUtils.copyToList(list, GoodsSpuStockRationingItemRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品库存供给明细 Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-stock-rationing-item:export')")
    public void exportGoodsSpuStockRationingItemExcel(@Valid GoodsSpuStockRationingItemPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<GoodsSpuStockRationingItemDO> list = goodsSpuStockRationingItemService.getGoodsSpuStockRationingItemPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "商品库存供给明细.xls", "数据", GoodsSpuStockRationingItemRespVO.class,
                        BeanUtils.toBean(list, GoodsSpuStockRationingItemRespVO.class));
    }

}