package com.yitong.octopus.module.sp.service.storeinfolabel;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.dto.SpStoreInfoLabelDto;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfolabel.SpStoreInfoLabelDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.sp.convert.storeinfolabel.SpStoreInfoLabelConvert;
import com.yitong.octopus.module.sp.dal.mysql.storeinfolabel.SpStoreInfoLabelMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 商家门店标签关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpStoreInfoLabelServiceImpl implements SpStoreInfoLabelService {

    @Resource
    private SpStoreInfoLabelMapper storeInfoLabelMapper;

    @Override
    public Long createStoreInfoLabel(SpStoreInfoLabelCreateReqVO createReqVO) {
        // 插入
        SpStoreInfoLabelDO storeInfoLabel = SpStoreInfoLabelConvert.INSTANCE.convert(createReqVO);
        storeInfoLabelMapper.insert(storeInfoLabel);
        // 返回
        return storeInfoLabel.getId();
    }

    @Override
    public void updateStoreInfoLabel(SpStoreInfoLabelUpdateReqVO updateReqVO) {
        // 校验存在
        validateStoreInfoLabelExists(updateReqVO.getId());
        // 更新
        SpStoreInfoLabelDO updateObj = SpStoreInfoLabelConvert.INSTANCE.convert(updateReqVO);
        storeInfoLabelMapper.updateById(updateObj);
    }

    @Override
    public void deleteStoreInfoLabel(Long id) {
        // 校验存在
        validateStoreInfoLabelExists(id);
        // 删除
        storeInfoLabelMapper.deleteById(id);
    }

    private void validateStoreInfoLabelExists(Long id) {
        if (storeInfoLabelMapper.selectById(id) == null) {
            throw exception(STORE_INFO_LABEL_NOT_EXISTS);
        }
    }

    @Override
    public SpStoreInfoLabelDO getStoreInfoLabel(Long id) {
        return storeInfoLabelMapper.selectById(id);
    }

    @Override
    public List<SpStoreInfoLabelDO> getStoreInfoLabelList(Collection<Long> ids) {
        return storeInfoLabelMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SpStoreInfoLabelDO> getStoreInfoLabelPage(SpStoreInfoLabelPageReqVO pageReqVO) {
        return storeInfoLabelMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SpStoreInfoLabelDO> getStoreInfoLabelList(SpStoreInfoLabelExportReqVO exportReqVO) {
        return storeInfoLabelMapper.selectList(exportReqVO);
    }

    @Override
    public boolean createStoreInfoLabels(List<SpStoreInfoLabelCreateReqVO> reqVOList) {
        storeInfoLabelMapper.insertBatch(BeanUtil.copyToList(reqVOList,SpStoreInfoLabelDO.class));
        return true;
    }

    @Override
    public boolean updateStoreInfoLabels(List<SpStoreInfoLabelUpdateReqVO> reqVOList) {
        storeInfoLabelMapper.updateBatchById(BeanUtil.copyToList(reqVOList,SpStoreInfoLabelDO.class));
        return true;
    }

    @Override
    public void deleteStoreInfoLabelByStoreId(Long storeId) {
        // 删除
        storeInfoLabelMapper.delete(new LambdaQueryWrapperX<SpStoreInfoLabelDO>()
                .eq(SpStoreInfoLabelDO::getStoreId,storeId));
    }

    @Override
    public List<SpStoreInfoLabelDto> getStoreInfoLabelListByStoreId(Long storeId) {
        List<SpStoreInfoLabelDO> storeInfoGalleryDOS= storeInfoLabelMapper.selectList(new LambdaQueryWrapperX<SpStoreInfoLabelDO>()
                .eq(SpStoreInfoLabelDO::getStoreId,storeId));
        return BeanUtil.copyToList(storeInfoGalleryDOS, SpStoreInfoLabelDto.class);
    }

}
