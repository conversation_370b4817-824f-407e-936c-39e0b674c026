package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 服务商门店-渠道配置信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigViewReqVO extends SpStoreChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "14253")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "审核状态 ", required = true, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审批人", example = "32579")
    private String auditUser;

    @Schema(description = "审批备注", example = "不香")
    private String auditReason;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;

    @Schema(description = "渠道商户门店ID", example = "axddd1233")
    private String channelStoreId;


    @Schema(description = "渠道返回结果", example = "{'success:true'}")
    private String channelResult;
}
