package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商家属性批量 Request VO")
@Data
public class SpGoodsSpuBatchPropertySaveReqVO {

    @Schema(description = "商家属性列表", example = "14993")
    @NotNull(message = "商家属性不能为空")
    private List<SpGoodsSpuPropertySaveReqVO> properties;

}