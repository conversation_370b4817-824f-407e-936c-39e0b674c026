package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.SpMainInfoAqiCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.SpMainInfoAuditEnterpriseCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo.SpMainInfoAuditPersonalCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo.SpMainPlatformContractCreateReqVO;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体基本信息创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoCreateReqVO extends SpMainInfoBaseVO {

    @Schema(description = "所在省id", required = true, example = "7635")
    @NotNull(message = "所在省id不能为空")
    private Integer spProvinceId;

    @Schema(description = "所在市id", required = true, example = "772")
    @NotNull(message = "所在市id不能为空")
    private Integer spCityId;

    @Schema(description = "所在县id", required = true, example = "13418")
    @NotNull(message = "所在县id不能为空")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "6052")
    private Integer spTownId;

    @Schema(description = "联系人电话")
    private String linkTel;

    @Schema(description = "联系人邮箱")
    private String linkEmail;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "个人认证", example = "个人认证")
    private SpMainInfoAuditPersonalCreateReqVO auditPersonalVo;

    @Schema(description = "企业认证", example = "企业认证")
    private SpMainInfoAuditEnterpriseCreateReqVO auditEnterpriseVo;

    @Schema(description = "主体资质信息", example = "主体资质信息")
    private SpMainInfoAqiCreateReqVO aqiInfoVo;

    @Schema(description = "合同信息", example = "合同信息")
    private SpMainPlatformContractCreateReqVO contractVo;

    @Schema(description = "商标注册证", example = "商标注册证")
    private SpMainInfoAqiCreateReqVO trcInfoVo;
}
