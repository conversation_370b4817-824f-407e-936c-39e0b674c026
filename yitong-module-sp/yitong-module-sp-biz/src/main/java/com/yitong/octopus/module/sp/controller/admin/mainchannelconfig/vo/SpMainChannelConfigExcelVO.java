package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 服务商渠道配置信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainChannelConfigExcelVO {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("服务商ID")
    private Long spId;

    @ExcelProperty("平台渠道ID")
    private Long channelId;

    @ExcelProperty("配置信息")
    private String configValue;

    @ExcelProperty("审核状态 0 待审核 1 未通过 2 已通过")
    private Integer auditStatus;

    @ExcelProperty("审批人")
    private Long auditUserId;

    @ExcelProperty("审批备注")
    private String auditReason;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
