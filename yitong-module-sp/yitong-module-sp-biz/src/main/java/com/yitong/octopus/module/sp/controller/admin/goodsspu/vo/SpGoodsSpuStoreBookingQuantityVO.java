package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpGoodsSpuStoreBookingQuantityVO {

	@Schema(description = "商品ID", required = true, example = "6521")
	@NotNull
	private Long id;

	@Schema(description = "可用门店的预约数配置", required = true)
	@NotNull
	@Valid
	private List<StoreBookingQuantityVO> list;
	
	
	
	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	public static class StoreBookingQuantityVO {

		@Schema(description = "门店Id")
		@NotNull
		private Long storeId;

		@Schema(description = "可预约数（仅在商品需要预约时有效)， -1表示无限制")
		@NotNull
		private Integer bookingQuantity;

	}


}
