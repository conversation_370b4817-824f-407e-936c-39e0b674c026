package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体认证信息（企业） Excel 导出 Request VO，参数和 SpMainInfoAuditEnterprisePageReqVO 是一致的")
@Data
public class SpMainInfoAuditEnterpriseExportReqVO {

    @Schema(description = "主体ID", example = "4375")
    private Long spId;

    @Schema(description = "法人姓名", example = "芋艿")
    private String legalName;

    @Schema(description = "营业执照号")
    private String licenseNum;

    @Schema(description = "营业执照所在省id", example = "12516")
    private Integer licenseProvinceId;

    @Schema(description = "营业执照所在市id", example = "7046")
    private Integer licenseCityId;

    @Schema(description = "营业执照所在县id", example = "26735")
    private Integer licenseCountyId;

    @Schema(description = "营业执照所在镇id", example = "31759")
    private Integer licenseTownId;

    @Schema(description = "营业执照是否长期有效 0 否，1 是")
    private Integer licenseIsLong;

    @Schema(description = "银行开户名", example = "王五")
    private String bankAccountName;

    @Schema(description = "开户银行支行名称", example = "王五")
    private String bankName;

    @Schema(description = "开户银行所在省id", example = "20528")
    private Integer bankProvinceId;

    @Schema(description = "开户银行所在市id", example = "10226")
    private Integer bankCityId;

    @Schema(description = "开户银行所在县id", example = "23775")
    private Integer bankCountyId;

    @Schema(description = "开户银行所在镇id", example = "5934")
    private Integer bankTownId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
