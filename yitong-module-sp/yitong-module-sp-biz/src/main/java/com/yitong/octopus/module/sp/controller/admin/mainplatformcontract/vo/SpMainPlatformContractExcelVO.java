package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 主体平台合同
 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainPlatformContractExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty(value = "合同类型", converter = DictConvert.class)
    @DictFormat("sp_main_platform_contract_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer contractType;

    @ExcelProperty("主体ID")
    private Long spId;

    @ExcelProperty("主体称")
    private String spName;

    @ExcelProperty(value = "主体类型", converter = DictConvert.class)
    @DictFormat("sp_main_platform_contract_sp_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer spType;

    @ExcelProperty("联系人姓名")
    private String linkName;

    @ExcelProperty("合作开始时间")
    private LocalDateTime coopStartTime;

    @ExcelProperty("合作结束时间")
    private LocalDateTime coopEndTime;

    @ExcelProperty(value = "合作状态", converter = DictConvert.class)
    @DictFormat("sp_main_platform_contract_coop_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer coopStatus;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("sp_main_platform_contract_audit_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer auditStatus;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("自动结算")
    private Boolean isAutoSettle;
}
