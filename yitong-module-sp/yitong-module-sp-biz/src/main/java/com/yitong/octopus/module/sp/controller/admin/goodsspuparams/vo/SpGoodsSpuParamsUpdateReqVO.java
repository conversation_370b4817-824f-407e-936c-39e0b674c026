package com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商品spu更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuParamsUpdateReqVO extends SpGoodsSpuParamsBaseVO {

    @Schema(description = "主键", required = true, example = "14241")
    @NotNull(message = "主键不能为空")
    private Long id;

}
