package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 变更所属服务商 Request VO")
@Data
@ToString(callSuper = true)
public class SpMainInfoChangeOwnerSpReqVO{

    @Schema(description = "ids", required = true, example = "27033")
    @NotNull(message = "id不能为空")
    private Long[] ids;

    @Schema(description = "id", required = true, example = "27033")
    @NotNull(message = "所属服务商不能为空")
    private Long ownerSpId;

}
