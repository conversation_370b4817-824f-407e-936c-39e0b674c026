package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商家门店渠道配置信息
 * <AUTHOR>
 */
@Data
public class SpStoreChannelConfigVo {

    /**
     * id
     */
    @Schema(description = "平台渠道Id")
    private Long id;
    /**
     * 渠道名称
     */
    @Schema(description = "平台渠道名称")
    private String name;
    /**
     * 渠道编码
     */
    @Schema(description = "平台渠道编码")
    private String code;
    /**
     * Logo
     */
    @Schema(description = "平台渠道Logo")
    private String logo;

    /**
     * 开通的渠道Id
     */
    @Schema(description = "开通的渠道Id,若没有则表示为开通")
    private Long spStoreChannelConfigId;

    /**
     * 渠道店铺ID
     */
    @Schema(description = "渠道店铺Id")
    private String channelStoreId;

    /**
     * 渠道返回结果-渠道审核成功之后
     */
    @Schema(description = "渠道店铺POIID")
    private String channelStorePoi;

    /**
     * 渠道返回结果-渠道审核成功之后
     */
    @Schema(description = "渠道店铺POI名称")
    private String channelStoreName;

    /**
     * 渠道返回结果-是否开通团购
     */
    @Schema(description = "渠道是否开通团购")
    private Boolean channelIsGroup;

    /**
     * 审核状态 0 待审核 1 未通过 2 已通过
     */
    private Integer auditStatus;

    /**
     * 审批备注
     */
    private String auditReason;

    /**
     * 审批时间
     */
    private LocalDateTime auditTime;

}
