package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import java.time.LocalDate;
import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;

import com.yitong.octopus.framework.common.pojo.PageParam;

import cn.hutool.core.date.DatePattern;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品spu分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuPageReqVO extends PageParam {
     //商品状态:0 草稿、1待审核、2上架、3下架、4已售罄
    /**
     * 草稿
     */
    public static final Integer DRAFT = 0;
    /**
     * 待审核
     */
    public static final Integer APPROVAL = 1;
    /**
     * 出售中商品[上架]
     */
    public static final Integer FOR_SALE = 2;

    /**
     * 下架商品[下架]
     */
    public static final Integer FORBIDDEN_SALE = 3;

    /**
     * 已售空商品[售罄]
     */
    public static final Integer SOLD_OUT = 4;

    /**
     * 警戒库存
     */
    public static final Integer ALERT_STOCK = 5;

    /**
     * 商品过期
     */
    public static final Integer SOLD_EXPIRE = 6;

    @Schema(description = "商品ID", example = "13133")
    private Long spuId;

    @Schema(description = "商品名称", example = "XX下午茶")
    private String spuName;

    @Schema(description = "主体Id", example = "24153")
    private Long spId;

    @Schema(description = "所属商服务商Id", example = "24153")
    private Long ownerSpId;

    @Schema(description = "商家门店Id", example = "2303")
    private Long storeId;

    @Schema(description = "类目", example = "25255")
    private Long categoryId;

    @Schema(description = "商品类型", example = "23655")
    private Integer type;

    @Schema(description = "商品品牌编号", example = "23655")
    private Integer brandId;

    @Schema(description = "商品状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "售卖时间截至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] soldDate;

    /**
     * 精选类型: 0 普通 ，1 主推
     */
    private Integer featuredType;

    @Schema(description = "前端请求的tab类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer tabType;

    @Schema(description = "查询条件", example = "XX下午茶")
    private String searchParams;

}
