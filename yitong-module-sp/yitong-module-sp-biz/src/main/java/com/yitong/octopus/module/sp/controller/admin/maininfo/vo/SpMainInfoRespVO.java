package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体基本信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoRespVO extends SpMainInfoBaseVO {

    @Schema(description = "id", required = true, example = "27033")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "所属主体名称")
    private String ownerSpName;

}
