package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.SpMainInfoAqiCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.SpMainInfoAqiUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.SpMainInfoAuditEnterpriseUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo.SpMainInfoAuditPersonalUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo.SpMainPlatformContractUpdateReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体基本信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoUpdateReqVO extends SpMainInfoBaseVO {

    @Schema(description = "id", required = true, example = "27033")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "所在省id", example = "7635")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "772")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "13418")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "6052")
    private Integer spTownId;

    @Schema(description = "联系人电话")
    private String linkTel;

    @Schema(description = "联系人邮箱")
    private String linkEmail;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "个人认证", example = "你说的对")
    private SpMainInfoAuditPersonalUpdateReqVO auditPersonalVo;

    @Schema(description = "企业认证", example = "你说的对")
    private SpMainInfoAuditEnterpriseUpdateReqVO auditEnterpriseVo;

    @Schema(description = "主体资质信息", example = "你说的对")
    private SpMainInfoAqiUpdateReqVO aqiInfoVo;

    @Schema(description = "合同信息", example = "你说的对")
    private SpMainPlatformContractUpdateReqVO contractVo;

    @Schema(description = "商标注册证", example = "商标注册证")
    private SpMainInfoAqiUpdateReqVO trcInfoVo;
}
