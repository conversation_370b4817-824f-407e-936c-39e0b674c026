package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;

import static com.yitong.octopus.framework.common.constant.Constants.STR_ZERO;

/**
* 商品spu结算信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpGoodsSpuBillItemBaseVO {

    @Schema(description = "主体Id", example = "3145")
    private Long spId;

    @Schema(description = "商家门店Id", example = "32735")
    private Long storeId;

    @Schema(description = "商品spuId", example = "694")
    private Long spuId;

    @Schema(description = "商品合同Id", example = "23032")
    private Long spContractId;

    @Schema(description = "结算项ID", example = "15778")
    @NotNull(message = "结算项ID不能为空")
    private Long billItemId;

    @Schema(description = "结算项值")
    private String billItemValue = STR_ZERO;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}
