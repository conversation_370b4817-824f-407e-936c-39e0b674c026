package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体审核记录更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditLogUpdateReqVO extends SpMainInfoAuditLogBaseVO {

    @Schema(description = "id", required = true, example = "7807")
    @NotNull(message = "id不能为空")
    private Long id;

}
