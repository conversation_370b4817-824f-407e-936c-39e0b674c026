package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店渠道配置信息表日志更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigLogUpdateReqVO extends SpStoreChannelConfigLogBaseVO {

    @Schema(description = "ID", required = true, example = "11741")
    @NotNull(message = "ID不能为空")
    private Long id;

}
