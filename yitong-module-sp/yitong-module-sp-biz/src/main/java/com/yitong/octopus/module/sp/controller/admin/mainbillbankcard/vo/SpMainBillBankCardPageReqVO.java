package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体结算银行卡分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainBillBankCardPageReqVO extends PageParam {

    @Schema(description = "服务商ID", example = "19184")
    private Long spId;

    @Schema(description = "门店ID", example = "19184")
    private Long storeId;

    @Schema(description = "账户类型", example = "2")
    private Integer type;

    @Schema(description = "银行卡类型", example = "2")
    private Integer cardType;

    @Schema(description = "银行开户名", example = "赵六")
    private String bankAccountName;

    @Schema(description = "银行开户账号")
    private String bankNumber;

    @Schema(description = "开户支行", example = "张三")
    private String subBankName;

    @Schema(description = "开户银行所在省id", example = "7861")
    private Integer bankProvinceId;

    @Schema(description = "开户银行所在市id", example = "32471")
    private Integer bankCityId;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
