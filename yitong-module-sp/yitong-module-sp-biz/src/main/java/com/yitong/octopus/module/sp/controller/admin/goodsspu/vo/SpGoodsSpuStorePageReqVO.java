package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import javax.validation.constraints.NotNull;

import com.yitong.octopus.framework.common.pojo.PageParam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品spu可用门店分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuStorePageReqVO extends PageParam {
    
    @Schema(description = "商品ID", example = "13133")
    @NotNull
    private Long spuId;
    
    
    private Long spId;

}
