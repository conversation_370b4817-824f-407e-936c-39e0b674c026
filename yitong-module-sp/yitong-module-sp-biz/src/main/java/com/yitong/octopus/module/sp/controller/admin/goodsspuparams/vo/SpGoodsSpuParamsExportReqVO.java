package com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品spu Excel 导出 Request VO，参数和 SpGoodsSpuParamsPageReqVO 是一致的")
@Data
public class SpGoodsSpuParamsExportReqVO {

    @Schema(description = "主体Id", example = "29818")
    private Long spId;

    @Schema(description = "商家门店Id", example = "17773")
    private Long storeId;

    @Schema(description = "参数名称", example = "芋艿")
    private String paramName;

    @Schema(description = "参数值")
    private String paramValue;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
