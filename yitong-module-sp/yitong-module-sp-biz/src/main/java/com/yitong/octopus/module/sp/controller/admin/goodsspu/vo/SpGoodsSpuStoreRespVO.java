package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品所属的门店
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpGoodsSpuStoreRespVO {

	@Schema(description = "门店Id")
	private Long storeId;

	@Schema(description = "门店名称")
	private String storeName;
	
	@Schema(description = "可预约数（仅在商品需要预约时有效)， -1表示无限制")
	private Integer bookingQuantity;
	
	@Schema(description = "已预约数(仅在商品需要预约时有效）")
	private Integer bookingedQuantity;

}
