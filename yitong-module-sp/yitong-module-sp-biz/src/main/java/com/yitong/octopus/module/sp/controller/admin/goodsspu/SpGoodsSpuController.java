package com.yitong.octopus.module.sp.controller.admin.goodsspu;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils;
import com.yitong.octopus.framework.excel.core.util.ExcelImportRespVO;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.sp.api.store.vo.SpGoodsSpuStoreVO;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.dto.SpGoodsSpuChannelConfigPageVO;
import com.yitong.octopus.module.sp.service.goodsspu.channel.SpGoodsSpuChannel;
import com.yitong.octopus.module.sp.service.goodsspu.channel.vo.SpuChannelShelveReq;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.dto.SpuChannelShelveXhsMIniAppReq;
import com.yitong.octopus.module.sp.service.goodsspu.channel.vo.SpuChannelSyncReq;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.*;
import com.yitong.octopus.module.sp.service.goodsspu.channel.vo.SpuChannelSyncXhsMiniAppReq;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoWithOwnerRespVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoReqVO;
import com.yitong.octopus.module.sp.convert.goodsspubillitem.SpGoodsSpuBillItemConvert;
import com.yitong.octopus.module.sp.convert.maininfo.SpMainInfoConvert;
import com.yitong.octopus.module.sp.dal.dataobject.goodssku.SpGoodsSkuDO;
import com.yitong.octopus.module.sp.dal.mysql.storeinfo.SpStoreInfoMapper;
import com.yitong.octopus.module.sp.enums.CouponUseDateTypeEnum;
import com.yitong.octopus.module.sp.service.goodsspu.SpGoodsSpuXhsMiniAppService;
import com.yitong.octopus.module.sp.service.goodsspubilllitem.SpGoodsSpuBillItemService;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.DateDuration;
import com.yitong.octopus.module.sp.convert.goodsspu.SpGoodsSpuChannelConvert;
import com.yitong.octopus.module.sp.convert.goodsspu.SpGoodsSpuConvert;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspu.SpGoodsSpuChannelDO;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspu.SpGoodsSpuDO;
import com.yitong.octopus.module.sp.dal.mysql.goodsspu.SpGoodsSpuChannelMapper;
import com.yitong.octopus.module.sp.service.goodssku.SpGoodsSkuService;
import com.yitong.octopus.module.sp.service.goodsspu.SpGoodsSpuService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 商品spu")
@RestController
@RequestMapping("/sp/goods-spu")
@Validated
@Slf4j
public class SpGoodsSpuController {

	@Lazy
	@Resource
	private SpGoodsSpuService spGoodsSpuService;

	@Lazy
	@Resource
	private SpGoodsSkuService spGoodsSkuService;

	@Lazy
	@Resource
	private SpGoodsSpuChannelMapper spSpuChannelMapper;

	@Lazy
	@Resource
	private List<SpGoodsSpuChannel> spGoodsSpuChannelList;

	@Lazy
	@Resource
	private SpMainInfoService spMainInfoService;

	@Lazy
	@Resource
	private SpStoreInfoMapper spStoreInfoMapper;

	@Lazy
	@Resource
	private SpGoodsSpuBillItemService spGoodsSpuBillItemService;

	@PostMapping("/create")
	@Operation(summary = "创建商品spu")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:create')")
	public CommonResult<Long> createGoodsSpu(@Valid @RequestBody SpGoodsSpuCreateReqVO createReqVO) {
		return success(spGoodsSpuService.createGoodsSpu(createReqVO));
	}

	@PutMapping("/update")
	@Operation(summary = "更新商品spu")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update')")
	public CommonResult<Boolean> updateGoodsSpu(@Valid @RequestBody SpGoodsSpuUpdateReqVO updateReqVO) {
		spGoodsSpuService.updateGoodsSpu(updateReqVO);
		return success(true);
	}

	@DeleteMapping("/delete")
	@Operation(summary = "删除商品spu")
	@Parameter(name = "id", description = "编号", required = true)
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:delete')")
	public CommonResult<Boolean> deleteGoodsSpu(@RequestParam("id") Long id) {
		spGoodsSpuService.deleteGoodsSpu(id);
		return success(true);
	}

	@GetMapping("/get")
	@Operation(summary = "获得商品spu")
	@Parameter(name = "id", description = "编号", required = true, example = "1024")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:query')")
	public CommonResult<SpGoodsSpuRespVO> getGoodsSpu(@RequestParam("id") Long id) {
		SpGoodsSpuDO spu = spGoodsSpuService.getGoodsSpu(id);
		SpGoodsSpuRespVO vo = BeanUtil.toBean(spu,SpGoodsSpuRespVO.class);
		vo.setMainImage(spu.getMainImg());
		vo.setRank(spu.getRating());
		vo.setLabels(spu.getGoodLabels());
		vo.setOnlineStatus(spu.getOnShelve());
		vo.setRotationImages(spu.getRotationImg());
		vo.setSoldDate(new DateDuration(LocalDateTimeUtils.initDayStart(spu.getSoldDateFrom()), LocalDateTimeUtils.initDayEnd(spu.getSoldDateTo())));
		vo.setOnlineStatus(spu.getOnShelve());
		vo.setTotalBuyNum(spu.getTotalBuyNum());
		vo.setSkus(spGoodsSkuService.getGoodsSkuListBySpuId(spu.getId()));
		vo.setStoreIds(spStoreInfoMapper.selectStoreInfoBySpuId(id).stream().map(s -> new SpStoreInfoReqVO(s.getId(),s.getStoreName()))
				.collect(Collectors.toList()));
		List<SpGoodsSpuChannelDO> list = spSpuChannelMapper.selectBySpuId(id);
		vo.setChannels(SpGoodsSpuChannelConvert.INSTANCE.convert(list));
		// 增加归属服务商/商家
		vo.setSpMainInfoRespVO(SpMainInfoConvert.INSTANCE.convert(spMainInfoService.getMainInfo(vo.getSpId())));
		//结算信息
		vo.setBillItems(SpGoodsSpuBillItemConvert.INSTANCE.convertList(spGoodsSpuBillItemService.getGoodsSpuBillItemListBySpuId(vo.getId())));
		return success(vo);
	}

	@GetMapping("/list")
	@Operation(summary = "获得商品spu列表")
	@Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:query')")
	public CommonResult<List<SpGoodsSpuRespVO>> getGoodsSpuList(@RequestParam("ids") Collection<Long> ids) {
		List<SpGoodsSpuDO> list = spGoodsSpuService.getGoodsSpuList(ids);
		return success(SpGoodsSpuConvert.INSTANCE.convertList(list));
	}

	@GetMapping("/page")
	@Operation(summary = "获得商品spu分页")
	@PreAuthenticated
	public CommonResult<PageResult<SpGoodsSpuPageVO>> getGoodsSpuPage(@Valid SpGoodsSpuPageReqVO pageVO) {
		PageResult<SpGoodsSpuDO> pageResult = spGoodsSpuService.getGoodsSpuPage(pageVO);
		List<SpGoodsSpuPageVO> list = pageResult.getList().stream().map(spu -> {
			SpGoodsSpuPageVO vo = BeanUtil.toBean(spu,SpGoodsSpuPageVO.class);
			vo.setSoldDate(new DateDuration(spu.getSoldDateFrom(), spu.getSoldDateTo()));
			vo.setMainImage(spu.getMainImg());
			vo.setRank(spu.getRating());
			vo.setLabels(spu.getGoodLabels());
			vo.setOnlineStatus(spu.getOnShelve());
			vo.setRotationImages(spu.getRotationImg());
			vo.setSkus(spGoodsSkuService.getGoodsSkuListBySpuId(spu.getId()));
			vo.setTotalBuyNum(spu.getTotalBuyNum());
			SpMainInfoWithOwnerRespVO spMainInfo = spMainInfoService.getMainInfoWithOwnerById(spu.getSpId());
			if (ObjectUtil.isNotNull(spMainInfo)){
				vo.setSpName(spMainInfo.getSpName());
				vo.setSpInfo(spMainInfo);
			}
			vo.setBillItems(SpGoodsSpuBillItemConvert.INSTANCE.convertList(spGoodsSpuBillItemService.getGoodsSpuBillItemListBySpuId(vo.getId())));
			return vo;
		}).collect(Collectors.toList());

		return success(new PageResult<>(list, pageResult.getTotal()));
	}


	@GetMapping("/page-sample")
	@Operation(summary = "获得商品spu精简分页")
	@PreAuthenticated
	public CommonResult<PageResult<SpGoodsSpuPageVO>> getGoodsSpuPage(@Valid SpGoodsSpuPageSampleReqVO pageVO) {
		PageResult<SpGoodsSpuDO> pageResult = spGoodsSpuService.getGoodsSpuPage(BeanUtil.toBean(pageVO,SpGoodsSpuPageReqVO.class));
		List<SpGoodsSkuDO> skuList = spGoodsSkuService.getSkuListBySpuId(pageResult.getList().stream().map(SpGoodsSpuDO::getId).collect(Collectors.toList()));
		Map<Long,List<SpGoodsSkuDO>> goodSkuMap = skuList.stream().collect(Collectors.groupingBy(SpGoodsSkuDO::getSpuId));
		List<SpGoodsSpuPageVO> list = pageResult.getList().stream().map(spu -> {
			SpGoodsSpuPageVO vo = BeanUtil.toBean(spu,SpGoodsSpuPageVO.class);
			vo.setSoldDate(new DateDuration(spu.getSoldDateFrom(), spu.getSoldDateTo()));
			vo.setMainImage(spu.getMainImg());
			vo.setRank(spu.getRating());
			vo.setLabels(spu.getGoodLabels());
			vo.setOnlineStatus(spu.getOnShelve());
			vo.setRotationImages(spu.getRotationImg());
			if (goodSkuMap.containsKey(vo.getId())){
				vo.setSkus(BeanUtil.copyToList(goodSkuMap.get(vo.getId()),SpGoodsSkuRespVO.class));
			}
			return vo;
		}).collect(Collectors.toList());
		return success(new PageResult<>(list, pageResult.getTotal()));
	}

	@GetMapping("/get-count")
	@Operation(summary = "获得商品 SPU 分页 tab count")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:query')")
	public CommonResult<Map<Integer, Long>> getSpuCount(@Valid SpGoodsSpuPageReqVO pageVO) {
		return success(spGoodsSpuService.getTabsCount(pageVO));
	}

	@PutMapping("/audit")
	@Operation(summary = "审核商品 SPU")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:audit')")
	public CommonResult<Boolean> auditSpu(@Valid @RequestBody SpGoodsSpuAuditReqVO updateReqVO) {
		spGoodsSpuService.auditSpu(updateReqVO);
		return success(true);
	}

	@PutMapping("/update-status")
	@Operation(summary = "更新商品 SPU Status")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-sale')")
	public CommonResult<Boolean> updateStatus(@Valid @RequestBody SpGoodsSpuUpdateStatusReqVO updateReqVO) {
		spGoodsSpuService.updateSpuStatus(updateReqVO);
		return success(true);
	}

	@PutMapping("/update-stock")
	@Operation(summary = "更新商品 SPU 库存")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-stock')")
	public CommonResult<Boolean> updateStock(@Valid @RequestBody SpGoodsSpuUpdateStockReqVO updateReqVO) {
		spGoodsSpuService.updateSpuStock(updateReqVO);
		return success(true);
	}

	@GetMapping("/export-excel")
	@Operation(summary = "导出商品spu Excel")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:export')")
	public void exportGoodsSpuExcel(@Valid SpGoodsSpuPageReqVO pageReqVO, HttpServletResponse response)
			throws IOException {
		List<SpGoodsSpuDO> list = spGoodsSpuService.getGoodsSpuList(pageReqVO);
		// 导出 Excel
		List<SpGoodsSpuExcelVO> datas = SpGoodsSpuConvert.INSTANCE.convertList02(list);
		ExcelUtils.write(response, "商品spu.xls", "数据", SpGoodsSpuExcelVO.class, datas);
	}

	@GetMapping("/channel-page")
	@Operation(summary = "获得服务商渠道配置信息列表分页")
	@PreAuthenticated
	public CommonResult<PageResult<SpGoodsSpuChannelVo>> getChannelConfigPageBySpId(@Valid SpGoodsSpuChannelConfigPageVO pageVO) {
		PageResult<SpGoodsSpuChannelVo> page = spGoodsSpuService.getChannelConfigPageBySpId(pageVO);
		return success(page);
	}

	@PostMapping("/channel-sync")
	@Operation(summary = "商品渠道同步")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-sync')")
	public CommonResult<Boolean> channelSync(@Valid @RequestBody SpuChannelSyncReq req) {
		spGoodsSpuChannelList.forEach(channel -> channel.sync(req));
		return success(true);
	}

	@GetMapping("/get-import-goods-sold-date-template")
	@Operation(summary = "获得导入商品售卖时间设置模板")
	public void importSoldDateTemplate(HttpServletResponse response) throws IOException {
		// 手动创建导出 demo
		LocalDateTime from = LocalDateTime.now();
		LocalDateTime to = from.plusDays(1);
		List<SpGoodsSpuSoldDateImportExcelVO> list = Arrays.asList(
				SpGoodsSpuSoldDateImportExcelVO.builder().spuId(1000000000L).channelCode("XHS").soldDateFrom(from).soldDateTo(to).build(),
				SpGoodsSpuSoldDateImportExcelVO.builder().spuId(1000000001L).channelCode("XHS").soldDateFrom(from).soldDateTo(to).build()
		);
		// 输出
		ExcelUtils.write(response, "商品售卖时间导入模板.xls", "商品售卖时间列表", SpGoodsSpuSoldDateImportExcelVO.class, list);
	}

	@PostMapping("/goods-sold-date-import")
	@Operation(summary = "导入商品售卖时间")
	@Parameters({
			@Parameter(name = "file", description = "Excel 文件", required = true),
			@Parameter(name = "syncChannel", description = "是否支持推送渠道，默认为 false", example = "true")
	})
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-sold-date')")
	public CommonResult<SpGoodsSpuImportRespVO> importSoldDateExcel(@RequestParam("file") MultipartFile file,
																	@RequestParam(value = "syncChannel", required = false, defaultValue = "false") Boolean syncChannel) throws Exception {
		List<SpGoodsSpuSoldDateImportExcelVO> list = ExcelUtils.read(file, SpGoodsSpuSoldDateImportExcelVO.class);
		return success(spGoodsSpuService.importGoodsSoldDate(list, syncChannel));
	}

	@GetMapping("/get-import-goods-redeem-date-template")
	@Operation(summary = "获得导入商品有效期设置模板")
	public void importRedeemDateTemplate(HttpServletResponse response) throws IOException {
		// 手动创建导出 demo
		LocalDateTime from = LocalDateTime.now();
		LocalDateTime to = from.plusDays(30);
		List<SpGoodsSpuRedeemDateImportExcelVO> list = Arrays.asList(
				SpGoodsSpuRedeemDateImportExcelVO.builder().spuId("1000000000").channelCode("XHS").useDateType(CouponUseDateTypeEnum.DAY_DURATION.getType()).dayDuration(30).build(),
				SpGoodsSpuRedeemDateImportExcelVO.builder().spuId("1000000001").channelCode("XHS").useDateType(CouponUseDateTypeEnum.DAY_RANGE.getType()).startDate(from).endDate(to).build()
		);
		// 输出
		ExcelUtils.write(response, "商品有效期列表导入模板.xls", "商品有效期列表", SpGoodsSpuRedeemDateImportExcelVO.class, list);
	}

	@PostMapping("/goods-redeem-date-import")
	@Operation(summary = "导入商品有效期")
	@Parameters({
			@Parameter(name = "file", description = "Excel 文件", required = true),
			@Parameter(name = "syncChannel", description = "是否支持推送渠道，默认为 false", example = "true")
	})
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-redeem-date')")
	public CommonResult<SpGoodsSpuImportRespVO> importRedeemDateExcel(@RequestParam("file") MultipartFile file,
																	@RequestParam(value = "syncChannel", required = false, defaultValue = "false") Boolean syncChannel) throws Exception {
		List<SpGoodsSpuRedeemDateImportExcelVO> list = ExcelUtils.read(file, SpGoodsSpuRedeemDateImportExcelVO.class);
		return success(spGoodsSpuService.importGoodsRedeemDate(list, syncChannel));
	}

	@GetMapping("/get-import-goods-stock-template")
	@Operation(summary = "获得导入商品库存设置模板")
	public void importTemplate(HttpServletResponse response) throws IOException {
		// 手动创建导出 demo
		List<SpGoodsSpuStockImportExcelVO> list = Arrays.asList(
				SpGoodsSpuStockImportExcelVO.builder().spuId(1000000000L).channelCode("XHS").storeNum(100L).build(),
				SpGoodsSpuStockImportExcelVO.builder().spuId(1000000001L).channelCode("XHS").storeNum(99L).build()
		);
		// 输出
		ExcelUtils.write(response, "商品库存导入模板.xls", "商品库存列表", SpGoodsSpuStockImportExcelVO.class, list);
	}

	@PostMapping("/goods-stock-import")
	@Operation(summary = "导入商品库存")
	@Parameters({
			@Parameter(name = "file", description = "Excel 文件", required = true),
			@Parameter(name = "syncChannel", description = "是否支持推送渠道，默认为 false", example = "true")
	})
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-stock')")
	public CommonResult<SpGoodsSpuImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
															@RequestParam(value = "syncChannel", required = false, defaultValue = "false") Boolean syncChannel) throws Exception {
		List<SpGoodsSpuStockImportExcelVO> list = ExcelUtils.read(file, SpGoodsSpuStockImportExcelVO.class);
		return success(spGoodsSpuService.importGoodsStock(list, syncChannel));
	}

	@GetMapping("/get-import-sync-template")
	@Operation(summary = "获得商品推送模板")
	public void importSyncTemplate(HttpServletResponse response) throws IOException {
		// 手动创建导出 demo
		List<SpGoodsSpuSyncImportExcelVO> list = Arrays.asList(
				SpGoodsSpuSyncImportExcelVO.builder().spuId(1000000000L).channelCode("XHS").build(),
				SpGoodsSpuSyncImportExcelVO.builder().spuId(1000000001L).channelCode("XHS").build()
		);
		// 输出
		ExcelUtils.write(response, "商品推送导入模板.xls", "商品列表", SpGoodsSpuSyncImportExcelVO.class, list);
	}

	@PostMapping("/goods-sync-import")
	@Operation(summary = "导入商品推送")
	@Parameters({
			@Parameter(name = "file", description = "Excel 文件", required = true),
	})
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:channel-sync')")
	public CommonResult<SpGoodsSpuImportRespVO> importExcel(@RequestParam("file") MultipartFile file) throws Exception {
		List<SpGoodsSpuSyncImportExcelVO> list = ExcelUtils.read(file, SpGoodsSpuSyncImportExcelVO.class);
		return success(spGoodsSpuService.importGoodsSync(list));
	}

	@PostMapping("/channel-sync-stock")
	@Operation(summary = "商品渠道库存同步")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-stock')")
	public CommonResult<Boolean> channelSyncStock(@Valid @RequestBody SpuChannelSyncReq req) {
		spGoodsSpuChannelList.forEach(channel -> channel.syncStock(req));
		return success(true);
	}

	@PostMapping("/channel-sync-shelve")
	@Operation(summary = "商品渠道上下架同步")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-shelve')")
	public CommonResult<Boolean> channelSyncShelve(@Valid @RequestBody SpuChannelShelveReq req) {
		spGoodsSpuChannelList.forEach(channel -> channel.shelve(req));
		return success(true);
	}

	@GetMapping("/stores")
	@Operation(summary = "商品适用门店")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:store')")
	public CommonResult<List<SpGoodsSpuStoreVO>> getGoodsSpuStore(@RequestParam("id") Long id) {
		return success(spGoodsSpuService.getGoodsSpuStoreListBySpuId(id));
	}

	@PutMapping("/update-cooperates")
	@Operation(summary = "批量更新商品结算信息")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-cooperates')")
	public CommonResult<Boolean> updateGoodsSpuCooperates(@Valid @RequestBody SpGoodsSpuCooperateUpdateReqVO updateReqVO) {
		spGoodsSpuService.updateGoodsSpuCooperates(updateReqVO);
		return success(true);
	}

	/**
	 *服务商/商家 打标签
	 * @param tagRelSaveReqVO
	 * @return
	 */
	@PostMapping("/makeLabel")
	@Operation(summary = "服务商/商家 打标签")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:tags')")
	public CommonResult<Boolean>  makeLabel(@Valid @RequestBody SpGoodsSpuTagRelSaveReqVO tagRelSaveReqVO) {
		spGoodsSpuService.makeLabel(tagRelSaveReqVO);
		return success(true);
	}

	/**
	 *服务商/商家 打标签
	 * @param tagRelSaveReqVO
	 * @return
	 */
	@PostMapping("/batchMakeLabel")
	@Operation(summary = "服务商/商家 批量打标签")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:tags')")
	public CommonResult<Boolean>  batchMakeLabel(@Valid @RequestBody SpGoodsSpuBatchTagRelSaveReqVO tagRelSaveReqVO) {
		spGoodsSpuService.batchMakeLabel(tagRelSaveReqVO);
		return success(true);
	}

	@GetMapping("/tag-list")
	@Operation(summary = "获得商品spu标签列表")
	@Parameter(name = "reqVO", description = "编号列表", required = true)
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:query')")
	public CommonResult<List<PlatformTagGroupVo>> getTagList(@Valid SpGoodsSpuTagReqVO reqVO) {
		List<PlatformTagGroupVo>list = spGoodsSpuService.getTagList(reqVO);
		return success(list);
	}


	/**
	 *服务商/商家属性
	 * @param reqVO
	 * @return
	 */
	@PostMapping("/makeProperty")
	@Operation(summary = "商品Spu打设置属性")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:property')")
	public CommonResult<Boolean>  makeProperty(@Valid @RequestBody SpGoodsSpuPropertySaveReqVO reqVO) {
		spGoodsSpuService.makeProperty(reqVO);
		return success(true);
	}

	/**
	 *服务商/商家属性【批量】
	 * @param reqVO
	 * @return
	 */
	@PostMapping("/batchMakeProperty")
	@Operation(summary = "商品Spu批量属性")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:property')")
	public CommonResult<Boolean>  batchMakeProperty(@Valid @RequestBody SpGoodsSpuBatchPropertySaveReqVO reqVO) {
		spGoodsSpuService.batchProperties(reqVO);
		return success(true);
	}

	@GetMapping("/property-list")
	@Operation(summary = "获得商品Spu属性列表")
	@Parameter(name = "reqVO", description = "编号列表", required = true)
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:property')")
	public CommonResult<List<PlatformPropertyVo>> getPropertyList(@Valid SpGoodsSpuPropertyReqVO reqVO) {
		List<PlatformPropertyVo>list = spGoodsSpuService.getPropertyList(reqVO);
		return success(list);
	}

	@GetMapping("/list-simple")
	@Operation(summary = "获得商品spu列表简化")
	@Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
	@PreAuthenticated
	public CommonResult<List<SpGoodsSpuSampleRespVO>> getGoodsSpuListByIds(@RequestParam("ids") Collection<Long> ids) {
		return success(spGoodsSpuService.getGoodsSpuSampleListByIds(ids));
	}
	
	/**
	 * 配置需预约商品的门店预约限额
	 * 
	 * @param vo
	 */
	@PostMapping("/stores/booking-quantity")
	@Operation(summary = "配置商品门店预约数")
	@PreAuthenticated
	public CommonResult<Boolean> setStoreBookingQuantity(@RequestBody @Valid SpGoodsSpuStoreBookingQuantityVO vo) {
		spGoodsSpuService.setStoreBookingQuantity(vo);
		return success(true);
	}

	@GetMapping("/stores/page")
	@Operation(summary = "商品适用门店分页")
	@PreAuthenticated
	public CommonResult<PageResult<SpGoodsSpuStoreRespVO>> getGoodsSpuStorePage(@Valid SpGoodsSpuStorePageReqVO reqVo) {
		return success(spGoodsSpuService.getGoodsSpuStorePage(reqVo));
	}


	/**
	 * 根据商品生成券码
	 * @param spuId 商品ID
	 */
	@PostMapping("/generator-coupon")
	@Operation(summary = "根据商品生成券码")
	@PreAuthorize("@ss.hasRole('super_admin')")
	public CommonResult<String> generatorCoupon(Long spuId) {
		return success(spGoodsSpuService.generatorCouponBySpuId(spuId));
	}

	// ########################################## 小红书本地生活相关渠道相关 ############################################################

	@GetMapping("/channel-app-page")
	@Operation(summary = "获得服务商渠道配置信息列表分页[分应用]")
	@PreAuthenticated
	public CommonResult<PageResult<SpGoodsSpuChannelAppVo>> getChannelAppConfigAPageBySpId(@Valid SpGoodsSpuChannelConfigPageVO pageVO) {
		PageResult<SpGoodsSpuChannelAppVo> page = spGoodsSpuService.getChannelAppConfigPageBySpId(pageVO);
		return success(page);
	}

	@PostMapping("/channel-sync-xhsminiapp")
	@Operation(summary = "商品渠道同步[小红书本地生活小程序]")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-sync')")
	public CommonResult<Boolean> channelSyncXhsMiniApp(@Valid @RequestBody SpuChannelSyncXhsMiniAppReq req) {
//		spGoodsSpuXhsMiniAppService.upsert(req);
		spGoodsSpuChannelList.forEach(channel -> channel.sync(req));
		return success(true);
	}

	@PostMapping("/channel-sync-shelve-xhsminiapp")
	@Operation(summary = "商品渠道上下架同步[小红书本地生活小程序]")
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update-shelve')")
	public CommonResult<Boolean> channelSyncShelveXhsMiniApp(@Valid @RequestBody SpuChannelShelveReq req) {
//		spGoodsSpuXhsMiniAppService.shelve(req);
		spGoodsSpuChannelList.forEach(channel -> channel.shelve(req));
		return success(true);
	}

	// ########################################## 商品批量更新门店 ############################################################

	@GetMapping("/get-update-store-template")
	@Operation(summary = "获得商品门店模板")
	public void importUpdateSpuStoreTemplate(HttpServletResponse response) throws IOException {
		// 手动创建导出 demo
		List<SpGoodsSpuStoreImportExcelVO> list = Arrays.asList(
				SpGoodsSpuStoreImportExcelVO.builder().storeId(1000000000L).build(),
				SpGoodsSpuStoreImportExcelVO.builder().storeId(1000000001L).build()
		);
		// 输出
		ExcelUtils.write(response, "商品门店导入模板.xls", "商品列表", SpGoodsSpuStoreImportExcelVO.class, list);
	}

	@PostMapping("/goods-update-store-import")
	@Operation(summary = "导入商品门店推送")
	@Parameters({
			@Parameter(name = "file", description = "Excel 文件", required = true),
			@Parameter(name = "isDeleted", description = "是否支持保留之前商品门店关系，默认为 false，保留，true:不保留", example = "true")
	})
	@PreAuthorize("@ss.hasPermission('sp:goods-spu:update')")
	public CommonResult<ExcelImportRespVO> importUpdateSpuStoreExcel(@RequestParam("file") MultipartFile file
			,@RequestParam(value = "isDeleted", required = false, defaultValue = "false") Boolean isDeleted
			,@RequestParam(value = "spuIds") List<Long> spuIds
	) throws Exception {
		List<SpGoodsSpuStoreImportExcelVO> list = ExcelUtils.read(file, SpGoodsSpuStoreImportExcelVO.class);
		return success(spGoodsSpuService.importUpdateSpuStoreExcel(list,isDeleted,spuIds));
	}
}
