package com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 商家门店地图信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoMapBaseVO {

    @Schema(description = "主体Id", example = "1234")
    private Long spId;

    @Schema(description = "商家门店ID", example = "18230")
    private Long storeId;

    @Schema(description = "地图渠道ID", example = "1679")
    private Long mapChannelId;

    @Schema(description = "门店地图ID", required = true, example = "2795")
    @NotNull(message = "门店地图ID不能为空")
    private String mapId;

    @Schema(description = "门店POI")
    private String mapPoi;

    @Schema(description = "门店地图POI链接", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "门店地图POI链接不能为空")
    private String mapPoiUrl;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度", required = true)
    @NotNull(message = "经度不能为空")
    private String longitude;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
