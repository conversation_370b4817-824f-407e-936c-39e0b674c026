package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体平台合同Excel 导出 Request VO，参数和 SpMainPlatformContractPageReqVO 是一致的")
@Data
public class SpMainPlatformContractExportReqVO {

    @Schema(description = "合同类型", example = "1")
    private Integer contractType;

    @Schema(description = "主体ID", example = "28349")
    private Long spId;

    @Schema(description = "主体称", example = "赵六")
    private String spName;

    @Schema(description = "主体类型", example = "1")
    private Integer spType;

    @Schema(description = "联系人姓名", example = "张三")
    private String linkName;

    @Schema(description = "合作开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] coopStartTime;

    @Schema(description = "合作结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] coopEndTime;

    @Schema(description = "合作状态", example = "1")
    private Integer coopStatus;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "是否自动结算", required = true, example = "true")
    private Boolean isAutoSettle;

}
