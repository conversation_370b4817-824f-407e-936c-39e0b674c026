package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体认证信息-个人更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditPersonalUpdateReqVO extends SpMainInfoAuditPersonalBaseVO {

    @Schema(description = "id", required = true, example = "8444")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "法人身份证照片-人像", required = true)
    private String idCardFront;

    @Schema(description = "法人身份证照片-国徽", required = true)
    private String idCardBack;

    @Schema(description = "法人身份证证件有效开始时间")
    private String idCardStartTime;

    @Schema(description = "法人身份证证件有效结束时间")
    private String idCardEndTime;

    /**
     * 法人身份证所在省id
     */
    @Schema(description = "法人身份证所在省id")
    private Integer idCardProvinceId;
    /**
     * 法人身份证所在市id
     */
    @Schema(description = "法人身份证所在市id")
    private Integer idCardCityId;
    /**
     * 法人身份证所在县id
     */
    @Schema(description = "法人身份证所在县id")
    private Integer idCardCountyId;
    /**
     * 法人身份证所在镇id
     */
    @Schema(description = "法人身份证所在镇id")
    private Integer idCardTownId;
    /**
     * 法人身份证所在省
     */
    @Schema(description = "法人身份证所在省")
    private String idCardProvince;
    /**
     * 法人身份证所在市
     */
    @Schema(description = "法人身份证所在市")
    private String idCardCity;
    /**
     * 法人身份证所在县
     */
    @Schema(description = "法人身份证所在县")
    private String idCardCounty;
    /**
     * 法人身份证所在镇
     */
    @Schema(description = "法人身份证所在镇")
    private String idCardTown;
    /**
     * 营业执照详细地址
     */
    @Schema(description = "营业执照详细地址")
    private String idCardAdd;

    @Schema(description = "开户银行所在省id", example = "27259")
    private Integer bankProvinceId;

    @Schema(description = "开户银行所在市id", example = "3448")
    private Integer bankCityId;

    @Schema(description = "开户银行所在县id", example = "8485")
    private Integer bankCountyId;

    @Schema(description = "开户银行所在镇id", example = "6248")
    private Integer bankTownId;

    @Schema(description = "开户银行所在镇")
    private String bankTown;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
