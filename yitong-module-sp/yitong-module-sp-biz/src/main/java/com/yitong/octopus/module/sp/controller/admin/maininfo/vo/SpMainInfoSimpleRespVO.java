package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Schema(description = "管理后台 - 主体基本信息Simple Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SpMainInfoSimpleRespVO {

    @Schema(description = "id", required = true, example = "27033")
    private Long id;

    @Schema(description = "服务商名称", required = true, example = "王五")
    private String spName;

    @Schema(description = "服务商名称简称", required = true, example = "王五")
    private String spShortName;

}
