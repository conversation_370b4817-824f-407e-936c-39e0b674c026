package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo;

import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 主体认证信息-个人 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoAuditPersonalExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体ID")
    private Long spId;

    @ExcelProperty("法人姓名")
    private String legalName;

    @ExcelProperty("法人身份证号")
    private String idCard;

    @ExcelProperty("银行开户名")
    private String bankAccountName;

    @ExcelProperty("银行开户账号")
    private String bankNumber;

    @ExcelProperty("开户银行支行名称")
    private String bankName;

    @ExcelProperty("开户银行所在省")
    private String bankProvince;

    @ExcelProperty("开户银行所在市")
    private String bankCity;

    @ExcelProperty("开户银行所在县")
    private String bankCounty;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
