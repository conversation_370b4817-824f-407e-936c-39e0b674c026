package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 服务商渠道配置信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainChannelConfigRespVO extends SpMainChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "12200")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
