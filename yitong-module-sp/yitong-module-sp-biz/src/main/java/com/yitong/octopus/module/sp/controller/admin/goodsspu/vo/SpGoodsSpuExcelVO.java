package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import lombok.*;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_GOODS_SPU;
import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_GOODS_SPU_SETTLE_TYPE;


/**
 * 商品spu Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpGoodsSpuExcelVO {

    @ExcelProperty("主键")
    private String id;

    @ExcelProperty("商品评级")
    private Integer rating;

    @ExcelProperty("商品标签")
    private List<String> goodLabels;

    @ExcelProperty("商品主图")
    private String mainImg;

    @ExcelProperty("首次动销时间")
    private LocalDateTime saleStart;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("商品销量")
    private Integer salesCount;

    @ExcelProperty("虚拟销量")
    private Integer virtualSalesCount;

    @ExcelProperty("商品点击量")
    private Integer clickCount;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("主体Id")
    private String spId;

    @ExcelProperty("商家门店Id")
    private String storeId;

    @ExcelProperty("类目")
    private String categoryId;

    @ExcelProperty("商品品牌编号")
    private String brandId;

    @ExcelProperty("商品名称")
    private String fullName;

    @ExcelProperty("商品简称")
    private String shortName;

    @ExcelProperty(value = "商品状态", converter = DictConvert.class)
    @DictFormat(SP_GOODS_SPU) // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer status;

    /**
     * 收款方式:1 统一收款 2 区域账户收款 3 分店收款
     */
    @ExcelProperty(value = "收款方式", converter = DictConvert.class)
    @DictFormat(SP_GOODS_SPU_SETTLE_TYPE) // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer settleType;

    @ExcelProperty("商品使用规则")
    private UseRuleVo useRule;

    @ExcelProperty("商品售后规则")
    private String afterSaleRule;

}
