package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.xhsminiapp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 服务商渠道配置信息创【小红书本地生活小程序】 Request VO")
@Data
@ToString(callSuper = true)
public class SpMainXhsMiniAppChannelConfigAuditReqVO {
    /**
     * appId
     */
    @Schema(description = "id", example = "1")

    private Long id;

    @Schema(description = "渠道类目ID", required = true, example = "xx2220xx")
    @NotEmpty(message = "渠道类目ID不能为空")
    private String categoryId;

    @Schema(description = "渠道类目名称", required = true, example = "xx2220xx")
    @NotEmpty(message = "渠道类目名称不能为空")
    private String categoryName;

    /**
     * 小红书渠道字典
     * {
     *    xhsUserId: 111,
     *    xhsUserName: '小红书',
     * }
     */
    @Schema(description = "配置信息", required = true, example = "{name:'张三',age:18}")
    @NotEmpty(message = "配置信息不能为空")
    private String configValue;

}
