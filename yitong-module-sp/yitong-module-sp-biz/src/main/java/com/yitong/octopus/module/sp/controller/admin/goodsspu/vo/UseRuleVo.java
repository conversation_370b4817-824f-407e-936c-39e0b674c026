package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.DateDuration;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.TimeDuration;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.UseDateDuration;

import lombok.Data;

@Data
public class UseRuleVo {

	@NotNull(message = "validDate不能为空")
	@Valid
	private UseDateDuration validDate;

	private Integer appointment; // 预约类型 0-无需预约

	private Integer appointmentType; // 0-天  1-小时

	private Integer appointmentDuration; // 提前X预约

	@Valid
	private UseDateTime noUseDateTime;

	@Valid
	private UseDateTime useDateTime;

	private Integer refundPolicy; // 1-未核销随时退,过期自动退

	private List<String> others;

	@Data
	public static class UseDateTime {

		private Integer allHolidays;

		private Integer[] weekdays;

		@Valid
		private List<DateDuration> dateDurations;

		@Valid
		private List<TimeDuration> timeDurations;
	}

}
