package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商家门店渠道配置信息表日志 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigLogRespVO extends SpStoreChannelConfigLogBaseVO {

    @Schema(description = "ID", required = true, example = "11741")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
