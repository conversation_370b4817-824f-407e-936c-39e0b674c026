package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 服务商渠道配置信息 Excel 导出 Request VO，参数和 SpChannelConfigPageReqVO 是一致的")
@Data
public class SpMainChannelConfigExportReqVO {

    @Schema(description = "审核状态 0 待审核 1 未通过 2 已通过", example = "1")
    private Integer auditStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
