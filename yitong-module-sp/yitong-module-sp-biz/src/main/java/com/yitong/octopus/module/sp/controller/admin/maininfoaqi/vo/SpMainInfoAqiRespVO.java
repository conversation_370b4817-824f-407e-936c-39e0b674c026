package com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体资质信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAqiRespVO extends SpMainInfoAqiBaseVO {

    @Schema(description = "id", required = true, example = "15165")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
