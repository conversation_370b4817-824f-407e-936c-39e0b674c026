package com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商家门店营业时间 Excel 导出 Request VO，参数和 SpStoreInfoOpeningHoursPageReqVO 是一致的")
@Data
public class SpStoreInfoOpeningHoursExportReqVO {

    @Schema(description = "主体Id", example = "1965")
    private Long spId;

    @Schema(description = "门店Id", example = "31397")
    private Long storeId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
