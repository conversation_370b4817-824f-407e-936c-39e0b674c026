package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 商户子账号 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoUserExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("手机号")
    private String mobile;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("状态")
    private Integer status;

    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat("sp_main_info_user_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer type;

    @ExcelProperty("门店ID")
    private Long storeId;

    @ExcelProperty("创建时间")
    private LocalDateTime crtTime;

}
