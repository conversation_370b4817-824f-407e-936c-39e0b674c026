package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.yitong.octopus.module.sp.api.sku.vo.SkuPackageContent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 商品sku Base VO，提供给添加、修改、详细的子 VO 使用 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class SpGoodsSkuBaseVO {
	
	@Schema(description = "主体Id", example = "19437")
	private Long spId;

	@Schema(description = "商品spuId", required = true, example = "2496")
	private Long spuId;

	@Schema(description = "SKU名称", required = true, example = "14979")
	@Size(max = 32, message = "SKU名称长度不能超过32字符")
	private String name;

	@Schema(description = "市场价", required = true)
	@NotNull(message = "市场价不能为空")
	private Long marketPrice;

	@Schema(description = "售价", required = true)
	@NotNull(message = "售价不能为空")
	private Long salePrice;

	@Schema(description = "售价")
	private Long settleAmount;

	private Integer billStock;

	@Schema(description = "消费者每单的购买数量")
	private Integer purchaseLimit = 1;

	@Schema(description = "备注", example = "你说的对")
	private String remark;

	@Schema(description = "套餐内容", required = true)
	@NotNull(message = "套餐内容不能为空")
	@Valid
	private List<SkuPackageContent> packages;

	@Schema(description = "SKU图片")
	private String[] images;

	@Schema(description = "适用人数")
	private Integer userNumLimit;

}
