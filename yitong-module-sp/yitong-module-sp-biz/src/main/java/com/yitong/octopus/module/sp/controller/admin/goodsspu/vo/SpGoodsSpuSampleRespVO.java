package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo.SpGoodsSpuBilItemRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoRespVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 商品spu Response VO")
@Data
@ToString(callSuper = true)
public class SpGoodsSpuSampleRespVO {

    @Schema(description = "主键", required = true, example = "6521")
    private Long id;

    @NotEmpty(message = "SPU全称不能为空")
    private String fullName;

    @NotEmpty(message = "shotName不能为空")
    private String shortName;

}
