package com.yitong.octopus.module.sp.controller.admin.storeinfogallery.dto;

import com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo.SpStoreInfoGalleryBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商家门店图片 Response Dto")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoGalleryDto extends SpStoreInfoGalleryBaseVO {

    @Schema(description = "id", required = true, example = "11762")
    private Long id;

}
