package com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 商家门店图片 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoGalleryBaseVO {

    @Schema(description = "主体Id", required = true, example = "7591")
    @NotNull(message = "主体Id不能为空")
    private Long spId;

    @Schema(description = "商家门店Id", required = true, example = "2596")
    @NotNull(message = "商家门店Id不能为空")
    private Long storeId;

    @Schema(description = "图片类型", required = true, example = "1")
    @NotNull(message = "图片类型不能为空")
    private Integer imageType;

    @Schema(description = "图片地址", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "图片地址不能为空")
    private String imageUrl;

    @Schema(description = "备注", required = true, example = "你猜")
    @NotNull(message = "备注不能为空")
    private String remark;

}
