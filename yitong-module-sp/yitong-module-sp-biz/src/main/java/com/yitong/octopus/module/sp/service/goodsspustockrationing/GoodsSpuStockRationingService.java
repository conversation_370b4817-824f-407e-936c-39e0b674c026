package com.yitong.octopus.module.sp.service.goodsspustockrationing;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.module.sp.dal.dto.GoodsSpuStockRationingDto;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品库存供给 Service 接口
 *
 * <AUTHOR>
 */
public interface GoodsSpuStockRationingService extends IService<GoodsSpuStockRationingDO> {

    /**
     * 创建商品库存供给
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGoodsSpuStockRationing(@Valid GoodsSpuStockRationingSaveReqVO createReqVO);

    /**
     * 更新商品库存供给
     *
     * @param updateReqVO 更新信息
     */
    void updateGoodsSpuStockRationing(@Valid GoodsSpuStockRationingSaveReqVO updateReqVO);

    /**
     * 删除商品库存供给
     *
     * @param id 编号
     */
    void deleteGoodsSpuStockRationing(Long id);

    /**
     * 获得商品库存供给
     *
     * @param id 编号
     * @return 商品库存供给
     */
    GoodsSpuStockRationingDO getGoodsSpuStockRationing(Long id);

    /**
     * 获得商品库存供给分页
     *
     * @param pageReqVO 分页查询
     * @return 商品库存供给分页
     */
    PageResult<GoodsSpuStockRationingDO> getGoodsSpuStockRationingPage(GoodsSpuStockRationingPageReqVO pageReqVO);

    /**
     * 获取当前日期的商品库存配给
     * @return
     */
    List<GoodsSpuStockRationingDto> getGoodsSpuStockRationingByCurrent();

    /**
     * 获取在当前时间内支持动态库存的供给计划
     * @return
     */
    List<GoodsSpuStockRationingDO> getGoodsSpuDynamicStockRationingByCurrent();

    /**
     * 改变商品配给状态
     * @return
     */
    int changeGoodsSpuStockRationingStatus();

    /**
     * 初始化动态库存
     */
    void initDynamicStock();
}