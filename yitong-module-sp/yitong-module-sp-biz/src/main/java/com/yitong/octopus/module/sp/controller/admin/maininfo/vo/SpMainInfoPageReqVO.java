package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 主体分页查询
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoPageReqVO extends PageParam {

    @Schema(description = "服务商/商家Id", example = "王五")
    private Long id;

    @Schema(description = "服务商名称", example = "王五")
    private String spName;

    @Schema(description = "模糊查询名称，品牌等", example = "王五")
    private String searchParams;

    @Schema(description = "服务商品牌名称", example = "王五")
    private String spShortName;

    @Schema(description = "主体类型", example = "1")
    private Integer spType;

    @Schema(description = "所在省id", example = "7635")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "772")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "13418")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "6052")
    private Integer spTownId;

    @Schema(description = "状态 ", example = "2")
    private Integer spStatus;

    @Schema(description = "所属服务商Id ", example = "2")
    private Long ownerSpId;

    @Schema(description = "联系人姓名", example = "王五")
    private String linkName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    private Integer poiAuditStatus;

}
