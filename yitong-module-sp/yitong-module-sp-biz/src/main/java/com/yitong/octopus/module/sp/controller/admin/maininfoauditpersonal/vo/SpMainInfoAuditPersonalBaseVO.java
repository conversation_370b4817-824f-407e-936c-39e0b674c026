package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
* 主体认证信息-个人 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class SpMainInfoAuditPersonalBaseVO {

    @Schema(description = "主体ID", example = "27254")
    private Long spId;

    @Schema(description = "法人姓名", required = true, example = "王五")
    private String legalName;

    @Schema(description = "法人身份证号", required = true)
    private String idCard;

    @Schema(description = "银行开户名", required = true, example = "赵六")
    private String bankAccountName;

    @Schema(description = "银行开户账号", required = true)
    private String bankNumber;

    @Schema(description = "开户银行支行名称", example = "李四")
    private String bankName;

    @Schema(description = "开户银行所在省")
    private String bankProvince;

    @Schema(description = "开户银行所在市")
    private String bankCity;

    @Schema(description = "开户银行所在县")
    private String bankCounty;

}
