package com.yitong.octopus.module.sp.controller.admin.goodssku;

import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.convert.goodssku.SpGoodsSkuConvert;
import com.yitong.octopus.module.sp.dal.dataobject.goodssku.SpGoodsSkuDO;
import com.yitong.octopus.module.sp.service.goodssku.SpGoodsSkuService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "管理后台 - 商品sku")
@RestController
@RequestMapping("/sp/goods-sku")
@Validated
public class SpGoodsSkuController {

    @Resource
    private SpGoodsSkuService goodsSkuService;

    @PostMapping("/create")
    @Operation(summary = "创建商品sku")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:create')")
    public CommonResult<Long> createGoodsSku(@Valid @RequestBody SpGoodsSkuBaseVO createReqVO) {
        return success(goodsSkuService.createGoodsSku(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品sku")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:update')")
    public CommonResult<Boolean> updateGoodsSku(@Valid @RequestBody SpGoodsSkuUpdateReqVO updateReqVO) {
        goodsSkuService.updateGoodsSku(updateReqVO.getId(), updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品sku")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:delete')")
    public CommonResult<Boolean> deleteGoodsSku(@RequestParam("id") Long id) {
        goodsSkuService.deleteGoodsSku(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品sku")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:query')")
    public CommonResult<SpGoodsSkuRespVO> getGoodsSku(@RequestParam("id") Long id) {
        SpGoodsSkuDO goodsSku = goodsSkuService.getGoodsSku(id);
        return success(SpGoodsSkuConvert.INSTANCE.convert(goodsSku));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品sku列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:query')")
    public CommonResult<List<SpGoodsSkuRespVO>> getGoodsSkuList(@RequestParam("ids") Collection<Long> ids) {
        List<SpGoodsSkuDO> list = goodsSkuService.getGoodsSkuList(ids);
        return success(SpGoodsSkuConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品sku分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:query')")
    public CommonResult<PageResult<SpGoodsSkuRespVO>> getGoodsSkuPage(@Valid SpGoodsSkuPageReqVO pageVO) {
        PageResult<SpGoodsSkuDO> pageResult = goodsSkuService.getGoodsSkuPage(pageVO);
        return success(SpGoodsSkuConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/page2")
    @Operation(summary = "获得商品sku分页")
    @PreAuthenticated
    public CommonResult<PageResult<SpGoodsSkuPageRespVO>> getGoodsSkuPage2(@Valid SpGoodsSkuPageReqVO pageVO) {
        PageResult<SpGoodsSkuPageRespVO> pageResult = goodsSkuService.getGoodsSkuPageSample(pageVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品sku Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-sku:export')")
    public void exportGoodsSkuExcel(@Valid SpGoodsSkuExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpGoodsSkuDO> list = goodsSkuService.getGoodsSkuList(exportReqVO);
        // 导出 Excel
        List<SpGoodsSkuExcelVO> datas = SpGoodsSkuConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商品sku.xls", "数据", SpGoodsSkuExcelVO.class, datas);
    }

    @GetMapping("/page-sample")
    @Operation(summary = "获得商品sku分页")
    @PreAuthenticated
    public CommonResult<PageResult<SpGoodsSkuSampleRespVO>> getGoodsSkuSamplePage(@Valid SpGoodsSkuPageReqVO pageVO) {
        PageResult<SpGoodsSkuDO> pageResult = goodsSkuService.getGoodsSkuPage(pageVO);
        return success(SpGoodsSkuConvert.INSTANCE.convertSamplePage(pageResult));
    }
}
