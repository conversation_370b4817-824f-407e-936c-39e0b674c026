package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体结算银行卡更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainBillBankCardUpdateReqVO extends SpMainBillBankCardBaseVO {

    @Schema(description = "id", required = true, example = "17403")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "开户银行所在县id", example = "22114")
    private Integer bankCountyId;

    @Schema(description = "开户银行所在镇id",example = "25662")
    private Integer bankTownId;

    @Schema(description = "状态", required = true, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;
}
