package com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商家门店地图信息 Excel 导出 Request VO，参数和 SpStoreInfoMapPageReqVO 是一致的")
@Data
public class SpStoreInfoMapExportReqVO {

    @Schema(description = "主体Id", example = "1234")
    private Long spId;

    @Schema(description = "商家门店ID", example = "18230")
    private Long storeId;

    @Schema(description = "地图渠道ID", example = "1679")
    private Long mapChannelId;

    @Schema(description = "门店地图ID", example = "2795")
    private String mapId;

    @Schema(description = "门店POI")
    private String mapPoi;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
