package com.yitong.octopus.module.sp.controller.admin.industry.vo;

import javax.validation.constraints.Size;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 行业 Base VO，提供给添加、修改、详细的子 VO 使用 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 */
@Data
public class IndustryBaseVO {
	
	@Schema(description = "行业名称", required = true, example = "美业")
	@Size(max = 32, message = "行业名称长度不能超过32字符")
	private String name;
	
}
