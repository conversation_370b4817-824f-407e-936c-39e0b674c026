package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商家素材标签关系新增/修改 Request VO")
@Data
public class SpMainMaterialsTagRelSaveReqVO {

    @Schema(description = "商家素材ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20541")
    @NotNull(message = "商家素材ID不能为空")
    private Long id;

    @Schema(description = "增加标签列表", example = "14993")
    private List<PlatformTagVo> addTag;

    @Schema(description = "删除标签列表", example = "14993")
    private List<PlatformTagVo> removeTag;

}