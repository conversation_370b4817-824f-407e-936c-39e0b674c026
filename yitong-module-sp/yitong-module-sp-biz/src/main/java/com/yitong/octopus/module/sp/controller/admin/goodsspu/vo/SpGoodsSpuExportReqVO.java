package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品spu Excel 导出 Request VO，参数和 SpGoodsSpuPageReqVO 是一致的")
@Data
public class SpGoodsSpuExportReqVO {

    @Schema(description = "首次动销时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] saleStart;

    @Schema(description = "商品销量", example = "13133")
    private Integer salesCount;

    @Schema(description = "虚拟销量", example = "3504")
    private Integer virtualSalesCount;

    @Schema(description = "商品点击量", example = "12991")
    private Integer clickCount;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "主体Id", example = "24153")
    private Long spId;

    @Schema(description = "商家门店Id", example = "2303")
    private Long storeId;

    @Schema(description = "类目", example = "25255")
    private Long categoryId;

    @Schema(description = "商品品牌编号", example = "23655")
    private Integer brandId;

    @Schema(description = "商品名称", example = "张三")
    private String fullName;

    @Schema(description = "商品简称", example = "张三")
    private String shortName;

    @Schema(description = "商品状态:0 草稿、1待审核、2上架、3下架、4已售罄", example = "2")
    private Integer status;

    /**
     * 精选类型: 0 普通 ，1 主推
     */
    private Integer featuredType;

    @Schema(description = "前端请求的tab类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer tabType;
}
