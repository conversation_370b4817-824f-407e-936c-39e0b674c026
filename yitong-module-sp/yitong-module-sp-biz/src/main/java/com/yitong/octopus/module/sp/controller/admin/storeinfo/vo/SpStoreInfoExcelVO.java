package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.*;
import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_POI_AUDIT_STATUS;

/**
 * 商家门店信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoExcelVO {

    @ExcelProperty("id")
    private String id;

    @ExcelProperty("主体Id")
    private String spId;

    @ExcelProperty("类目Id")
    private String categoryId;

    @ExcelProperty("商家门店名称")
    private String storeName;

    @ExcelProperty("所在省")
    private String spProvince;

    @ExcelProperty("所在市")
    private String spCity;

    @ExcelProperty("所在县")
    private String spCounty;

    @ExcelProperty("所在镇")
    private String spTown;

    @ExcelProperty("详细地址")
    private String spAdd;

    @ExcelProperty("人均消费")
    private Integer storeAvgPrice;

    @ExcelProperty(value = "状态 ", converter = DictConvert.class)
    @DictFormat(SP_MAIN_INFO_STATUS)
    private Integer spStatus;

    @ExcelProperty("规模")
    private Integer spScale;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat(SP_MAIN_INFO_AUDIT_STATUS)
    private Integer auditStatus;

    @ExcelProperty("联系人姓名")
    private String linkName;

    @ExcelProperty("联系人手机")
    private String linkPhone;

    @ExcelProperty("联系人电话")
    private String linkTel;

    @ExcelProperty("联系人邮箱")
    private String linkEmail;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("地图渠道ID")
    private String mapChannelId;

    @ExcelProperty("门店地图ID")
    private String mapId;

    @ExcelProperty("门店POI")
    private String mapPoi;

    @ExcelProperty("门店地图POI链接")
    private String mapPoiUrl;

    @ExcelProperty("纬度")
    private String latitude;

    @ExcelProperty("经度")
    private String longitude;

    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    @ExcelProperty(value = "认领状态", converter = DictConvert.class)
    @DictFormat(SP_CLAIM_STATUS)
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    @ExcelProperty(value = "主体资质审核", converter = DictConvert.class)
    @DictFormat(SP_SUBJECT_QUAL_STATUS)
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    @ExcelProperty(value = "行业资质审核", converter = DictConvert.class)
    @DictFormat(SP_INDUSTRY_AUDIT_STATUS)
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    @ExcelProperty(value = "门店资质审核", converter = DictConvert.class)
    @DictFormat(SP_POI_AUDIT_STATUS)
    private Integer poiAuditStatus;
}
