package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 商家门店渠道配置信息表日志 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreChannelConfigLogBaseVO {

    @Schema(description = "配置ID", example = "476")
    private Long configId;

    @Schema(description = "门店ID", example = "2851")
    private Long storeId;

    @Schema(description = "平台渠道ID", example = "18834")
    private Long channelId;

    @Schema(description = "配置信息值")
    private String configValue;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审批人", example = "13141")
    private Long auditUserId;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;

}
