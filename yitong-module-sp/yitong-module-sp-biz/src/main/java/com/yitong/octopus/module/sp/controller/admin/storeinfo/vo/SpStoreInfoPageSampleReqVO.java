package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商家门店信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoPageSampleReqVO extends PageParam {

    @Schema(description = "主体Id", example = "11434")
    private Long spId;

    @Schema(description = "商家门店名称", example = "王五")
    private String storeName;

    @Schema(description = "门店状态")
    private Integer status;

}
