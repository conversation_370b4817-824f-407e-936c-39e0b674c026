package com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 商品spu Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpGoodsSpuParamsBaseVO {

    @Schema(description = "主体Id", required = true, example = "29818")
    @NotNull(message = "主体Id不能为空")
    private Long spId;

    @Schema(description = "商家门店Id", required = true, example = "17773")
    @NotNull(message = "商家门店Id不能为空")
    private Long storeId;

    @Schema(description = "参数Id", required = true, example = "21198")
    @NotNull(message = "参数Id不能为空")
    private Integer paramId;

    @Schema(description = "参数名称", required = true, example = "芋艿")
    @NotNull(message = "参数名称不能为空")
    private String paramName;

    @Schema(description = "参数值", required = true)
    @NotNull(message = "参数值不能为空")
    private String paramValue;

}
