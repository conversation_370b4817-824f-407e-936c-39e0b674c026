package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 门店审核记录 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoAuditLogBaseVO {

    @Schema(description = "门店Id", example = "3656")
    private Long storeId;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

}
