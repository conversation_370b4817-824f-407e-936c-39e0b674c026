package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品sku Excel 导出 Request VO，参数和 SpGoodsSkuPageReqVO 是一致的")
@Data
public class SpGoodsSkuExportReqVO {

    @Schema(description = "主体Id", example = "19437")
    private Long spId;

    @Schema(description = "商品spuId", example = "2496")
    private Long spuId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
