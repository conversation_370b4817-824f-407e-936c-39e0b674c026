package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 主体平台合同结算信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainPlatformContractBillItemBaseVO {

    @Schema(description = "服务商ID", example = "3810")
    private Long spId;

//    @Schema(description = "服务商名称", example = "芋艿")
//    private String spName;

    @Schema(description = "服务商合同ID", example = "1489")
    private Long spContractId;

    @Schema(description = "结算项ID", example = "22961")
    private Long billItemId;

    @Schema(description = "结算项值")
    private String billItemValue;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建人ID", example = "19499")
//    @NotNull(message = "创建人ID不能为空")
    private Long createUserId;

}
