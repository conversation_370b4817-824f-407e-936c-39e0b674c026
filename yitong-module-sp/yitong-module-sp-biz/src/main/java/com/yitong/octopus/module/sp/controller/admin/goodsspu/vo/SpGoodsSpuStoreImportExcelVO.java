package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 商家商品门店更新导入 Excel 导入 VO
 */
@Schema(description = "管理后台 - 商家商品门店更新导入 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpGoodsSpuStoreImportExcelVO {

    @Schema(description = "门店Id", required = true, example = "1")
    @ExcelProperty(value = "门店Id",converter = LongStringConverter.class)
    @NotNull(message = "门店Id不能为空")
    private Long storeId;

}
