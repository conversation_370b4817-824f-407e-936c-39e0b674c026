package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品库存供给分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GoodsSpuStockRationingPageReqVO extends PageParam {

    @Schema(description = "商品ID", example = "11530")
    private Long spuId;

    @Schema(description = "是否动态库存")
    private Boolean isDynamicStock;

}