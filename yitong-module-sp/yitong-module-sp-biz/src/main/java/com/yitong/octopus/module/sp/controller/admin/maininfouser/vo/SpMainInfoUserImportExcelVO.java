package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import com.yitong.octopus.module.sp.enums.DictTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import static com.yitong.octopus.module.system.enums.DictTypeConstants.YT_COMMON_STATUS;

/**
 * 商家核销账号 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpMainInfoUserImportExcelVO {

    @ExcelProperty("商家ID")
    private Long spId;

    @ExcelProperty("名称")
    private String name;

    @ExcelProperty("手机号")
    private String mobile;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty(value = "账号状态", converter = DictConvert.class)
    @DictFormat(YT_COMMON_STATUS)
    private Integer status;

    @ExcelProperty(value = "类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SP_MAIN_INFO_USER_TYPE)
    private Integer type;

    @ExcelProperty("门店ID")
    private Long storeId;

}
