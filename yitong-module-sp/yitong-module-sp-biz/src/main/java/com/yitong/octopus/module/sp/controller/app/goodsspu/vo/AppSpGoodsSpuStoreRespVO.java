package com.yitong.octopus.module.sp.controller.app.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

@Schema(description = "前端 - 商品门店 Response VO")
@Data
@ToString(callSuper = true)
public class AppSpGoodsSpuStoreRespVO {

    @Schema(description = "主键", required = true, example = "6521")
    private Long id;

    @NotEmpty(message = "门店名称")
    private String storeName;

    /**
     * 商家门店电话
     */
    private String storeTel;
    /**
     * 所在省id
     */
    private Integer spProvinceId;
    /**
     * 所在市id
     */
    private Integer spCityId;
    /**
     * 所在县id
     */
    private Integer spCountyId;
    /**
     * 所在镇id
     */
    private Integer spTownId;

    /**
     * 所在省
     */
    private String spProvince;
    /**
     * 所在市
     */
    private String spCity;
    /**
     * 所在县
     */
    private String spCounty;
    /**
     * 所在镇
     */
    private String spTown;
    /**
     * 详细地址
     */
    private String spAdd;

}
