package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 商品库存供给 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GoodsSpuStockRationingRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31520")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "商品ID", example = "11530")
    @ExcelProperty("商品ID")
    private Long spuId;

    @Schema(description = "商品名称", example = "11530")
    @ExcelProperty("商品名称")
    private String spuName;

    @Schema(description = "总库存数")
    @ExcelProperty("总库存数")
    private Integer totalStock;

    @Schema(description = "是否动态库存")
    @ExcelProperty("是否动态库存")
    private Boolean isDynamicStock;

    @Schema(description = "剩余存数")
    @ExcelProperty("剩余存数")
    private Integer remainingStock;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}