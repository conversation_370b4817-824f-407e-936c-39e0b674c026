package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainplatformcontract.SpMainPlatformContractDO;
import com.yitong.octopus.module.sp.convert.mainplatformcontract.SpMainPlatformContractConvert;
import com.yitong.octopus.module.sp.service.mainplatformcontract.SpMainPlatformContractService;

@Tag(name = "管理后台 - 主体平台合同")
@RestController
@RequestMapping("/sp/main-platform-contract")
@Validated
public class SpMainPlatformContractController {

    @Resource
    private SpMainPlatformContractService mainPlatformContractService;

    @PostMapping("/create")
    @Operation(summary = "创建主体平台合同")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:create')")
    public CommonResult<Long> createMainPlatformContract(@Valid @RequestBody SpMainPlatformContractCreateReqVO createReqVO) {
        return success(mainPlatformContractService.createMainPlatformContract(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体平台合同")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:update')")
    public CommonResult<Boolean> updateMainPlatformContract(@Valid @RequestBody SpMainPlatformContractUpdateReqVO updateReqVO) {
        mainPlatformContractService.updateMainPlatformContract(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体平台合同")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:delete')")
    public CommonResult<Boolean> deleteMainPlatformContract(@RequestParam("id") Long id) {
        mainPlatformContractService.deleteMainPlatformContract(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体平台合同")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:query')")
    public CommonResult<SpMainPlatformContractViewRespVO> getMainPlatformContract(@RequestParam("id") Long id) {
        return success(mainPlatformContractService.getMainPlatformContractWithBillItems(id));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体平台合同列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:query')")
    public CommonResult<List<SpMainPlatformContractRespVO>> getMainPlatformContractList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainPlatformContractDO> list = mainPlatformContractService.getMainPlatformContractList(ids);
        return success(SpMainPlatformContractConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体平台合同分页")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:query')")
    public CommonResult<PageResult<SpMainPlatformContractRespVO>> getMainPlatformContractPage(@Valid SpMainPlatformContractPageReqVO pageVO) {
        PageResult<SpMainPlatformContractDO> pageResult = mainPlatformContractService.getMainPlatformContractPage(pageVO);
        return success(SpMainPlatformContractConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体平台合同Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-platform-contract:export')")
    public void exportMainPlatformContractExcel(@Valid SpMainPlatformContractExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainPlatformContractDO> list = mainPlatformContractService.getMainPlatformContractList(exportReqVO);
        // 导出 Excel
        List<SpMainPlatformContractExcelVO> datas = SpMainPlatformContractConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体平台合同.xls", "数据", SpMainPlatformContractExcelVO.class, datas);
    }

}
