package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 主体审核记录
 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainInfoAuditLogBaseVO {

    @Schema(description = "主体Id", example = "29684")
    private Long spId;

    @Schema(description = "主体名称", example = "王五")
    private String spName;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "审批人", required = true, example = "6646")
    @NotNull(message = "审批人不能为空")
    private Long auditUserId;

    @Schema(description = "审批备注", required = true, example = "不喜欢")
    private String auditReason;

    @Schema(description = "审批时间", required = true)
    @NotNull(message = "审批时间不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime auditTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
