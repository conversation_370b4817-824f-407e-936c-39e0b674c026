package com.yitong.octopus.module.sp.controller.admin.userdatascop.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.sp.permission.enums.SpDataScopeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;
import java.util.Set;

/**
* 用户与商家权限数据权限 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpUserDataScopeBaseVO {

    @Schema(description = "用户ID", required = true, example = "21387")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "数据范围（1：全部数据权限 2：自定数据权限 3：本商家/服务商数据权限 4：本服务商商家及以下数据权限）", required = true)
    @NotNull(message = "数据范围（1：全部数据权限 2：自定数据权限 3：本商家/服务商数据权限 4：本服务商商家及以下数据权限）不能为空")
    @InEnum(SpDataScopeEnum.class)
    private Integer dataScope;

    @Schema(description = "数据范围(指定部门数组)", required = true)
    private Set<Long> dataScopeSpIds;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
