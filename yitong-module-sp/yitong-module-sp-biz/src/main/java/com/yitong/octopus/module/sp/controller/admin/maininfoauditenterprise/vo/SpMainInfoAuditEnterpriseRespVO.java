package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体认证信息（企业） Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditEnterpriseRespVO extends SpMainInfoAuditEnterpriseBaseVO {

    @Schema(description = "id", required = true, example = "27114")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
