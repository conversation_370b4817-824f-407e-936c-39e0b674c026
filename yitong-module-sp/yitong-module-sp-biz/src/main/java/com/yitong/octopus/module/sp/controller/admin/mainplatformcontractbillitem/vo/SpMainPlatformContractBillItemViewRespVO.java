package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 主体平台合同结算信息view resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemViewRespVO extends SpMainPlatformContractBillItemBaseVO {

    @Schema(description = "id", required = true, example = "4718")
    @NotNull(message = "id不能为空")
    private Long id;

}
