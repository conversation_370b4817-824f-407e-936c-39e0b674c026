package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 服务商门店-渠道配置信息 Excel 导出 Request VO，参数和 SpStoreChannelConfigPageReqVO 是一致的")
@Data
public class SpStoreChannelConfigExportReqVO {

    @Schema(description = "门店ID", example = "4888")
    private Long storeId;

    @Schema(description = "平台渠道ID", example = "19503")
    private Long channelId;

    @Schema(description = "配置信息值")
    private String configValue;

    @Schema(description = "审核状态 ", example = "1")
    private Integer auditStatus;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
