package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoauditlog.SpMainInfoAuditLogDO;
import com.yitong.octopus.module.sp.convert.maininfoauditlog.SpMainInfoAuditLogConvert;
import com.yitong.octopus.module.sp.service.maininfoauditlog.SpMainInfoAuditLogService;

@Tag(name = "管理后台 - 主体审核记录")
@RestController
@RequestMapping("/sp/main-info-audit-log")
@Validated
public class SpMainInfoAuditLogController {

    @Resource
    private SpMainInfoAuditLogService mainInfoAuditLogService;

    @PostMapping("/create")
    @Operation(summary = "创建主体审核记录")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:create')")
    public CommonResult<Long> createMainInfoAuditLog(@Valid @RequestBody SpMainInfoAuditLogCreateReqVO createReqVO) {
        return success(mainInfoAuditLogService.createMainInfoAuditLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体审核记录")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:update')")
    public CommonResult<Boolean> updateMainInfoAuditLog(@Valid @RequestBody SpMainInfoAuditLogUpdateReqVO updateReqVO) {
        mainInfoAuditLogService.updateMainInfoAuditLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体审核记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:delete')")
    public CommonResult<Boolean> deleteMainInfoAuditLog(@RequestParam("id") Long id) {
        mainInfoAuditLogService.deleteMainInfoAuditLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体审核记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:query')")
    public CommonResult<SpMainInfoAuditLogViewRespVO> getMainInfoAuditLog(@RequestParam("id") Long id) {
        SpMainInfoAuditLogDO mainInfoAuditLog = mainInfoAuditLogService.getMainInfoAuditLog(id);
        return success(SpMainInfoAuditLogConvert.INSTANCE.convertView(mainInfoAuditLog));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体审核记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:query')")
    public CommonResult<List<SpMainInfoAuditLogRespVO>> getMainInfoAuditLogList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoAuditLogDO> list = mainInfoAuditLogService.getMainInfoAuditLogList(ids);
        return success(SpMainInfoAuditLogConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体审核记录分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:query')")
    public CommonResult<PageResult<SpMainInfoAuditLogRespVO>> getMainInfoAuditLogPage(@Valid SpMainInfoAuditLogPageReqVO pageVO) {
        PageResult<SpMainInfoAuditLogDO> pageResult = mainInfoAuditLogService.getMainInfoAuditLogPage(pageVO);
        return success(SpMainInfoAuditLogConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体审核记录Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-audit-log:export')")
    public void exportMainInfoAuditLogExcel(@Valid SpMainInfoAuditLogExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoAuditLogDO> list = mainInfoAuditLogService.getMainInfoAuditLogList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoAuditLogExcelVO> datas = SpMainInfoAuditLogConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体审核记录.xls", "数据", SpMainInfoAuditLogExcelVO.class, datas);
    }

}
