package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商家门店标签关系查询 Request VO")
@Data
public class SpStoreInfoTagReqVO {

    @Schema(description = "商家门店ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20541")
    @NotNull(message = "商家门店ID不能为空")
    private Long id;

    @Schema(description = "标签组Id", example = "20541")
    private Long groupId;

}