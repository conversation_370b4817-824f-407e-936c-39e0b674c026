package com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店标签关联创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoLabelCreateReqVO extends SpStoreInfoLabelBaseVO {

}
