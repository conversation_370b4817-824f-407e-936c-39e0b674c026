package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体审核记录Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditLogRespVO extends SpMainInfoAuditLogBaseVO {

    @Schema(description = "id", required = true, example = "7807")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
