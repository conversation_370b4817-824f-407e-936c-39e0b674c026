package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 商家核销账号导入 Response VO")
@Data
@Builder
public class SpStoreChannelConfigImportRespVO {

    @Schema(description = "创建成功的门店数组", required = true)
    private List<String> createResult;

    @Schema(description = "更新成功的门店数组", required = true)
    private List<String> updateResult;

    @Schema(description = "导入失败的用户集合,key 为用户名，value 为失败原因", required = true)
    private Map<String, String> failureResult;

}
