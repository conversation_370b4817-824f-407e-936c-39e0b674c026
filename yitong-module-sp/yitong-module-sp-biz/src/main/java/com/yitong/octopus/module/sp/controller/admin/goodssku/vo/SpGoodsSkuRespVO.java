package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品sku Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSkuRespVO extends SpGoodsSkuBaseVO {

    @Schema(description = "主键", required = true, example = "25294")
    private Long id;

    @Schema(description = "创建时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;
    
    private Long spId;
    
    private Long spuId;
    
    @Schema(description = "可用库存")
	private Integer saleStock;
    
    @Schema(description = "预占库存")
    private Integer occupyStock;

    /**
     * 市场价
     */
    @Schema(description = "市场价")
    private Long marketAmount;
    /**
     * 售价
     */
    @Schema(description = "售价")
    private Long saleAmount;

}
