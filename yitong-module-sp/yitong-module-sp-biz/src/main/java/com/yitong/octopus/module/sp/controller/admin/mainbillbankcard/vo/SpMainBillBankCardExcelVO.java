package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import com.yitong.octopus.framework.excel.core.convert.AreaConvert;
import com.yitong.octopus.module.platform.convert.excel.PlatformBankExcelConvert;
import com.yitong.octopus.module.sp.convert.excel.SpMainInfoExcelConvert;
import com.yitong.octopus.module.sp.convert.excel.SpStoreInfoExcelConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.*;


/**
 * 主体结算银行卡 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainBillBankCardExcelVO {

    @ExcelProperty(value = "账号类型", converter = DictConvert.class)
    @DictFormat(SP_MAIN_BILL_BANK_CARD_TYPE)
    private Integer type;

    @ExcelProperty(value = "银行卡类型", converter = DictConvert.class)
    @DictFormat(SP_MAIN_BILL_BANK_CARD_CARD_TYPE)
    private Integer cardType;

    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat(SP_MAIN_BILL_BANK_CARD_STATUS)
    private Integer status;

    @ExcelProperty(value = "服务商/商家", converter = SpMainInfoExcelConvert.class)
    private Long spId;

    @ExcelProperty(value = "门店", converter = SpStoreInfoExcelConvert.class)
    private Long storeId;

    @ExcelProperty(value = "开户行", converter = PlatformBankExcelConvert.class)
    private Long bankId;

    @ExcelProperty("银行开户名")
    private String bankAccountName;

    @ExcelProperty("银行账号")
    private String bankNumber;

    @ExcelProperty("支行名称")
    private String subBankName;

    @ExcelProperty(value = "银行地址",converter = AreaConvert.class)
    private Long bankProvinceId;

}
