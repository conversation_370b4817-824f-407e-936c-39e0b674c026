package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品spu结算信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuBillItemPageReqVO extends PageParam {

    @Schema(description = "主体Id", example = "3145")
    private Long spId;

    @Schema(description = "商家门店Id", example = "32735")
    private Long storeId;

    @Schema(description = "商品spuId", example = "694")
    private Long spuId;

    @Schema(description = "商品合同Id", example = "23032")
    private Long spContractId;

    @Schema(description = "结算项ID", example = "15778")
    private Long billItemId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
