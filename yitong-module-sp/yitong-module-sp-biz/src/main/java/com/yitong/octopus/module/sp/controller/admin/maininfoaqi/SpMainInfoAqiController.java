package com.yitong.octopus.module.sp.controller.admin.maininfoaqi;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;

import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoaqi.SpMainInfoAqiDO;
import com.yitong.octopus.module.sp.convert.maininfoaqi.SpMainInfoAqiConvert;
import com.yitong.octopus.module.sp.service.maininfoaqi.SpMainInfoAqiService;

@Tag(name = "管理后台 - 主体资质信息")
@RestController
@RequestMapping("/sp/main-info-aqi")
@Validated
public class SpMainInfoAqiController {

    @Resource
    private SpMainInfoAqiService mainInfoAqiService;

    @PostMapping("/create")
    @Operation(summary = "创建主体资质信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:create')")
    public CommonResult<Long> createMainInfoAqi(@Valid @RequestBody SpMainInfoAqiCreateReqVO createReqVO) {
        return success(mainInfoAqiService.createMainInfoAqi(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体资质信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:update')")
    public CommonResult<Boolean> updateMainInfoAqi(@Valid @RequestBody SpMainInfoAqiUpdateReqVO updateReqVO) {
        mainInfoAqiService.updateMainInfoAqi(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体资质信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:delete')")
    public CommonResult<Boolean> deleteMainInfoAqi(@RequestParam("id") Long id) {
        mainInfoAqiService.deleteMainInfoAqi(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体资质信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:query')")
    public CommonResult<SpMainInfoAqiViewRespVO> getMainInfoAqi(@RequestParam("id") Long id) {
        SpMainInfoAqiDO mainInfoAqi = mainInfoAqiService.getMainInfoAqi(id);
        return success(SpMainInfoAqiConvert.INSTANCE.convertView(mainInfoAqi));
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体资质信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:query')")
    public CommonResult<List<SpMainInfoAqiRespVO>> getMainInfoAqiList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoAqiDO> list = mainInfoAqiService.getMainInfoAqiList(ids);
        return success(SpMainInfoAqiConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体资质信息分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:query')")
    public CommonResult<PageResult<SpMainInfoAqiRespVO>> getMainInfoAqiPage(@Valid SpMainInfoAqiPageReqVO pageVO) {
        PageResult<SpMainInfoAqiDO> pageResult = mainInfoAqiService.getMainInfoAqiPage(pageVO);
        return success(SpMainInfoAqiConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体资质信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info-aqi:export')")
    public void exportMainInfoAqiExcel(@Valid SpMainInfoAqiExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoAqiDO> list = mainInfoAqiService.getMainInfoAqiList(exportReqVO);
        // 导出 Excel
        List<SpMainInfoAqiExcelVO> datas = SpMainInfoAqiConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体资质信息.xls", "数据", SpMainInfoAqiExcelVO.class, datas);
    }

}
