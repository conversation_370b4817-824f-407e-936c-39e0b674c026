package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 主体素材 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainInfoMaterialsBaseVO {

    @Schema(description = "商家名称ID", example = "10336")
    private Long spId;

    @Schema(description = "商家名称", example = "王五")
    private String spName;

    @Schema(description = "门店ID", example = "12719")
    private Long storeId;

    @Schema(description = "门店名称", example = "王五")
    private String storeName;

    @Schema(description = "类型", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    private String type;

    @Schema(description = "文件类型", required = true, example = "2")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "审核意见")
    private String auditMsg;

    @Schema(description = "审核人")
    private String auditUser;

    @Schema(description = "文件url", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "文件url不能为空")
    private String fileUrl;

    @Schema(description = "视频封面url", required = true, example = "https://www.iocoder.cn")
    private String coverUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    /**
     * 商品spuId
     */
    @Schema(description = "商品spuId")
    private Long spuId;

    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    private Long skuId;

}
