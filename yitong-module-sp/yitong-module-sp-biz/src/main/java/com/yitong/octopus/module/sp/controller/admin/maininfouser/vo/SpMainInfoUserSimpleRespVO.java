package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商户子账号 Response VO")
@Data
@ToString(callSuper = true)
public class SpMainInfoUserSimpleRespVO {

    @Schema(description = "主键", required = true, example = "19328")
    private Long id;

    @Schema(description = "门店ID", example = "26789")
    private Long storeId;

    @Schema(description = "名称", required = true, example = "张三")
    @NotNull(message = "名称不能为空")
    private String name;

    @Schema(description = "类型", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    private Integer type;

}
