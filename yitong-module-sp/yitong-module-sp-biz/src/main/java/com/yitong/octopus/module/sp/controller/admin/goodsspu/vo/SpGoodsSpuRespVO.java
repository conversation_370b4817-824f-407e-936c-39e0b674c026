package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;

import com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo.SpGoodsSpuBilItemRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo.SpGoodsSpuBillItemBaseVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoRespVO;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.SpStoreInfoReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品spu Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuRespVO extends SpGoodsSpuBaseVO {

    @Schema(description = "主键", required = true, example = "6521")
    private Long id;

    @Schema(description = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;
    
	private List<SpGoodsSkuRespVO> skus;
	
	private List<SpStoreInfoReqVO> storeIds;
	
	private List<SpGoodsSpuChannelRespVo> channels;

    @Schema(description = "spu结算信息")
    private List<SpGoodsSpuBilItemRespVO> billItems;

    @Schema(description = "所属服务商/商家")
    private SpMainInfoRespVO spMainInfoRespVO;

}
