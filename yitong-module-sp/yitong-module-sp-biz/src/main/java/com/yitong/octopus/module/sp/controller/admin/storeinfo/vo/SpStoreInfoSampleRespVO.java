package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息 Response VO")
@Data
@ToString(callSuper = true)
public class SpStoreInfoSampleRespVO {

    @Schema(description = "id", required = true, example = "25053")
    private Long id;

    @Schema(description = "商家门店名称", required = true, example = "王五")
    @NotNull(message = "商家门店名称不能为空")
    private String storeName;

}
