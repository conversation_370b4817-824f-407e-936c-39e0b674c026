package com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店图片更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoGalleryUpdateReqVO extends SpStoreInfoGalleryBaseVO {

    @Schema(description = "id", required = true, example = "11762")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "创建人ID", required = true, example = "26331")
    @NotNull(message = "创建人ID不能为空")
    private Long createUserId;

}
