package com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店地图信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoMapUpdateReqVO extends SpStoreInfoMapBaseVO {

    @Schema(description = "id", required = true, example = "897")
    @NotNull(message = "id不能为空")
    private Long id;

}
