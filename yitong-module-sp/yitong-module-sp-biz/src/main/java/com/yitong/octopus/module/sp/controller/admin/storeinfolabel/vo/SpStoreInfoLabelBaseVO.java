package com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import javax.validation.constraints.*;

/**
* 商家门店标签关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoLabelBaseVO {

    @Schema(description = "主体Id", required = true, example = "26818")
    @NotNull(message = "主体Id不能为空")
    private Long spId;

    @Schema(description = "商家门店ID", required = true, example = "17692")
    @NotNull(message = "商家门店ID不能为空")
    private Long storeId;

    @Schema(description = "标签Id", required = true, example = "13745")
    @NotNull(message = "标签Id不能为空")
    private Long labelId;

}
