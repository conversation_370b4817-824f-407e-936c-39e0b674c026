package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
* 主体平台合同结算信息 Base VO，提供给添加、修改、详细的子 DTO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainPlatformContractBillItemBaseDto {
    @Schema(description = "结算项ID", example = "22961")
    @NotNull(message = "结算项ID不能为空")
    private Long billItemId;

    @Schema(description = "结算项值")
    @NotBlank(message = "结算项值不能为空")
    private String billItemValue;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
