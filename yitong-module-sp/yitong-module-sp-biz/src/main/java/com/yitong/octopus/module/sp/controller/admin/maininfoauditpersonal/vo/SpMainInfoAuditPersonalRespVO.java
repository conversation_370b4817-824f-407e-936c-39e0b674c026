package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体认证信息-个人 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditPersonalRespVO extends SpMainInfoAuditPersonalBaseVO {

    @Schema(description = "id", required = true, example = "8444")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
