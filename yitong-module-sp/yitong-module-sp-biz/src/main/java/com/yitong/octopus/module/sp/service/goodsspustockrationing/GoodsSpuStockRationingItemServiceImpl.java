package com.yitong.octopus.module.sp.service.goodsspustockrationing;

import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.sp.enums.SpStockRationingStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspustockrationing.GoodsSpuStockRationingItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.sp.dal.mysql.goodsspustockrationing.GoodsSpuStockRationingItemMapper;

import java.time.LocalDateTime;
import java.util.List;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 商品库存供给明细 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GoodsSpuStockRationingItemServiceImpl extends ServiceImpl<GoodsSpuStockRationingItemMapper,GoodsSpuStockRationingItemDO>  implements GoodsSpuStockRationingItemService {

    @Override
    public Long createGoodsSpuStockRationingItem(GoodsSpuStockRationingItemSaveReqVO createReqVO) {
        // 插入
        GoodsSpuStockRationingItemDO goodsSpuStockRationingItem = BeanUtils.toBean(createReqVO, GoodsSpuStockRationingItemDO.class);
        if (ObjectUtil.isNull(goodsSpuStockRationingItem.getStatus())){
            goodsSpuStockRationingItem.setStatus(SpStockRationingStatusEnum.TO_BE_EFFECTIVE.getStatus());
        }
        goodsSpuStockRationingItem.setRealTotalStock(goodsSpuStockRationingItem.getTotalStock());
        getBaseMapper().insert(goodsSpuStockRationingItem);
        // 返回
        return goodsSpuStockRationingItem.getId();
    }

    @Override
    public void updateGoodsSpuStockRationingItem(GoodsSpuStockRationingItemSaveReqVO updateReqVO) {
        // 校验存在
        validateGoodsSpuStockRationingItemExists(updateReqVO.getId());
        // 更新
        GoodsSpuStockRationingItemDO updateObj = BeanUtils.toBean(updateReqVO, GoodsSpuStockRationingItemDO.class);
        // 同步库存数
        updateObj.setRealTotalStock(updateObj.getTotalStock());
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void deleteGoodsSpuStockRationingItem(Long id) {
        // 校验存在
        validateGoodsSpuStockRationingItemExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateGoodsSpuStockRationingItemExists(Long id) {
        GoodsSpuStockRationingItemDO itemDO  = getBaseMapper().selectById(id);
        if (ObjectUtil.isNull(itemDO)) {
            throw exception(GOODS_SPU_STOCK_RATIONING_ITEM_NOT_EXISTS);
        }
        /**
         * 非待生效的不能修改
         */
        if (!SpStockRationingStatusEnum.TO_BE_EFFECTIVE.getStatus().equals(itemDO.getStatus())){
            throw exception(GOODS_SPU_STOCK_RATIONING_ITEM_STATUS_NOT_VALID);
        }
    }

    @Override
    public GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItem(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemPage(GoodsSpuStockRationingItemPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItemCurrentByRationingId(Long rationingId) {
        return getBaseMapper().selectOne(
                new LambdaQueryWrapperX<GoodsSpuStockRationingItemDO>().eq(GoodsSpuStockRationingItemDO::getRationingId,rationingId)
                        .eq(GoodsSpuStockRationingItemDO::getStatus,SpStockRationingStatusEnum.TO_BE_EFFECTIVE.getStatus())
                        .apply("DATE_FORMAT(day,'%Y-%m-%d') = DATE_FORMAT(NOW(),'%Y-%m-%d')")
        );
    }

    @Override
    public GoodsSpuStockRationingItemDO getGoodsSpuStockRationingItemBeforeByRationingId(Long rationingId) {
        return getBaseMapper().selectOne(
                new LambdaQueryWrapperX<GoodsSpuStockRationingItemDO>().eq(GoodsSpuStockRationingItemDO::getRationingId,rationingId)
                        .eq(GoodsSpuStockRationingItemDO::getStatus,SpStockRationingStatusEnum.TAKING_EFFECT.getStatus())
                        .apply("DATE_FORMAT(day,'%Y-%m-%d') < DATE_FORMAT(NOW(),'%Y-%m-%d')")
        );
    }

    @Override
    public Long getGoodsSpuStockRationingBeforeItemByRationingId(Long rationingId) {
        return getBaseMapper().getGoodsSpuStockRationingBeforeItemByRationingId(rationingId);
    }

    @Override
    public List<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemList() {
        return getBaseMapper().selectList(
                new LambdaQueryWrapperX<GoodsSpuStockRationingItemDO>()
                 .eq(GoodsSpuStockRationingItemDO::getStatus,SpStockRationingStatusEnum.TAKING_EFFECT.getStatus())
                .apply("DATE_FORMAT(day,'%Y-%m-%d') < DATE_FORMAT(NOW(),'%Y-%m-%d')")
        );
    }

    @Override
    public List<GoodsSpuStockRationingItemDO> getGoodsSpuStockRationingItemListByRationingId(Long rationingId) {
        return getBaseMapper().selectList(GoodsSpuStockRationingItemDO::getRationingId,rationingId);
    }

}