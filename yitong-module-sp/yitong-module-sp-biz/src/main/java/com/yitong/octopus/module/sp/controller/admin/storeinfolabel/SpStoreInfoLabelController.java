//package com.yitong.octopus.module.sp.controller.admin.storeinfolabel;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import javax.validation.constraints.*;
//import javax.validation.*;
//import javax.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.storeinfolabel.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.storeinfolabel.SpStoreInfoLabelDO;
//import com.yitong.octopus.module.sp.convert.storeinfolabel.SpStoreInfoLabelConvert;
//import com.yitong.octopus.module.sp.service.storeinfolabel.SpStoreInfoLabelService;
//
//@Tag(name = "管理后台 - 商家门店标签关联")
//@RestController
//@RequestMapping("/sp/store-info-label")
//@Validated
//public class SpStoreInfoLabelController {
//
//    @Resource
//    private SpStoreInfoLabelService storeInfoLabelService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家门店标签关联")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:create')")
//    public CommonResult<Long> createStoreInfoLabel(@Valid @RequestBody SpStoreInfoLabelCreateReqVO createReqVO) {
//        return success(storeInfoLabelService.createStoreInfoLabel(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家门店标签关联")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:update')")
//    public CommonResult<Boolean> updateStoreInfoLabel(@Valid @RequestBody SpStoreInfoLabelUpdateReqVO updateReqVO) {
//        storeInfoLabelService.updateStoreInfoLabel(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家门店标签关联")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:delete')")
//    public CommonResult<Boolean> deleteStoreInfoLabel(@RequestParam("id") Long id) {
//        storeInfoLabelService.deleteStoreInfoLabel(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家门店标签关联")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:query')")
//    public CommonResult<SpStoreInfoLabelRespVO> getStoreInfoLabel(@RequestParam("id") Long id) {
//        SpStoreInfoLabelDO storeInfoLabel = storeInfoLabelService.getStoreInfoLabel(id);
//        return success(SpStoreInfoLabelConvert.INSTANCE.convert(storeInfoLabel));
//    }
//
//    @GetMapping("/list")
//    @Operation(summary = "获得商家门店标签关联列表")
//    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:query')")
//    public CommonResult<List<SpStoreInfoLabelRespVO>> getStoreInfoLabelList(@RequestParam("ids") Collection<Long> ids) {
//        List<SpStoreInfoLabelDO> list = storeInfoLabelService.getStoreInfoLabelList(ids);
//        return success(SpStoreInfoLabelConvert.INSTANCE.convertList(list));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家门店标签关联分页")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:query')")
//    public CommonResult<PageResult<SpStoreInfoLabelRespVO>> getStoreInfoLabelPage(@Valid SpStoreInfoLabelPageReqVO pageVO) {
//        PageResult<SpStoreInfoLabelDO> pageResult = storeInfoLabelService.getStoreInfoLabelPage(pageVO);
//        return success(SpStoreInfoLabelConvert.INSTANCE.convertPage(pageResult));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家门店标签关联 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:store-info-label:export')")
//    @OperateLog(type = EXPORT)
//    public void exportStoreInfoLabelExcel(@Valid SpStoreInfoLabelExportReqVO exportReqVO,
//              HttpServletResponse response) throws IOException {
//        List<SpStoreInfoLabelDO> list = storeInfoLabelService.getStoreInfoLabelList(exportReqVO);
//        // 导出 Excel
//        List<SpStoreInfoLabelExcelVO> datas = SpStoreInfoLabelConvert.INSTANCE.convertList02(list);
//        ExcelUtils.write(response, "商家门店标签关联.xls", "数据", SpStoreInfoLabelExcelVO.class, datas);
//    }
//
//}
