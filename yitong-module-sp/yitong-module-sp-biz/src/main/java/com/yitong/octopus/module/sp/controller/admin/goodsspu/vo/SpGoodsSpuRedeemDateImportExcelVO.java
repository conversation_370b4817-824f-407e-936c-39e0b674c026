package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.byteconverter.ByteNumberConverter;
import com.alibaba.excel.converters.localdatetime.LocalDateTimeStringConverter;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_GOODS_SPU_USE_DATE_TYPE;

/**
 * 商家售卖时间 Excel 导入 VO
 */
@Schema(description = "管理后台 - 商家商品售卖时间导入 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpGoodsSpuRedeemDateImportExcelVO {

    @Schema(description = "商品Spu", required = true, example = "1")
    @ExcelProperty("商品Spu")
    @NotNull(message = "商品Spu不能为空")
    private String spuId;

    @Schema(description = "渠道", required = true, example = "1")
    @ExcelProperty("渠道")
    @NotEmpty(message = "渠道不能为空")
    private String channelCode;

    @Schema(description = "商品使用日期类型", required = true, example = "1")
    @ExcelProperty(value = "商品使用日期类型",converter = DictConvert.class)
    @DictFormat(SP_GOODS_SPU_USE_DATE_TYPE)
    @NotNull(message = "商品使用日期类型不能为空")
    private Integer useDateType;

    @Schema(description = "购买后N天", required = true, example = "1")
    @ExcelProperty(value = "购买后N天")
    private Integer dayDuration;

    @Schema(description = "有效期开始时间", required = true, example = "1")
    @ExcelProperty(value = "有效期开始时间",converter = LocalDateTimeStringConverter.class)
    private LocalDateTime startDate;

    @Schema(description = "有效期结束时间", required = true, example = "1")
    @ExcelProperty(value = "有效期结束时间",converter = LocalDateTimeStringConverter.class)
    private LocalDateTime endDate;

}
