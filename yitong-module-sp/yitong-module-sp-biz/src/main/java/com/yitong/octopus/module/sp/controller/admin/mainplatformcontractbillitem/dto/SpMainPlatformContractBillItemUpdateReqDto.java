package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 主体平台合同结算信息创建 Request DTO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemUpdateReqDto extends SpMainPlatformContractBillItemBaseDto {

    @Schema(description = "id", example = "4718")
    private Long id;

}
