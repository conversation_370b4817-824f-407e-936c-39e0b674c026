package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.module.platform.service.bank.PlatformBankService;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoAuditReqVO;
import com.yitong.octopus.module.sp.enums.SpBankCardCardTypeEnum;
import com.yitong.octopus.module.sp.enums.SpBankCardTypeEnum;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.sp.service.storeinfo.SpStoreInfoService;
import io.swagger.v3.oas.annotations.Parameters;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainbillbankcard.SpMainBillBankCardDO;
import com.yitong.octopus.module.sp.convert.mainbillbankcard.SpMainBillBankCardConvert;
import com.yitong.octopus.module.sp.service.mainbillbankcard.SpMainBillBankCardService;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 主体结算银行卡")
@RestController
@RequestMapping("/sp/main-bill-bank-card")
@Validated
public class SpMainBillBankCardController {

    @Resource
    private SpMainBillBankCardService mainBillBankCardService;

    @Resource
    private PlatformBankService platformBankService;

    @Resource
    private SpMainInfoService spMainInfoService;

    @Resource
    private SpStoreInfoService spStoreInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建主体结算银行卡")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:create')")
    public CommonResult<Long> createMainBillBankCard(@Valid @RequestBody SpMainBillBankCardCreateReqVO createReqVO) {
        return success(mainBillBankCardService.createMainBillBankCard(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体结算银行卡")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:update')")
    public CommonResult<Boolean> updateMainBillBankCard(@Valid @RequestBody SpMainBillBankCardUpdateReqVO updateReqVO) {
        mainBillBankCardService.updateMainBillBankCard(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体结算银行卡")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:delete')")
    public CommonResult<Boolean> deleteMainBillBankCard(@RequestParam("id") Long id) {
        mainBillBankCardService.deleteMainBillBankCard(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体结算银行卡")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:query')")
    public CommonResult<SpMainBillBankCardRespVO> getMainBillBankCard(@RequestParam("id") Long id) {
        SpMainBillBankCardDO mainBillBankCard = mainBillBankCardService.getMainBillBankCard(id);
        SpMainBillBankCardRespVO  resp = SpMainBillBankCardConvert.INSTANCE.convert(mainBillBankCard);
        if (ObjectUtil.isNotNull(resp)){
            resp.setPlatformBankVo(platformBankService.getBankSampleRespVo(resp.getBankId()));
            resp.setSpMainInfoVo(spMainInfoService.getMainInfoSampleById(resp.getSpId()));
            if (SpBankCardTypeEnum.STORE.getType().equals(resp.getType())){
                resp.setSpStoreInfoVo(spStoreInfoService.getStoreInfoSampleById(resp.getStoreId()));
            }
        }
        return success(resp);
    }

    @GetMapping("/get-owner")
    @Operation(summary = "获得主体结算银行卡")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:query')")
    public CommonResult<SpMainBillBankCardRespVO> getMainBillBankCardByOwner(@Valid SpMainBillBankCardOwnerReqVO reqVO) {
        SpMainBillBankCardDO mainBillBankCard = mainBillBankCardService.getMainBillBankCardBySpIdOrStoreId(SpBankCardTypeEnum.getByType(reqVO.getType()),reqVO.getSpId(),reqVO.getStoreId());
        SpMainBillBankCardRespVO  resp = SpMainBillBankCardConvert.INSTANCE.convert(mainBillBankCard);
        if (ObjectUtil.isNotNull(resp)){
            resp.setPlatformBankVo(platformBankService.getBankSampleRespVo(resp.getBankId()));
            resp.setSpMainInfoVo(spMainInfoService.getMainInfoSampleById(resp.getSpId()));
            if (SpBankCardTypeEnum.STORE.getType().equals(resp.getType())){
                resp.setSpStoreInfoVo(spStoreInfoService.getStoreInfoSampleById(resp.getStoreId()));
            }
        }
        return success(resp);
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体结算银行卡列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:query')")
    public CommonResult<List<SpMainBillBankCardRespVO>> getMainBillBankCardList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainBillBankCardDO> list = mainBillBankCardService.getMainBillBankCardList(ids);
        List<SpMainBillBankCardRespVO> respVOList = SpMainBillBankCardConvert.INSTANCE.convertList(list);
        if (CollectionUtil.isNotEmpty(respVOList)){
            respVOList.forEach(resp -> {
                resp.setPlatformBankVo(platformBankService.getBankSampleRespVo(resp.getBankId()));
                resp.setSpMainInfoVo(spMainInfoService.getMainInfoSampleById(resp.getSpId()));
                if (SpBankCardTypeEnum.STORE.getType().equals(resp.getType())){
                    resp.setSpStoreInfoVo(spStoreInfoService.getStoreInfoSampleById(resp.getStoreId()));
                }
            });
        }
        return success(respVOList);
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体结算银行卡分页")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:query')")
    public CommonResult<PageResult<SpMainBillBankCardRespVO>> getMainBillBankCardPage(@Valid SpMainBillBankCardPageReqVO pageVO) {
        PageResult<SpMainBillBankCardDO> pageResult = mainBillBankCardService.getMainBillBankCardPage(pageVO);
        PageResult<SpMainBillBankCardRespVO> page = SpMainBillBankCardConvert.INSTANCE.convertPage(pageResult);
        if (CollectionUtil.isNotEmpty(page.getList())){
            page.getList().forEach(resp ->{
                resp.setPlatformBankVo(platformBankService.getBankSampleRespVo(resp.getBankId()));
                resp.setSpMainInfoVo(spMainInfoService.getMainInfoSampleById(resp.getSpId()));
                if (SpBankCardTypeEnum.STORE.getType().equals(resp.getType())){
                    resp.setSpStoreInfoVo(spStoreInfoService.getStoreInfoSampleById(resp.getStoreId()));
                }
            });
        }
        return success(page);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体结算银行卡 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:export')")
    public void exportMainBillBankCardExcel(@Valid SpMainBillBankCardExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainBillBankCardDO> list = mainBillBankCardService.getMainBillBankCardList(exportReqVO);
        // 导出 Excel
        List<SpMainBillBankCardExcelVO> datas = SpMainBillBankCardConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体结算银行卡.xls", "数据", SpMainBillBankCardExcelVO.class, datas);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核主体基本信息")
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:audit')")
    public CommonResult<Boolean> auditMainInfo(@Valid @RequestBody SpMainInfoAuditReqVO vo) {
//        mainBillBankCardService.auditMainInfo(vo, SpContractTypeEnum.SP);
        return success(true);
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得导入商户核销账号模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<SpMainBillBankCardImportExcelVO> list = Arrays.asList(
                SpMainBillBankCardImportExcelVO.builder().spId(10000L).cardType(SpBankCardCardTypeEnum.CORPORATE_ACCOUNT.getType()).bankAccountName("张三").bankNumber("*************").bankId(1L).subBankName("上海浦东支行").bankProvince("110000").build(),
                SpMainBillBankCardImportExcelVO.builder().spId(20000L).cardType(SpBankCardCardTypeEnum.PRIVATE_ACCOUNT.getType()).bankAccountName("张三").bankNumber("*************").bankId(1L).subBankName("上海浦东支行").bankProvince("110000").build(),
                SpMainBillBankCardImportExcelVO.builder().spId(20000L).storeId(10000L).cardType(SpBankCardCardTypeEnum.PRIVATE_ACCOUNT.getType()).bankAccountName("张三").bankNumber("*************").bankId(1L).subBankName("上海浦东支行").bankProvince("110000").build()
        );
        // 输出
        ExcelUtils.write(response, "收款账户导入模板.xls", "收款账户列表", SpMainBillBankCardImportExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "导入商户核销账号")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true")
    })
    @PreAuthorize("@ss.hasPermission('sp:main-bill-bank-card:import')")
    public CommonResult<SpMainBillBankCardImportRespVO> importExcel(@RequestParam("file") MultipartFile file,
                                                                @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport) throws Exception {
        List<SpMainBillBankCardImportExcelVO> list = ExcelUtils.read(file, SpMainBillBankCardImportExcelVO.class);
        return success(mainBillBankCardService.importBillBankCardList(list, updateSupport));
    }

}
