package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Schema(description = "管理后台 - 商家商品信息View Response VO")
@Data
public class SpGoodsSpuChannelAppVo {

	private Long appId;

	private String appName;

	private Long channelId;

	private String channelCode;

	private String channelName;

	private Long spuId;

	private Long skuId;

	//渠道相关
	private Long spuChannelId;

	private String channelSpuId;

	private String channelSkuId;

	private String channelCategoryType;

	private String channelCategoryId;

	/**
	 * appPoiType
	 */
	@Schema(description = "应用POI类型")
	private Integer appPoiType;
	/**
	 * appIsCommonCategory
	 */
	@Schema(description = "应用类目类型")
	private Boolean appIsCommonCategory;

	private Integer status;  //0-待同步 1-审核通过 2-审核不通过 3-已同步审核中

	private Integer onShelve; //是否上架

	/**
	 * 状态 0-待审核 1-审核通过 2-审核失败 3-渠道审核中
	 */
	private Integer auditStatus;

	private String auditResult;

	private String auditMsg;

	private Map<String,Object> channelConfig;

}
