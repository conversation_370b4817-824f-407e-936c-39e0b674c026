package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 商家商品导入 Excel 导入 VO
 */
@Schema(description = "管理后台 - 商家商品库存导入 Response VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpGoodsSpuStockImportExcelVO {

    @Schema(description = "商品Spu", required = true, example = "1")
    @ExcelProperty(value = "商品Spu",converter = LongStringConverter.class)
    @NotNull(message = "商品Spu不能为空")
    private Long spuId;

    @Schema(description = "渠道", required = true, example = "1")
    @ExcelProperty("渠道")
    @NotEmpty(message = "渠道不能为空")
    private String channelCode;

    @Schema(description = "商品库存", required = true, example = "1")
    @ExcelProperty("商品库存")
    @NotNull(message = "商品库存不能为空")
    private Long storeNum;

}
