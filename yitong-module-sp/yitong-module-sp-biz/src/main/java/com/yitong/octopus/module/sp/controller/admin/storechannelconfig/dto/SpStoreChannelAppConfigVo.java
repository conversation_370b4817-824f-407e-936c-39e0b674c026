package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 服务商/商家渠道配置信息
 * <AUTHOR>
 */
@Data
public class SpStoreChannelAppConfigVo {

    /**
     * id
     */
    @Schema(description = "平台渠道Id")
    private String channelId;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;

    /**
     * 渠道编码
     */
    @Schema(description = "渠道编码")
    private String channelCode;

    /**
     * appId
     */
    @Schema(description = "应用名称")
    private Long appId;

    /**
     * appId
     */
    @Schema(description = "应用名称")
    private String appName;

    /**
     * appPoiType
     */
    @Schema(description = "应用POI类型")
    private Integer appPoiType;

    /**
     * appIsCommonCategory
     */
    @Schema(description = "应用类目类型")
    private Boolean appIsCommonCategory;
    

    @Schema(description = "商家Id" , example = "4888")
    private Long spId;

    @Schema(description = "门店ID", example = "4888")
    private Long storeId;

    /**
     * 渠道类目
     */
    @Schema(description = "渠道类目")
    private String categoryId;

    /**
     * 渠道类目
     */
    @Schema(description = "渠道类目名称")
    private String categoryName;

    /**
     * 开通的渠道Id
     */
    @Schema(description = "开通的渠道Id,若没有则表示未开通")
    private String id;

    @Schema(description = "渠道商户门店POI", example = "axddd1233")
    private String channelStorePoi;


    /**
     * 渠道返回结果-渠道审核成功之后
     */
    private String channelResult;

    /**
     * 审核状态 0 待审核 1 未通过 2 已通过
     */
    private Integer auditStatus;

    /**
     * 审批备注
     */
    private String auditReason;

    /**
     * 审批时间
     */
    private LocalDateTime auditTime;

    /**
     * 审批人
     */
    private String auditUser;

}
