package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商家资质信息 Excel 导出 Request VO，参数和 SpStoreInfoAqiPageReqVO 是一致的")
@Data
public class SpStoreInfoAqiExportReqVO {

    @Schema(description = "商家门店ID", example = "9436")
    private Long storeId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "资质类型", example = "2")
    private Integer aqiType;

    @Schema(description = "资质编号")
    private String aqiNumber;

    @Schema(description = "资质有效期是否长期有效 0 否，1 是")
    private Integer aqiIsLong;

    @Schema(description = "资质有效期开始")
    private Long aqiStart;

    @Schema(description = "资质有效期结束")
    private Long aqiEnd;

    @Schema(description = "资质电子版")
    private String aqiImg1;

    @Schema(description = "资质电子版2")
    private String aqiImg2;

    @Schema(description = "资质发证机构")
    private String aqiOrg;

    @Schema(description = "资质状态", example = "2")
    private Integer aqiStatus;

}
