package com.yitong.octopus.module.sp.controller.app.goodsspu.vo;

import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.UseRuleVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "前端 - 商品spu Response VO")
@Data
@ToString(callSuper = true)
public class AppSpGoodsSpuRespVO {

    @Schema(description = "主键", required = true, example = "6521")
    private Long id;

    @NotNull(message = "spId不能为空")
    private Long spId;

    @NotEmpty(message = "SPU全称不能为空")
    private String fullName;

    @NotEmpty(message = "shotName不能为空")
    private String shortName;

    private String mainImage;

    private List<String> rotationImages;

    private List<String> images;

    private String features; //特色

    private String flavour; //商品特色

    private Integer featuredType; // 精选类型: 0 普通 ，1 主推

    //使用规则
    private UseRuleVo useRule;
    //售后规则
    private String afterSaleRule;

    /**
     * 精选优先级
     */
    private Integer featuredSort;

    /**
     * 精选描述
     */
    private String featuredRemark;

    /**
     * 商品Sku
     */
    private List<AppSpGoodsSkuRespVO> skus;

    /**
     * 商品适用门店
     */
    private List<AppSpGoodsSpuStoreRespVO> storeList;
}
