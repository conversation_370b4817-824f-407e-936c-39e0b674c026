package com.yitong.octopus.module.sp.service.maininfoauditenterprise;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfoauditenterprise.SpMainInfoAuditEnterpriseDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.sp.convert.maininfoauditenterprise.SpMainInfoAuditEnterpriseConvert;
import com.yitong.octopus.module.sp.dal.mysql.maininfoauditenterprise.SpMainInfoAuditEnterpriseMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 主体认证信息（企业） Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpMainInfoAuditEnterpriseServiceImpl implements SpMainInfoAuditEnterpriseService {

    @Resource
    private SpMainInfoAuditEnterpriseMapper mainInfoAuditEnterpriseMapper;

    @Override
    public Long createMainInfoAuditEnterprise(SpMainInfoAuditEnterpriseCreateReqVO createReqVO) {
        // 插入
        SpMainInfoAuditEnterpriseDO mainInfoAuditEnterprise = SpMainInfoAuditEnterpriseConvert.INSTANCE.convert(createReqVO);
        mainInfoAuditEnterpriseMapper.insert(mainInfoAuditEnterprise);
        // 返回
        return mainInfoAuditEnterprise.getId();
    }

    @Override
    public void updateMainInfoAuditEnterprise(SpMainInfoAuditEnterpriseUpdateReqVO updateReqVO) {
        // 校验存在
        validateMainInfoAuditEnterpriseExists(updateReqVO.getId());
        // 更新
        SpMainInfoAuditEnterpriseDO updateObj = SpMainInfoAuditEnterpriseConvert.INSTANCE.convert(updateReqVO);
        mainInfoAuditEnterpriseMapper.updateById(updateObj);
    }

    @Override
    public void deleteMainInfoAuditEnterprise(Long id) {
        // 校验存在
        validateMainInfoAuditEnterpriseExists(id);
        // 删除
        mainInfoAuditEnterpriseMapper.deleteById(id);
    }

    @Override
    public void deleteMainInfoAuditEnterpriseBySpId(Long spId) {
        // 删除
        mainInfoAuditEnterpriseMapper
                .delete(new LambdaQueryWrapperX<SpMainInfoAuditEnterpriseDO>().eq(SpMainInfoAuditEnterpriseDO::getSpId,spId));
    }

    private void validateMainInfoAuditEnterpriseExists(Long id) {
        if (mainInfoAuditEnterpriseMapper.selectById(id) == null) {
            throw exception(MAIN_INFO_AUDIT_ENTERPRISE_NOT_EXISTS);
        }
    }

    @Override
    public SpMainInfoAuditEnterpriseDO getMainInfoAuditEnterprise(Long id) {
        return mainInfoAuditEnterpriseMapper.selectById(id);
    }

    @Override
    public SpMainInfoAuditEnterpriseViewRespVO getMainInfoAuditEnterpriseByMainId(Long mainId) {
        SpMainInfoAuditEnterpriseDO o = mainInfoAuditEnterpriseMapper.selectOne(
                new LambdaQueryWrapperX<SpMainInfoAuditEnterpriseDO>().eq(SpMainInfoAuditEnterpriseDO::getSpId,mainId));
        return BeanUtil.toBean(o,SpMainInfoAuditEnterpriseViewRespVO.class);
    }

    @Override
    public List<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterpriseList(Collection<Long> ids) {
        return mainInfoAuditEnterpriseMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterprisePage(SpMainInfoAuditEnterprisePageReqVO pageReqVO) {
        return mainInfoAuditEnterpriseMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SpMainInfoAuditEnterpriseDO> getMainInfoAuditEnterpriseList(SpMainInfoAuditEnterpriseExportReqVO exportReqVO) {
        return mainInfoAuditEnterpriseMapper.selectList(exportReqVO);
    }

}
