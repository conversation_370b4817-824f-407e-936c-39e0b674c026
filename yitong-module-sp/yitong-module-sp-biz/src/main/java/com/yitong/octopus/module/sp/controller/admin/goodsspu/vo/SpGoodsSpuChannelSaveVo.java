package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Map;

/**
 * 商品spu DO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家商品信息应用渠道View Response VO")
@Data

@ToString(callSuper = true)
public class SpGoodsSpuChannelSaveVo {

    /**
     * 主键
     */
    private Long id;
    /**
     * 应用Id
     */
    private Long appId;

    /**
     * 渠道ID
     */
    private Long channelId;
    /**
     * Spu ID
     */
    private Long spuId;
    /**
     * Spu ID
     */
    private Long skuId;
    
  /**
   * 渠道目录类型，例如团购..
   */
    private String channelCategoryType;
    
    /**
     * 渠道目录ID
     */
    private String channelCategoryId;
    
    /**
     * 渠道SPU ID
     */
    private String channelSpuId;

    /**
     * 渠道SkU ID
     */
    private String channelSkuId;
    
    /**
     * 状态 0-待审核 1-审核通过 2-审核失败 3-渠道审核中
     */
    private Integer status;
    
    /**
     * 渠道审核结果码
     */
    private String auditResult;
    
    /**
     * 渠道审核结果描述
     */
    private String auditMsg;
    
    /**
     * 渠道是否上架 0-未上架 1-上架
     */
    private Integer onShelve;

    private Map<String,Object> channelExt;

    private Map<String,Object> channelConfig;
}
