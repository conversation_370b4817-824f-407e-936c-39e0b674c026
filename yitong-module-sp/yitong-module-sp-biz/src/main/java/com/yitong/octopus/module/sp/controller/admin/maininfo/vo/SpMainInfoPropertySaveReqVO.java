package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyValueVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商家属性 Request VO")
@Data
public class SpMainInfoPropertySaveReqVO {

    @Schema(description = "商家ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20541")
    @NotNull(message = "商家ID不能为空")
    private Long id;

    @Schema(description = "增加属性值列表", example = "14993")
    private List<PlatformPropertyValueVo> add;

    @Schema(description = "删除属性值列表", example = "14993")
    private List<PlatformPropertyValueVo> remove;

}