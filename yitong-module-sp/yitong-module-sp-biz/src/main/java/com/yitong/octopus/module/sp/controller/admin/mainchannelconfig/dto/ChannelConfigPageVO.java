package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.dto;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 渠道分页
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 服务商渠道配置信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelConfigPageVO extends PageParam {

    @Schema(description = "服务商Id/商家Id", required = true, example = "1")
    @NotNull(message = "服务商Id/商家Id不能为空")
    private Long spId;

}
