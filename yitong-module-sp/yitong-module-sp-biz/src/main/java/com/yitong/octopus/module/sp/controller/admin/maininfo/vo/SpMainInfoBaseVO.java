package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import com.yitong.octopus.module.sp.enums.SpCommonStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
* 主体基本信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainInfoBaseVO {

    @Schema(description = "服务商名称", required = true, example = "王五")
    @NotNull(message = "服务商名称不能为空")
    private String spName;

    @Schema(description = "服务商logo", example = "http://xxx.com/image.png")
    private String spLogo;

    @Schema(description = "主体类型", required = true, example = "1")
    @NotNull(message = "主体类型不能为空")
    private Integer spType;

    @Schema(description = "所在省", required = true)
    @NotNull(message = "所在省不能为空")
    private String spProvince;

    @Schema(description = "所在市", required = true)
    @NotNull(message = "所在市不能为空")
    private String spCity;

    @Schema(description = "所在县", required = true)
    @NotNull(message = "所在县不能为空")
    private String spCounty;

    @Schema(description = "所在镇", required = true)
    private String spTown;

    @Schema(description = "详细地址", required = true)
    @NotNull(message = "详细地址不能为空")
    private String spAdd;

    @Schema(description = "状态 ", example = "2")
    private Integer spStatus = SpCommonStatusEnum.DRAFT.getStatus();

    @Schema(description = "规模", required = true)
    @NotNull(message = "规模不能为空")
    private Integer spScale;

    @Schema(description = "联系人姓名", required = true, example = "王五")
    @NotNull(message = "联系人姓名不能为空")
    private String linkName;

    @Schema(description = "联系人手机", required = true)
    @NotNull(message = "联系人手机不能为空")
    private String linkPhone;

    @Schema(description = "所属主体ID")
    private Long ownerSpId;

    /**
     * 服务商简称
     */
    @Schema(description = "服务商简称")
    private String spShortName;

    /**
     * 行业ID
     */
    @Schema(description = "行业ID")
    private Long industryId;

    /**
     * 子行业ID
     */
    @Schema(description = "子行业ID")
    private Long industrySubId;

//    @Schema(description = "属性数组")
//    private List<PlatformPropertyValueDto> properties;

    /**
     * 商家经营特色
     */
    @Schema(description = "商家经营特色")
    private String spCharacters;

    // 20240314
    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    private Integer poiAuditStatus;

}
