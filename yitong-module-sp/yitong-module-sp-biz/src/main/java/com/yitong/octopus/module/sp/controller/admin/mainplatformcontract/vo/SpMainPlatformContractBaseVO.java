package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 主体平台合同
 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class SpMainPlatformContractBaseVO {

    @Schema(description = "合同类型", required = true, example = "1")
    @NotNull(message = "合同类型不能为空")
    private Integer contractType;

    @Schema(description = "主体ID", example = "28349")
    private Long spId;

    @Schema(description = "主体类型", example = "1")
    private Integer spType;

    /**
     * 合同服务商类型
     *
     * 枚举 {@link TODO sp_main_platform_contract_sp_type 对应的类}
     */
    @Schema(description = "合同服务商类型", required = true, example = "1")
    @NotNull(message = "合同服务商类型不能为空")
    private Integer contractSpType;

    @Schema(description = "合作开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime coopStartTime;

    @Schema(description = "合作结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime coopEndTime;

    @Schema(description = "合作状态", example = "1")
    private Integer coopStatus;

    @Schema(description = "服务商合同文件地址", example = "https://www.iocoder.cn")
    private String spContractUrl;

    @Schema(description = "平台入驻授权函协议", example = "https://www.iocoder.cn")
    private String coopPlatformUrl;

    @Schema(description = "主体合作协议", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "主体合作协议不能为空")
    private String coopUrl;

    @Schema(description = "是否自动结算", required = true, example = "true")
    private Boolean isAutoSettle = false;
}
