package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Schema(description = "管理后台 - 商品sku sample Response VO")
@Data
@ToString(callSuper = true)
public class SpGoodsSkuSampleRespVO {

    @Schema(description = "主键", required = true, example = "25294")
    private Long id;

    @Schema(description = "可用库存")
	private Integer saleStock;

    @Schema(description = "预占库存")
    private Integer occupyStock;

    @Schema(description = "主体Id", example = "19437")
    private Long spId;

    @Schema(description = "商品spuId", required = true, example = "2496")
    private Long spuId;

    @Schema(description = "SKU名称", required = true, example = "14979")
    private String name;

    @Schema(description = "市场价", required = true)
    private Long marketPrice;

    @Schema(description = "售价", required = true)
    private Long salePrice;

    @Schema(description = "结算价", required = true)
    private Long settlePrice;

    @Schema(description = "门店数量", required = true)
    private Integer storeTotal;
}
