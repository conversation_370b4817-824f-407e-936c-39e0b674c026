package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo.xhsminiapp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 服务商渠道配置信息创建 Request VO")
@Data
@ToString(callSuper = true)
public class SpMainXhsMiniAppChannelConfigSaveReqVO {
    /**
     * appId
     */
    @Schema(description = "id", example = "1")

    private Long id;

    /**
     * appId
     */
    @Schema(description = "appId0", required = true, example = "1")
    @NotNull(message = "appId 不能为空")
    private Long appId;

    @Schema(description = "平台渠道ID", required = true, example = "5575")
    @NotEmpty(message = "平台渠道ID不能为空")
    private String channelId;

    @Schema(description = "渠道类目ID", required = true, example = "xx2220xx")
    @NotEmpty(message = "渠道类目ID不能为空")
    private String categoryId;

    @Schema(description = "渠道类目名称", required = true, example = "xx2220xx")
    @NotEmpty(message = "渠道类目名称不能为空")
    private String categoryName;

    @Schema(description = "商家Id", required = true, example = "29498")
    @NotNull(message = "商家Id不能为空")
    private Long spId;

    /**
     * 小红书渠道字典
     * {
     *    xhsUserId: 111,
     *    xhsUserName: '小红书',
     * }
     */
    @Schema(description = "配置信息", required = true, example = "{name:'张三',age:18}")
    @NotEmpty(message = "配置信息不能为空")
    private String configValue;

}
