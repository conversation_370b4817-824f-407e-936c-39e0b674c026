//package com.yitong.octopus.module.sp.controller.admin.industry;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import javax.annotation.Resource;
//
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import com.yitong.octopus.module.sp.controller.admin.industry.vo.CategoryTreeRespVO;
//import com.yitong.octopus.module.sp.dal.mysql.industry.CategoryMapper;
//
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//
//@Tag(name = "管理后台 - 类目")
//@RestController
//@RequestMapping("/sp/category")
//@Validated
//public class CategoryController {
//
//	@Resource
//	private CategoryMapper categoryMapper;
//
//	@GetMapping("/tree")
//	@Operation(summary = "获得类目层级结构")
////	@PreAuthenticated
//	public CommonResult<List<CategoryTreeRespVO>> getTree() {
//		List<CategoryTreeRespVO> list = new ArrayList<>();
//		Map<String, CategoryTreeRespVO> cache = new HashMap<>();
//		categoryMapper.selectList().forEach(category -> {
//			CategoryTreeRespVO level1 = cache.get(category.getLevel1());
//			if (level1 == null) {
//				level1 = new CategoryTreeRespVO(category.getLevel1(), new ArrayList<>());
//				cache.put(level1.getName(), level1);
//				list.add(level1);
//			}
//			CategoryTreeRespVO level2 = cache.get(category.getLevel2());
//			if (level2 == null) {
//				level2 = new CategoryTreeRespVO(category.getLevel2(), new ArrayList<>());
//				cache.put(level2.getName(), level2);
//				level1.getChildren().add(level2);
//			}
//			level2.getChildren().add(new CategoryTreeRespVO(category.getLevel3(), null));
//		});
//
//		return CommonResult.success(list);
//	}
//}
