package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.*;


/**
 * 主体基本信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoExcelVO {

    @ExcelProperty("id")
    private String id;

    @ExcelProperty("系统登录用户ID")
    private Long userId;

    @ExcelProperty("服务商名称")
    private String spName;

    @ExcelProperty(value = "主体类型", converter = DictConvert.class)
    @DictFormat(SP_MAIN_TYPE)
    private Integer spType;

    @ExcelProperty("所在省")
    private String spProvince;

    @ExcelProperty("所在市")
    private String spCity;

    @ExcelProperty("所在县")
    private String spCounty;

    @ExcelProperty("所在镇")
    private String spTown;

    @ExcelProperty("详细地址")
    private String spAdd;

    @ExcelProperty(value = "状态 ", converter = DictConvert.class)
    @DictFormat(SP_MAIN_INFO_STATUS)
    private Integer spStatus;

    @ExcelProperty("规模")
    private Integer spScale;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat(SP_MAIN_INFO_AUDIT_STATUS)
    private Integer auditStatus;

    @ExcelProperty("联系人姓名")
    private String linkName;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    @ExcelProperty(value = "认领状态", converter = DictConvert.class)
    @DictFormat(SP_CLAIM_STATUS)
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    @ExcelProperty(value = "主体资质审核", converter = DictConvert.class)
    @DictFormat(SP_SUBJECT_QUAL_STATUS)
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    @ExcelProperty(value = "行业资质审核", converter = DictConvert.class)
    @DictFormat(SP_INDUSTRY_AUDIT_STATUS)
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    @ExcelProperty(value = "门店资质审核", converter = DictConvert.class)
    @DictFormat(SP_POI_AUDIT_STATUS)
    private Integer poiAuditStatus;
}
