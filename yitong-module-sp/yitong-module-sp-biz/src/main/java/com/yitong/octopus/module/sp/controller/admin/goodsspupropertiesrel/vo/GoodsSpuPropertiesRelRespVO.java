//package com.yitong.octopus.module.sp.controller.admin.goodsspupropertiesrel.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.*;
//import java.time.LocalDateTime;
//import com.alibaba.excel.annotation.*;
//
//@Schema(description = "管理后台 - 商家商品属性关系 Response VO")
//@Data
//@ExcelIgnoreUnannotated
//public class GoodsSpuPropertiesRelRespVO {
//
//    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10219")
//    @ExcelProperty("ID")
//    private Long id;
//
//    @Schema(description = "商家ID", example = "22433")
//    @ExcelProperty("商家ID")
//    private Long spId;
//
//    @Schema(description = "商品ID", example = "1071")
//    @ExcelProperty("商品ID")
//    private Long spuId;
//
//    @Schema(description = "属性ID", example = "3532")
//    @ExcelProperty("属性ID")
//    private Long propertyId;
//
//    @Schema(description = "属性值ID", example = "31528")
//    @ExcelProperty("属性值ID")
//    private Long valueId;
//
//    @Schema(description = "属性值名称", example = "王五")
//    @ExcelProperty("属性值名称")
//    private String valueName;
//
//    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("创建时间")
//    private LocalDateTime createTime;
//
//}