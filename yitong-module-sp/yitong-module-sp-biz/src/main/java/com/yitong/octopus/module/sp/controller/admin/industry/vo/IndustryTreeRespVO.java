package com.yitong.octopus.module.sp.controller.admin.industry.vo;

import java.util.ArrayList;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 行业层级结构 Response VO")
@Data
@ToString(callSuper = true)
public class IndustryTreeRespVO {

	@Schema(description = "ID", required = true, example = "25294")
	private Long id;

	@Schema(description = "名称", required = true, example = "美业")
	private String name;

	@Schema(description = "父行业ID", example = "25293")
	private Long parentId;

	@Schema(description = "子行业", example = "美发")
	private List<IndustryTreeRespVO> children = new ArrayList<>();

}
