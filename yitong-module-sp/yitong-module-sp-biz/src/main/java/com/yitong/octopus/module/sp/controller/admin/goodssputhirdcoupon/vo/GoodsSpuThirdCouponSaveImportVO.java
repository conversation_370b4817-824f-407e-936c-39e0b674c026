package com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品Spu三方券码导入 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class GoodsSpuThirdCouponSaveImportVO {

    @Schema(description = "商品ID", example = "20446")
    @NotNull(message = "商品ID不能为空")
    @ExcelProperty(value = "商品ID",converter = LongStringConverter.class)
    private Long spuId;

    @Schema(description = "券码")
    @NotNull(message = "券码不能为空")
    @ExcelProperty(value = "券码")
    private String couponCode;

    @Schema(description = "核销开始时间")
    @NotNull(message = "核销开始时间不能为空")
    @ExcelProperty(value = "核销开始时间")
    private LocalDateTime validDateFrom;

    @Schema(description = "核销结束时间")
    @NotNull(message = "核销结束时间不能为空")
    @ExcelProperty(value = "核销结束时间")
    private LocalDateTime validDateTo;

}