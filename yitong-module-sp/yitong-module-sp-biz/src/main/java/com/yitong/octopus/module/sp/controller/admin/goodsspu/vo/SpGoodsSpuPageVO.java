package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import java.time.LocalDateTime;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.framework.jackson.core.databind.MoneyF2YSerializer;
import com.yitong.octopus.framework.jackson.core.databind.MoneyY2FDeserializer;
import com.yitong.octopus.module.sp.controller.admin.goodssku.vo.SpGoodsSkuRespVO;
import com.yitong.octopus.module.sp.controller.admin.goodsspu.vo.SpGoodsSpuBaseVO.DateDuration;

import com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo.SpGoodsSpuBilItemRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoWithOwnerRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 商品spu Response VO")
@Data
@ToString(callSuper = true)
public class SpGoodsSpuPageVO {

	@Schema(description = "主键", required = true, example = "6521")
	private Long id;

	@NotNull(message = "spId不能为空")
	private Long spId;

	@NotEmpty(message = "sp名称不能为空")
	private String spName;

	@NotEmpty(message = "SPU全称不能为空")
	private String fullName;

	@NotEmpty(message = "shotName不能为空")
	private String shortName;

	private Integer brandId;

	@NotNull(message = "categoryId不能为空")
	private Long categoryId;

	@JsonSerialize(using = MoneyF2YSerializer.class)
	private Long rank;

	private List<String> labels;

	private String mainImage;

	private List<String>  rotationImages;

	private List<String> images;

	@NotNull(message = "soldDate不能为空")
	@Valid
	private DateDuration soldDate;

	@Schema(description = "创建时间")
	private LocalDateTime createTime;
	
	private Integer status;
	
	private Integer type;

	private Integer onlineStatus;

	private List<SpGoodsSkuRespVO> skus;

	@Schema(description = "spu结算信息")
	private List<SpGoodsSpuBilItemRespVO> billItems;
	
	@Schema(description = "使用前是否需要预约 0-无需预约 1-需要预约")
	private Integer booking;

	/**
	 * 收款方式:1 统一收款 2 分店收款  3区域账户收款
	 */
	@Schema(description = "收款方式:1 统一收款 2分店收款  3区域账户收款 ")
	private Integer settleType;

	/**
	 * 精选类型: 0 普通 ，1 主推
	 */
	@Schema(description = "精选类型: 0 普通 ，1 主推")
	private Integer featuredType;

	/**
	 * 精选优先级
	 */
	@Schema(description = "精选优先级")
	private Integer featuredSort;

	private UseRuleVo useRule;

	@Schema(description = "商家信息")
	SpMainInfoWithOwnerRespVO spInfo;

	/**
	 * 限制单用户的购买数量
	 */
	private Integer totalBuyNum;

}
