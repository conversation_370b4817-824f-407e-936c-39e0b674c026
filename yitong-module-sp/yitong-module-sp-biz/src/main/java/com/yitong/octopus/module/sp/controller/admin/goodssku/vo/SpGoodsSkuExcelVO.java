package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import lombok.*;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品sku Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpGoodsSkuExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店Id")
    private Long storeId;

    @ExcelProperty("商品spuId")
    private Long spuId;

    @ExcelProperty("市场价")
    private Long marketAmount;

    @ExcelProperty("售价")
    private Long saleAmount;

    @ExcelProperty("即时库存")
    private Integer billStock;

    @ExcelProperty("占用库存")
    private Integer occupyStock;

    @ExcelProperty("可用库存")
    private Integer saleStock;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
