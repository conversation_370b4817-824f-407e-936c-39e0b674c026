package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 门店审核记录 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoAuditLogExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("门店Id")
    private Long storeId;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("主体名称")
    private String spName;

    @ExcelProperty("审核状态")
    private Integer auditStatus;

    @ExcelProperty("审批人")
    private Long auditUserId;

    @ExcelProperty("审批备注")
    private String auditReason;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
