package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storechannelconfiglog.SpStoreChannelConfigLogDO;
import com.yitong.octopus.module.sp.convert.storechannelconfiglog.SpStoreChannelConfigLogConvert;
import com.yitong.octopus.module.sp.service.storechannelconfig.SpStoreChannelConfigLogService;

@Tag(name = "管理后台 - 商家门店渠道配置信息表日志")
@RestController
@RequestMapping("/sp/store-channel-config-log")
@Validated
public class SpStoreChannelConfigLogController {

    @Resource
    private SpStoreChannelConfigLogService storeChannelConfigLogService;

    @PostMapping("/create")
    @Operation(summary = "创建商家门店渠道配置信息表日志")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:create')")
    public CommonResult<Long> createStoreChannelConfigLog(@Valid @RequestBody SpStoreChannelConfigLogCreateReqVO createReqVO) {
        return success(storeChannelConfigLogService.createStoreChannelConfigLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商家门店渠道配置信息表日志")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:update')")
    public CommonResult<Boolean> updateStoreChannelConfigLog(@Valid @RequestBody SpStoreChannelConfigLogUpdateReqVO updateReqVO) {
        storeChannelConfigLogService.updateStoreChannelConfigLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商家门店渠道配置信息表日志")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:delete')")
    public CommonResult<Boolean> deleteStoreChannelConfigLog(@RequestParam("id") Long id) {
        storeChannelConfigLogService.deleteStoreChannelConfigLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商家门店渠道配置信息表日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:query')")
    public CommonResult<SpStoreChannelConfigLogRespVO> getStoreChannelConfigLog(@RequestParam("id") Long id) {
        SpStoreChannelConfigLogDO storeChannelConfigLog = storeChannelConfigLogService.getStoreChannelConfigLog(id);
        return success(SpStoreChannelConfigLogConvert.INSTANCE.convert(storeChannelConfigLog));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商家门店渠道配置信息表日志列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:query')")
    public CommonResult<List<SpStoreChannelConfigLogRespVO>> getStoreChannelConfigLogList(@RequestParam("ids") Collection<Long> ids) {
        List<SpStoreChannelConfigLogDO> list = storeChannelConfigLogService.getStoreChannelConfigLogList(ids);
        return success(SpStoreChannelConfigLogConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商家门店渠道配置信息表日志分页")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:query')")
    public CommonResult<PageResult<SpStoreChannelConfigLogRespVO>> getStoreChannelConfigLogPage(@Valid SpStoreChannelConfigLogPageReqVO pageVO) {
        PageResult<SpStoreChannelConfigLogDO> pageResult = storeChannelConfigLogService.getStoreChannelConfigLogPage(pageVO);
        return success(SpStoreChannelConfigLogConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商家门店渠道配置信息表日志 Excel")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config-log:export')")
    public void exportStoreChannelConfigLogExcel(@Valid SpStoreChannelConfigLogExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpStoreChannelConfigLogDO> list = storeChannelConfigLogService.getStoreChannelConfigLogList(exportReqVO);
        // 导出 Excel
        List<SpStoreChannelConfigLogExcelVO> datas = SpStoreChannelConfigLogConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商家门店渠道配置信息表日志.xls", "数据", SpStoreChannelConfigLogExcelVO.class, datas);
    }

}
