package com.yitong.octopus.module.sp.controller.admin.goodsspuparams;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspuparams.SpGoodsSpuParamsDO;
import com.yitong.octopus.module.sp.convert.goodsspuparams.SpGoodsSpuParamsConvert;
import com.yitong.octopus.module.sp.service.goodsspuparams.SpGoodsSpuParamsService;

@Tag(name = "管理后台 - 商品spu")
@RestController
@RequestMapping("/sp/goods-spu-params")
@Validated
public class SpGoodsSpuParamsController {

    @Resource
    private SpGoodsSpuParamsService goodsSpuParamsService;

    @PostMapping("/create")
    @Operation(summary = "创建商品spu")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:create')")
    public CommonResult<Long> createGoodsSpuParams(@Valid @RequestBody SpGoodsSpuParamsCreateReqVO createReqVO) {
        return success(goodsSpuParamsService.createGoodsSpuParams(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品spu")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:update')")
    public CommonResult<Boolean> updateGoodsSpuParams(@Valid @RequestBody SpGoodsSpuParamsUpdateReqVO updateReqVO) {
        goodsSpuParamsService.updateGoodsSpuParams(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品spu")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:delete')")
    public CommonResult<Boolean> deleteGoodsSpuParams(@RequestParam("id") Long id) {
        goodsSpuParamsService.deleteGoodsSpuParams(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品spu")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:query')")
    public CommonResult<SpGoodsSpuParamsRespVO> getGoodsSpuParams(@RequestParam("id") Long id) {
        SpGoodsSpuParamsDO goodsSpuParams = goodsSpuParamsService.getGoodsSpuParams(id);
        return success(SpGoodsSpuParamsConvert.INSTANCE.convert(goodsSpuParams));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品spu列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:query')")
    public CommonResult<List<SpGoodsSpuParamsRespVO>> getGoodsSpuParamsList(@RequestParam("ids") Collection<Long> ids) {
        List<SpGoodsSpuParamsDO> list = goodsSpuParamsService.getGoodsSpuParamsList(ids);
        return success(SpGoodsSpuParamsConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品spu分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:query')")
    public CommonResult<PageResult<SpGoodsSpuParamsRespVO>> getGoodsSpuParamsPage(@Valid SpGoodsSpuParamsPageReqVO pageVO) {
        PageResult<SpGoodsSpuParamsDO> pageResult = goodsSpuParamsService.getGoodsSpuParamsPage(pageVO);
        return success(SpGoodsSpuParamsConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品spu Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-params:export')")
    public void exportGoodsSpuParamsExcel(@Valid SpGoodsSpuParamsExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpGoodsSpuParamsDO> list = goodsSpuParamsService.getGoodsSpuParamsList(exportReqVO);
        // 导出 Excel
        List<SpGoodsSpuParamsExcelVO> datas = SpGoodsSpuParamsConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商品spu.xls", "数据", SpGoodsSpuParamsExcelVO.class, datas);
    }

}
