package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体结算银行卡创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainBillBankCardCreateReqVO extends SpMainBillBankCardBaseVO {

    @Schema(description = "开户银行所在县")
    private String bankCounty;

    @Schema(description = "开户银行所在镇", required = true)
    private String bankTown;

    @Schema(description = "开户银行所在县id", example = "22114")
    private Integer bankCountyId;

    @Schema(description = "开户银行所在镇id", example = "25662")
    private Integer bankTownId;

}
