package com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店图片创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoGalleryCreateReqVO extends SpStoreInfoGalleryBaseVO {

    @Schema(description = "创建人ID", required = true, example = "26331")
    private Long createUserId;

}
