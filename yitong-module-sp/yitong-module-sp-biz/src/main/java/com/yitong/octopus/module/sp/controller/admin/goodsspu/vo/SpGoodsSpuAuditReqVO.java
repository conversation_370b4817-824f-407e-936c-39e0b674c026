package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.yitong.octopus.framework.common.validation.InEnum;
import com.yitong.octopus.module.sp.enums.SpGoodsSpuStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商品 SPU 审核 Request VO")
@Data
public class SpGoodsSpuAuditReqVO {

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品编号不能为空")
    private List<Long> ids;

    @Schema(description = "商品状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "商品状态不能为空")
    @InEnum(SpGoodsSpuStatusEnum.class)
    private Integer status;

}
