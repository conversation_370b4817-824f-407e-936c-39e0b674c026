package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.yitong.octopus.module.platform.controller.admin.brand.vo.PlatformBrandRespVO;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryRespVO;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.SpMainInfoRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoPageRespVO extends SpStoreInfoBaseVO {

    @Schema(description = "id", required = true, example = "25053")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    //所属商家、所属类目、关联品牌、高德POI链接、最后修改人、最后修改时间
    @Schema(description = "所属商家", example = "25053")
    private SpMainInfoRespVO spMainInfoRespVO;

    @Schema(description = "所属类目", example = "25053")
    private PlatformCategoryRespVO platformCategoryRespVO;

    @Schema(description = "关联品牌", example = "25053")
    private PlatformBrandRespVO platformBrandRespVO;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
}
