package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 主体审核记录
 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoAuditLogExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("主体名称")
    private String spName;

    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("sp_info_audit_audit_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer auditStatus;

    @ExcelProperty("审批人")
    private Long auditUserId;

    @ExcelProperty("审批备注")
    private String auditReason;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
