package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

@Schema(description = "管理后台 - 服务商渠道配置信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainChannelConfigUpdateReqVO extends SpMainChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "12200")
    @NotNull(message = "ID不能为空")
    private Long id;

}
