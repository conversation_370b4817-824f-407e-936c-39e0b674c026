package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体基本信息更新 Request VO")
@Data
@ToString(callSuper = true)
public class SpMainBillBankCardAuditReqVO {

    @Schema(description = "主体Id",required = true, example = "29684")
    @NotNull(message = "id不能为空")
    private List<Long> ids;

    @Schema(description = "审核状态",required = true, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

}
