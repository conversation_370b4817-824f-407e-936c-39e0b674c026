package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体素材分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoMaterialsPageReqVO extends PageParam {

    @Schema(description = "商家名称ID", example = "10336")
    private Long spId;

    @Schema(description = "商家", example = "10336")
    private String spSearch;

    @Schema(description = "门店ID", example = "12719")
    private Long storeId;

    @Schema(description = "商家门店", example = "10336")
    private String storeSearch;


    @Schema(description = "类型", example = "1")
    private String type;

    @Schema(description = "文件类型", example = "2")
    private String fileType;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
