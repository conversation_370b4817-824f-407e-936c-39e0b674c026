package com.yitong.octopus.module.sp.controller.admin.userdatascop.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户与商家权限数据权限 Excel 导出 Request VO，参数和 UserDataScopPageReqVO 是一致的")
@Data
public class SpUserDataScopeExportReqVO {

    @Schema(description = "用户ID", example = "21387")
    private Long userId;

    @Schema(description = "数据范围（1：全部数据权限 2：自定数据权限 3：本商家/服务商数据权限 4：本服务商商家及以下数据权限）")
    private Byte dataScope;

    @Schema(description = "数据范围(指定部门数组)")
    private String dataScopeSpIds;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
