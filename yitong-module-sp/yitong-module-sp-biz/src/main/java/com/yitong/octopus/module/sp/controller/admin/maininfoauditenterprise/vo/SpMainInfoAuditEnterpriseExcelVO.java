package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 主体认证信息（企业） Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoAuditEnterpriseExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体ID")
    private Long spId;

    @ExcelProperty("法人姓名")
    private String legalName;

    @ExcelProperty("营业执照号")
    private String licenseNum;

    @ExcelProperty("营业执照所在省")
    private String licenseProvince;

    @ExcelProperty("营业执照所在市")
    private String licenseCity;

    @ExcelProperty("营业执照所在县")
    private String licenseCounty;

    @ExcelProperty("营业执照所在镇")
    private String licenseTown;

    @ExcelProperty(value = "营业执照是否长期有效 0 否，1 是", converter = DictConvert.class)
    @DictFormat("sp_main_license_is_long") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer licenseIsLong;

    @ExcelProperty("营业执照电子版")
    private String licenseImg;

    @ExcelProperty("银行开户名")
    private String bankAccountName;

    @ExcelProperty("开户银行支行名称")
    private String bankName;

    @ExcelProperty("开户银行所在省")
    private String bankProvince;

    @ExcelProperty("开户银行所在市")
    private String bankCity;

    @ExcelProperty("开户银行所在县")
    private String bankCounty;

    @ExcelProperty("开户银行所在镇")
    private String bankTown;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
