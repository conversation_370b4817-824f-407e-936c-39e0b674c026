package com.yitong.octopus.module.sp.controller.admin.industry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 行业 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndustryRespVO extends IndustryBaseVO {

    @Schema(description = "ID", required = true, example = "25294")
    private Long id;
    
    @Schema(description = "上级行业ID", required = true, example = "25294")
    private Long parentId;
    
}
