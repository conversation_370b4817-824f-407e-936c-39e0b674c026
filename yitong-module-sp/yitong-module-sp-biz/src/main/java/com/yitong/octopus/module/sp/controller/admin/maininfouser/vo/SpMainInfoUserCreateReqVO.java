package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商户子账号创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoUserCreateReqVO extends SpMainInfoUserBaseVO {

    @Schema(description = "商户ID", required = true, example = "21695")
    @NotNull(message = "商户ID不能为空")
    private Long spId;

}
