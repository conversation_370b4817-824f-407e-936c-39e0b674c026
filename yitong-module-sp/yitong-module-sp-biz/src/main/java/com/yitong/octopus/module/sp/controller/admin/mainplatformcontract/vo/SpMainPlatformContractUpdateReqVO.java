package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.dto.SpMainPlatformContractBillItemUpdateReqDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体平台合同更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractUpdateReqVO extends SpMainPlatformContractBaseVO {

    @Schema(description = "id", required = true, example = "10360")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "联系人手机", required = true)
    @NotNull(message = "联系人手机不能为空")
    private String linkPhone;

    @Schema(description = "联系人电话")
    private String linkTel;

    @Schema(description = "联系人邮箱")
    private String linkEmail;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "合同结算项", example = "随便")
    @NotNull(message = "合同结算项不能为空")
    List<SpMainPlatformContractBillItemUpdateReqDto> contractBillItemList;
}
