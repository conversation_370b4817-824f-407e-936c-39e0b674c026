package com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - 商品Spu三方券码分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GoodsSpuThirdCouponPageReqVO extends PageParam {

    @Schema(description = "商品ID", example = "20446")
    private Long spuId;

    @Schema(description = "券码")
    private String couponCode;

    @Schema(description = "状态： 1 有效 2 已使用 3 已过期", example = "1")
    private Integer status;

}