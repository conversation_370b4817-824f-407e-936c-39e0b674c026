package com.yitong.octopus.module.sp.controller.admin.maininfoauditpersonal.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体认证信息-个人分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditPersonalPageReqVO extends PageParam {

    @Schema(description = "主体ID", example = "27254")
    private Long spId;

    @Schema(description = "法人姓名", example = "王五")
    private String legalName;

    @Schema(description = "法人身份证号")
    private String idCard;

    @Schema(description = "银行开户名", example = "赵六")
    private String bankAccountName;

    @Schema(description = "银行开户账号")
    private String bankNumber;

    @Schema(description = "开户银行支行名称", example = "李四")
    private String bankName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
