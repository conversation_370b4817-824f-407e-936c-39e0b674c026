package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

/**
 * 审核日志
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 主体审核记录创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAuditLogCreateReqVO extends SpMainInfoAuditLogBaseVO {

}
