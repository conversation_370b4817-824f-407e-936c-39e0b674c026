package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
* 主体结算银行卡 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainBillBankCardBaseVO {

    @Schema(description = "商家ID", required = true, example = "19184")
    @NotNull(message = "服务商ID不能为空")
    private Long spId;

    @Schema(description = "门店ID", example = "19184")
    private Long storeId;

    @Schema(description = "类型 1 总部账户 2 门店账户", example = "19184")
    @NotNull(message = "账户类型不能为空")
    private Integer type;

    @Schema(description = "收款账户类型 1 对公账户 2 个人账户", example = "19184")
    @NotNull(message = "收款账户类型不能为空")
    private Integer cardType;

    @Schema(description = "开户银行", example = "19184")
    @NotNull(message = "开户银行不能为空")
    private Long bankId;

    @Schema(description = "收款账户名称", required = true, example = "赵六")
    @NotEmpty(message = "收款账户名称不能为空")
    private String bankAccountName;

    @Schema(description = "银行卡号", required = true)
    @NotEmpty(message = "银行卡号不能为空")
    private String bankNumber;

    @Schema(description = "开户支行", required = true, example = "张三")
    @NotEmpty(message = "开户支行不能为空")
    private String subBankName;

    @Schema(description = "开户银行所在省", required = true)
    @NotEmpty(message = "开户银行所在省不能为空")
    private String bankProvince;

    @Schema(description = "开户银行所在省id", required = true, example = "7861")
    @NotNull(message = "开户银行所在省id不能为空")
    private Integer bankProvinceId;

    @Schema(description = "开户银行所在市", required = true)
    private String bankCity;

    @Schema(description = "开户银行所在市id", required = true, example = "32471")
    private Integer bankCityId;

}
