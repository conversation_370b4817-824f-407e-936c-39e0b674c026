package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 门店审核记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoAuditLogPageReqVO extends PageParam {

    @Schema(description = "门店Id", example = "3656")
    private Long storeId;

    @Schema(description = "主体Id", example = "10052")
    private Long spId;

    @Schema(description = "主体名称", example = "王五")
    private String spName;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审批人", example = "5765")
    private Long auditUserId;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
