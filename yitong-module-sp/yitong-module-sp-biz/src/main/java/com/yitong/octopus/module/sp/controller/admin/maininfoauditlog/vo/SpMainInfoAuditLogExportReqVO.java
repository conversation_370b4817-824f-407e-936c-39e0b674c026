package com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体审核记录Excel 导出 Request VO，参数和 SpMainInfoAuditLogPageReqVO 是一致的")
@Data
public class SpMainInfoAuditLogExportReqVO {

    @Schema(description = "主体Id", example = "29684")
    private Long spId;

    @Schema(description = "主体名称", example = "王五")
    private String spName;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

    @Schema(description = "审批时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
