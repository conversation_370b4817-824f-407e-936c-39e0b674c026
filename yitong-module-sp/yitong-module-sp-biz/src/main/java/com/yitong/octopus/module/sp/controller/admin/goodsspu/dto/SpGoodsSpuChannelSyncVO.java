package com.yitong.octopus.module.sp.controller.admin.goodsspu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 渠道分页
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商品渠道同步 Request VO")
@Data
@ToString(callSuper = true)
public class SpGoodsSpuChannelSyncVO{

    @Schema(description = "商品Id", required = true, example = "1")
    @NotNull(message = "商品不能为空")
    private Long spGoodsId;

    @NotEmpty
    @NotNull(message = "渠道不能为空")
    private String channelCode;

}
