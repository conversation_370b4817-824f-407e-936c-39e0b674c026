package com.yitong.octopus.module.sp.controller.admin.mainplatformcontract.vo;

import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.dto.SpMainPlatformContractBillItemCreateReqDto;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;
import java.util.List;

@Schema(description = "管理后台 - 主体平台合同创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractCreateReqVO extends SpMainPlatformContractBaseVO {

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "合同结算项", example = "随便")
    @NotNull(message = "合同结算项不能为空")
    List<SpMainPlatformContractBillItemCreateReqDto> contractBillItemList;
}
