package com.yitong.octopus.module.sp.controller.admin.industry.vo;

import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;

@Schema(description = "管理后台 - 类目层级结构 Response VO")
@Data
@AllArgsConstructor
public class CategoryTreeRespVO {

	@Schema(description = "名称", required = true, example = "美业")
	private String name;

	@Schema(description = "子类目", example = "美发")
	private List<CategoryTreeRespVO> children;

}
