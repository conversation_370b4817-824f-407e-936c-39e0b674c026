package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;
import com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.dto.SpStoreInfoAqiDto;
import com.yitong.octopus.module.sp.controller.admin.storeinfomap.dto.SpStoreInfoMapDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 商家门店信息渠道初始化 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoInitRespVO extends SpStoreInfoBaseVO {

    @Schema(description = "id", required = true, example = "25053")
    private Long id;

    @Schema(description = "资质信息信息")
    private SpStoreInfoAqiDto spStoreInfoAqiDtoList;

    @Schema(description = "门店地图")
    private SpStoreInfoMapDto spStoreInfoMapDto;

    @Schema(description = "营业执照号")
    private String licenseNum;

    @Schema(description = "营业执照电子版")
    private String licenseImg;

    @Schema(description = "营业执照是否长期有效")
    private Integer licenseIsLong;

    @Schema(description = "营业执照有效期开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime licenseStart;

    @Schema(description = "营业执照有效期结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime licenseEnd;

}
