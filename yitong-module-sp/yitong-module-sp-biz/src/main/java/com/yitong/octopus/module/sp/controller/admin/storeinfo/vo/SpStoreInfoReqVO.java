package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店信息 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class SpStoreInfoReqVO {

    @Schema(description = "门店Id", required = true, example = "8918")
    @NotNull(message = "门店不能为空")
    private Long id;

    @Schema(description = "门店名称", required = true, example = "12694")
    @NotEmpty(message = "门店名称不能为空")
    private String storeName;

}
