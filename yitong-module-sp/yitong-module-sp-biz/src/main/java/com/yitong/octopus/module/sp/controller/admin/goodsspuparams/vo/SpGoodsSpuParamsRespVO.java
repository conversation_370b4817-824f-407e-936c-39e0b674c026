package com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品spu Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuParamsRespVO extends SpGoodsSpuParamsBaseVO {

    @Schema(description = "主键", required = true, example = "14241")
    private Long id;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}
