package com.yitong.octopus.module.sp.service.mainplatformcontractbillitem;

import java.util.*;
import javax.validation.*;
import javax.validation.constraints.NotEmpty;

import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainplatformcontractbillitem.SpMainPlatformContractBillItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

/**
 * 主体平台合同结算信息 Service 接口
 *
 * <AUTHOR>
 */
public interface SpMainPlatformContractBillItemService {

    /**
     * 创建主体平台合同结算信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMainPlatformContractBillItem(@Valid SpMainPlatformContractBillItemCreateReqVO createReqVO);

    /**
     * 更新主体平台合同结算信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMainPlatformContractBillItem(@Valid SpMainPlatformContractBillItemUpdateReqVO updateReqVO);

    /**
     * 删除主体平台合同结算信息
     *
     * @param id 编号
     */
    void deleteMainPlatformContractBillItem(Long id);

    /**
     * 根据合同编号，删除主体平台合同结算信息
     *
     * @param spContractId 合同编号
     */
    void deleteMainPlatformContractBillItemByContractId(Long spContractId);

    /**
     * 根据合同编号，删除主体平台合同结算信息
     *
     * @param spId 主体Id
     */
    void deleteMainPlatformContractBillItemBySpId(Long spId);

    /**
     * 获得主体平台合同结算信息
     *
     * @param id 编号
     * @return 主体平台合同结算信息
     */
    SpMainPlatformContractBillItemDO getMainPlatformContractBillItem(Long id);

    /**
     * 获得主体平台合同结算信息列表
     *
     * @param ids 编号
     * @return 主体平台合同结算信息列表
     */
    List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemList(Collection<Long> ids);

    /**
     * 获得主体平台合同结算信息列表
     *
     * @param spContractId 合同编号
     * @return 主体平台合同结算信息列表
     */
    List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemListByContractId(Long spContractId);


    /**
     * 获得主体平台合同结算信息分页
     *
     * @param pageReqVO 分页查询
     * @return 主体平台合同结算信息分页
     */
    PageResult<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemPage(SpMainPlatformContractBillItemPageReqVO pageReqVO);

    /**
     * 获得主体平台合同结算信息列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 主体平台合同结算信息列表
     */
    List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemList(SpMainPlatformContractBillItemExportReqVO exportReqVO);

    /**
     * 创建主体平台合同结算信息
     *
     * @param createReqVOList 创建信息列表
     * @return 编号
     */
    boolean createMainPlatformContractBillItemByList(@Valid @NotEmpty List<SpMainPlatformContractBillItemCreateReqVO> createReqVOList);

    /**
     * 更新主体平台合同结算信息
     *
     * @param updateReqVOList 更新信息列表
     * @return 编号
     */
    boolean updateMainPlatformContractBillItemByList(@Valid @NotEmpty List<SpMainPlatformContractBillItemUpdateReqVO> updateReqVOList);
}
