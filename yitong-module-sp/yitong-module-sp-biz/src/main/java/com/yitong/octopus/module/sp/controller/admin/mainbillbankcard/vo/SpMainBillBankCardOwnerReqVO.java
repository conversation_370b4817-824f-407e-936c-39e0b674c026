package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 主体结算银行卡 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainBillBankCardOwnerReqVO {

    @Schema(description = "商家ID", required = true, example = "19184")
    @NotNull(message = "服务商ID不能为空")
    private Long spId;

    @Schema(description = "类型 1 总部账户 2 门店账户", example = "19184")
    @NotNull(message = "账户类型不能为空")
    private Integer type;

    @Schema(description = "门店ID", example = "19184")
    private Long storeId;

}
