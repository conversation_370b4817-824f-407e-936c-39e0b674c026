package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体基本信息 Excel 导出 Request VO，参数和 SpMainInfoPageReqVO 是一致的")
@Data
public class SpMainInfoExportReqVO {

    @Schema(description = "服务商Id", example = "王五")
    private Long id;

    @Schema(description = "服务商名称", example = "王五")
    private String spName;

    @Schema(description = "主体类型", example = "1")
    private Integer spType;

    @Schema(description = "所在省id", example = "7635")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "772")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "13418")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "6052")
    private Integer spTownId;

    @Schema(description = "状态 ", example = "2")
    private Integer spStatus;

    @Schema(description = "联系人姓名", example = "王五")
    private String linkName;

    @Schema(description = "所属服务商Id ", example = "2")
    private Long ownerSpId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;



}
