package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;


@Schema(description = "管理后台 - 商家门店标签关系批量 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpStoreInfoBatchTagRelSaveReqVO {

    @Schema(description = "商家门店标签列表", example = "14993")
    @NotNull(message = "商家门店标签不能为空")
    private List<SpStoreInfoTagRelSaveReqVO> tags;

}