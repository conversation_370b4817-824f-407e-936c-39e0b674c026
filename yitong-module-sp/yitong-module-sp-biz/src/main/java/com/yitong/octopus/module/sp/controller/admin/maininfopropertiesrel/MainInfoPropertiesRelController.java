//package com.yitong.octopus.module.sp.controller.admin.maininfopropertiesrel;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletResponse;
//import javax.validation.Valid;
//
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageParam;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import com.yitong.octopus.framework.common.util.object.BeanUtils;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.maininfopropertiesrel.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.maininfopropertiesrel.MainInfoPropertiesRelDO;
//import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoPropertiesRelService;
//
//@Tag(name = "管理后台 - 商家属性关系")
//@RestController
//@RequestMapping("/sp/main-info-properties-rel")
//@Validated
//public class MainInfoPropertiesRelController {
//
//    @Resource
//    private SpMainInfoPropertiesRelService spMainInfoPropertiesRelService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家属性关系")
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:create')")
//    public CommonResult<Long> createMainInfoPropertiesRel(@Valid @RequestBody MainInfoPropertiesRelSaveReqVO createReqVO) {
//        return success(spMainInfoPropertiesRelService.createMainInfoPropertiesRel(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家属性关系")
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:update')")
//    public CommonResult<Boolean> updateMainInfoPropertiesRel(@Valid @RequestBody MainInfoPropertiesRelSaveReqVO updateReqVO) {
//        spMainInfoPropertiesRelService.updateMainInfoPropertiesRel(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家属性关系")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:delete')")
//    public CommonResult<Boolean> deleteMainInfoPropertiesRel(@RequestParam("id") Long id) {
//        spMainInfoPropertiesRelService.deleteMainInfoPropertiesRel(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家属性关系")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:query')")
//    public CommonResult<MainInfoPropertiesRelRespVO> getMainInfoPropertiesRel(@RequestParam("id") Long id) {
//        MainInfoPropertiesRelDO mainInfoPropertiesRel = spMainInfoPropertiesRelService.getMainInfoPropertiesRel(id);
//        return success(BeanUtils.toBean(mainInfoPropertiesRel, MainInfoPropertiesRelRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家属性关系分页")
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:query')")
//    public CommonResult<PageResult<MainInfoPropertiesRelRespVO>> getMainInfoPropertiesRelPage(@Valid MainInfoPropertiesRelPageReqVO pageReqVO) {
//        PageResult<MainInfoPropertiesRelDO> pageResult = spMainInfoPropertiesRelService.getMainInfoPropertiesRelPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, MainInfoPropertiesRelRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家属性关系 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:main-info-properties-rel:export')")
//    @OperateLog(type = EXPORT)
//    public void exportMainInfoPropertiesRelExcel(@Valid MainInfoPropertiesRelPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<MainInfoPropertiesRelDO> list = spMainInfoPropertiesRelService.getMainInfoPropertiesRelPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "商家属性关系.xls", "数据", MainInfoPropertiesRelRespVO.class,
//                        BeanUtils.toBean(list, MainInfoPropertiesRelRespVO.class));
//    }
//
//}