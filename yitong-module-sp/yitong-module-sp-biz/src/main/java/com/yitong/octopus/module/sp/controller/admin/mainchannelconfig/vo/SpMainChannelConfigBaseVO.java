package com.yitong.octopus.module.sp.controller.admin.mainchannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
* 服务商渠道配置信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class SpMainChannelConfigBaseVO {

    /**
     * appId
     */
    @Schema(description = "appId", example = "1")
    private Long appId;

    @Schema(description = "服务商ID", example = "29498")
    private String spId;

    @Schema(description = "渠道类目ID", example = "xx2220xx")
    private String categoryId;

    @Schema(description = "平台渠道ID", example = "5575")
    private String channelId;

    /**
     * 小红书渠道字典
     * {
     *    xhsUserId: 111,
     *    xhsUserName: '小红书',
     * }
     */
    @Schema(description = "配置信息", required = true)
    @NotNull(message = "配置信息不能为空")
    private String configValue;

    @Schema(description = "审核状态 0 待审核 1 未通过 2 已通过", example = "1")
    private Integer auditStatus;

//    @Schema(description = "审批人", example = "6434")
//    private Long auditUserId;
//
//    @Schema(description = "审批备注", example = "不喜欢")
//    private String auditReason;
//
//    @Schema(description = "审批时间")
//    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
//    private LocalDateTime auditTime;

}
