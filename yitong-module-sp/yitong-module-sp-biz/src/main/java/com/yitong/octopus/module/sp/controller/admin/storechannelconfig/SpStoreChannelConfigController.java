package com.yitong.octopus.module.sp.controller.admin.storechannelconfig;

import cn.hutool.core.bean.BeanUtil;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelAppConfigVo;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigPageVo;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto.SpStoreChannelConfigVo;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapp.SpStoreXhsMiniAppChannelConfigRespVO;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapp.SpStoreXhsMiniAppChannelConfigSaveReqVO;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapp.SpStoreXhsMiniAppChannelConfigUpdateReqVO;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi.SpStoreXhsMiniAppChannelPOIConfigRespVO;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi.SpStoreXhsMiniAppChannelPOIConfigSaveReqVO;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi.SpStoreXhsMiniAppChannelPOIConfigUpdateReqVO;
import com.yitong.octopus.module.sp.service.storechannelconfig.SpStoreXhsMiniAppChannelPOIConfigService;
import com.yitong.octopus.module.sp.service.storechannelconfig.SpStoreXhsMiniAppChannelConfigService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.servlet.http.*;
import javax.validation.Valid;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storechannelconfig.SpStoreChannelConfigDO;
import com.yitong.octopus.module.sp.convert.storechannelconfig.SpStoreChannelConfigConvert;
import com.yitong.octopus.module.sp.service.storechannelconfig.SpStoreChannelConfigService;

@Tag(name = "管理后台 - 服务商门店-渠道配置信息")
@RestController
@RequestMapping("/sp/store-channel-config")
@Validated
public class SpStoreChannelConfigController {

    @Resource
    private SpStoreChannelConfigService storeChannelConfigService;
    @Resource
    private SpStoreXhsMiniAppChannelConfigService storeXhsMiniAppChannelConfigService;
    @Resource
    private SpStoreXhsMiniAppChannelPOIConfigService storeXhsMiniAppChannelPOIConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建服务商门店-渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:create')")
    public CommonResult<Long> createStoreChannelConfig(@Valid @RequestBody SpStoreChannelConfigCreateReqVO createReqVO) {
        return success(storeChannelConfigService.createOrUpdateChannelConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务商门店-渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:update')")
    public CommonResult<Boolean> updateStoreChannelConfig(@Valid @RequestBody SpStoreChannelConfigUpdateReqVO updateReqVO) {
        storeChannelConfigService.updateStoreChannelConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务商门店-渠道配置信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:delete')")
    public CommonResult<Boolean> deleteStoreChannelConfig(@RequestParam("id") Long id) {
        storeChannelConfigService.deleteStoreChannelConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务商门店-渠道配置信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<SpStoreChannelConfigViewReqVO> getStoreChannelConfig(@RequestParam("id") Long id) {
        SpStoreChannelConfigDO storeChannelConfig = storeChannelConfigService.getStoreChannelConfig(id);
        return success(SpStoreChannelConfigConvert.INSTANCE.convert(storeChannelConfig));
    }

    @GetMapping("/list")
    @Operation(summary = "获得服务商门店-渠道配置信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<List<SpStoreChannelConfigViewReqVO>> getStoreChannelConfigList(@RequestParam("ids") Collection<Long> ids) {
        List<SpStoreChannelConfigDO> list = storeChannelConfigService.getStoreChannelConfigList(ids);
        return success(SpStoreChannelConfigConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务商门店-渠道配置信息分页")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<PageResult<SpStoreChannelConfigViewReqVO>> getStoreChannelConfigPage(@Valid SpStoreChannelConfigPageReqVO pageVO) {
        PageResult<SpStoreChannelConfigDO> pageResult = storeChannelConfigService.getStoreChannelConfigPage(pageVO);
        return success(SpStoreChannelConfigConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出服务商门店-渠道配置信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:export')")
    public void exportStoreChannelConfigExcel(@Valid SpStoreChannelConfigExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpStoreChannelConfigDO> list = storeChannelConfigService.getStoreChannelConfigList(exportReqVO);
        // 导出 Excel
        List<SpStoreChannelConfigExcelVO> datas = SpStoreChannelConfigConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "服务商门店-渠道配置信息.xls", "数据", SpStoreChannelConfigExcelVO.class, datas);
    }

    @GetMapping("/channel-list")
    @Operation(summary = "获得商家门店渠道配置信息列表")
    @Parameter(name = "storeId", description = "商家门店Id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<List<SpStoreChannelConfigVo>> getChannelConfigListBySpId(@RequestParam("storeId") Long storeId) {
        List<SpStoreChannelConfigVo> list = storeChannelConfigService.getChannelConfigListByStoreId(storeId);
        return success(list);
    }

    @GetMapping("/channel-page")
    @Operation(summary = "获得商家门店渠道配置信息列表分页")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<PageResult<SpStoreChannelConfigVo>> getChannelConfigPageBySpId(@Valid SpStoreChannelConfigPageVo pageVO) {
        PageResult<SpStoreChannelConfigVo> page = storeChannelConfigService.getChannelConfigPageByStoreId(pageVO);
        return success(page);
    }

    @GetMapping("/channel-app-page")
    @Operation(summary = "获得门店渠道配置信息列表分页")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:query')")
    public CommonResult<PageResult<SpStoreChannelAppConfigVo>> getChannelConfigAppPageBySpId(@Valid SpStoreChannelConfigPageVo pageVO) {
        PageResult<SpStoreChannelAppConfigVo> page = storeChannelConfigService.getChannelAppConfigPageBySpId(pageVO);
        return success(page);
    }


    // ################################################# 小红书本地生活小程序 ##########################################################
    // ################################################# 小红书本地生活小程序 代开发 ##########################################################
    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PostMapping("/channel-xhsminiapp-create")
    @Operation(summary = "创建门店渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:create')")
    public CommonResult<Long> createChannelXhsMiniAppChannelConfig(@Valid @RequestBody SpStoreXhsMiniAppChannelConfigSaveReqVO reqVO) {
        return success(storeXhsMiniAppChannelConfigService.createXhsMiniAppChannelConfig(reqVO));
    }
    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PutMapping("/channel-xhsminiapp-update")
    @Operation(summary = "更新门店渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:update')")
    public CommonResult<Boolean> updateChannelXhsMiniAppChannelConfig(@Valid @RequestBody SpStoreXhsMiniAppChannelConfigUpdateReqVO reqVO) {
        storeXhsMiniAppChannelConfigService.updateXhsMiniAppChannelConfig(reqVO);
        return success(true);
    }

    /**
     * 获取小红书本地生活小程序
     * @param id
     * @return
     */
    @GetMapping("/channel-xhsminiapp-get")
    @Operation(summary = "获取门店渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:create')")
    public CommonResult<SpStoreXhsMiniAppChannelConfigRespVO> getChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        return success(BeanUtil.toBean(storeXhsMiniAppChannelConfigService.getById(id), SpStoreXhsMiniAppChannelConfigRespVO.class));
    }

    /**
     * 保存小红书本地生活小程序
     * @param id
     * @return
     */
    @PutMapping("/channel-xhsminiapp-upsert")
    @Operation(summary = "提交审核门店渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:update')")
    public CommonResult<Boolean> upsertChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        storeXhsMiniAppChannelConfigService.upsertChannelConfig(id);
        return success(true);
    }


    /**
     * 保存小红书本地生活小程序
     * @param id
     * @return
     */
    @PutMapping("/channel-xhsminiapp-unbind")
    @Operation(summary = "解绑门店渠道配置信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:update')")
    public CommonResult<Boolean> unbindChannelXhsMiniAppChannelConfig(@RequestParam("id") Long id) {
        storeXhsMiniAppChannelConfigService.unbindChannelConfig(id);
        return success(true);
    }

    // ################################################# 小红书本地生活小程序 代开发 ##########################################################

    // ################################################# 小红书本地生活小程序 代开发POI ##########################################################
    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PostMapping("/channel-xhsminiapp-poi-create")
    @Operation(summary = "创建门店渠道配置POI信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:create')")
    public CommonResult<Long> createChannelXhsMiniAppChannelConfigByPOI(@Valid @RequestBody SpStoreXhsMiniAppChannelPOIConfigSaveReqVO reqVO) {
        return success(storeXhsMiniAppChannelPOIConfigService.createXhsMiniAppChannelConfig(reqVO));
    }
    /**
     * 保存小红书本地生活小程序
     * @param reqVO
     * @return
     */
    @PutMapping("/channel-xhsminiapp-poi-update")
    @Operation(summary = "更新门店渠道配置POI信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:update')")
    public CommonResult<Boolean> updateChannelXhsMiniAppChannelConfigByPOI(@Valid @RequestBody SpStoreXhsMiniAppChannelPOIConfigUpdateReqVO reqVO) {
        storeXhsMiniAppChannelPOIConfigService.updateXhsMiniAppChannelConfig(reqVO);
        return success(true);
    }

    /**
     * 获取小红书本地生活小程序
     * @param id
     * @return
     */
    @GetMapping("/channel-xhsminiapp-poi-get")
    @Operation(summary = "获取门店渠道配置POI信息")
    @PreAuthorize("@ss.hasPermission('sp:store-channel-config:create')")
    public CommonResult<SpStoreXhsMiniAppChannelPOIConfigRespVO> getChannelXhsMiniAppChannelConfigByPOI(@RequestParam("id") Long id) {
        return success(BeanUtil.toBean(storeXhsMiniAppChannelPOIConfigService.getById(id), SpStoreXhsMiniAppChannelPOIConfigRespVO.class));
    }
    // ################################################# 小红书本地生活小程序 代开发POI ##########################################################



}
