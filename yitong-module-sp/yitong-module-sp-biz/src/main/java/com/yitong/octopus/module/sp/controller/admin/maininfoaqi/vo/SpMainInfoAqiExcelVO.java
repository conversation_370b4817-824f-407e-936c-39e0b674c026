package com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_MAIN_AQI_STATUS;
import static com.yitong.octopus.module.sp.enums.DictTypeConstants.SP_MAIN_AQI_TYPE;


/**
 * 主体资质信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainInfoAqiExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体ID")
    private Long spId;

    @ExcelProperty("资质编号")
    private String aqiNumber;

    @ExcelProperty(value = "资质有效期是否长期有效", converter = DictConvert.class)
    @DictFormat("sp_main_aqi_is_long") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer aqiIsLong;

    @ExcelProperty("资质有效期开始")
    private String aqiStart;

    @ExcelProperty("资质有效期结束")
    private String aqiEnd;

    @ExcelProperty("资质电子版")
    private String aqiImg1;

    @ExcelProperty("资质电子版2")
    private String aqiImg2;

    @ExcelProperty("资质发证机构")
    private String aqiOrg;

    @ExcelProperty(value = "资质状态", converter = DictConvert.class)
    @DictFormat(SP_MAIN_AQI_STATUS)
    private Byte aqiStatus;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建人ID")
    private Long createUserId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "资质类型", converter = DictConvert.class)
    @DictFormat(SP_MAIN_AQI_TYPE)
    private Integer aqiType;

}
