package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.framework.jackson.core.databind.MoneyF2YSerializer;
import com.yitong.octopus.framework.jackson.core.databind.MoneyY2FDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
* 商家门店信息 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoBaseVO {

    @Schema(description = "主体Id", required = true, example = "11434")
    @NotNull(message = "主体Id不能为空")
    private Long spId;

    @Schema(description = "类目Id", required = true, example = "4730")
    @NotNull(message = "类目Id不能为空")
    private Long categoryId;

    @Schema(description = "商家门店名称", required = true, example = "王五")
    @NotNull(message = "商家门店名称不能为空")
    private String storeName;
    /**
     * 商家门店Logo
     */
    @Schema(description = "商家门店Logo", required = true, example = "王五")
    private String storeLogo;

    @Schema(description = "所在省", required = true)
    @NotNull(message = "所在省不能为空")
    private String spProvince;

    @Schema(description = "所在市", required = true)
    @NotNull(message = "所在市不能为空")
    private String spCity;

    @Schema(description = "所在县")
    private String spCounty;

    @Schema(description = "所在镇")
    private String spTown;

    @Schema(description = "详细地址")
    private String spAdd;

    @Schema(description = "人均消费", example = "21006")
    private Integer storeAvgPrice;

    @Schema(description = "状态", example = "2")
    private Integer spStatus;

    @Schema(description = "规模")
    private Integer spScale;

    @Schema(description = "联系人姓名", example = "王五")
    private String linkName;

    @Schema(description = "联系人手机")
    private String linkPhone;

    @Schema(description = "联系人电话")
    private String linkTel;

    @Schema(description = "联系人邮箱")
    private String linkEmail;

    @Schema(description = "品牌Id", example = "随便")
    private Long brandId;

    @Schema(description = "商家门店电话", example = "400")
    private String storeTel;

    // 20240314
    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    private Integer poiAuditStatus;

    @Schema(description = "备注", example = "随便")
    private String remark;

    /**
     * 评级
     */
    @Schema(description = "评级", example = "随便")
    @JsonSerialize(using = MoneyF2YSerializer.class)
    @JsonDeserialize(using = MoneyY2FDeserializer.class)
    private Long rating;

    @Schema(description = "门店特色")
    private String flavour;
}
