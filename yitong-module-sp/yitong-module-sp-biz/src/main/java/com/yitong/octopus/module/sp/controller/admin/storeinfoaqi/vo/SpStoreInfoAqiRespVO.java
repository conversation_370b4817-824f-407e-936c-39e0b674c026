package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商家资质信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoAqiRespVO extends SpStoreInfoAqiBaseVO {

    @Schema(description = "id", required = true, example = "32297")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
