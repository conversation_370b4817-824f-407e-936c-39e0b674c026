package com.yitong.octopus.module.sp.controller.admin.storeinfoaqi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 商家资质信息 Dto，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoAqiDto {

    @Schema(description = "id", example = "32297")
    private Long id;

    @Schema(description = "资质类型", example = "2")
    private Integer aqiType;

    @Schema(description = "资质编号")
    private String aqiNumber;

    @Schema(description = "资质有效期是否长期有效")
    private Integer aqiIsLong;

    @Schema(description = "资质有效期开始")
    private Long aqiStart;

    @Schema(description = "资质有效期结束")
    private Long aqiEnd;

    @Schema(description = "资质电子版")
    private String aqiImg1;

    @Schema(description = "资质电子版2")
    private String aqiImg2;

    @Schema(description = "资质发证机构")
    private String aqiOrg;

    @Schema(description = "资质状态", example = "2")
    private Integer aqiStatus;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
