package com.yitong.octopus.module.sp.service.mainplatformcontractbillitem;

import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.mainplatformcontractbillitem.SpMainPlatformContractBillItemDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.sp.convert.mainplatformcontractbillitem.SpMainPlatformContractBillItemConvert;
import com.yitong.octopus.module.sp.dal.mysql.mainplatformcontractbillitem.SpMainPlatformContractBillItemMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 主体平台合同结算信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SpMainPlatformContractBillItemServiceImpl implements SpMainPlatformContractBillItemService {

    @Resource
    private SpMainPlatformContractBillItemMapper mainPlatformContractBillItemMapper;

    @Override
    public Long createMainPlatformContractBillItem(SpMainPlatformContractBillItemCreateReqVO createReqVO) {
        // 插入
        SpMainPlatformContractBillItemDO mainPlatformContractBillItem = SpMainPlatformContractBillItemConvert.INSTANCE.convert(createReqVO);
        mainPlatformContractBillItemMapper.insert(mainPlatformContractBillItem);
        // 返回
        return mainPlatformContractBillItem.getId();
    }

    @Override
    public void updateMainPlatformContractBillItem(SpMainPlatformContractBillItemUpdateReqVO updateReqVO) {
        // 校验存在
        validateMainPlatformContractBillItemExists(updateReqVO.getId());
        // 更新
        SpMainPlatformContractBillItemDO updateObj = SpMainPlatformContractBillItemConvert.INSTANCE.convert(updateReqVO);
        mainPlatformContractBillItemMapper.updateById(updateObj);
    }

    @Override
    public void deleteMainPlatformContractBillItem(Long id) {
        // 校验存在
        validateMainPlatformContractBillItemExists(id);
        // 删除
        mainPlatformContractBillItemMapper.deleteById(id);
    }

    @Override
    public void deleteMainPlatformContractBillItemByContractId(Long spContractId) {
        // 删除
        mainPlatformContractBillItemMapper.delete(new LambdaQueryWrapperX<SpMainPlatformContractBillItemDO>()
                .eq(SpMainPlatformContractBillItemDO::getSpContractId,spContractId));
    }

    @Override
    public void deleteMainPlatformContractBillItemBySpId(Long spId) {
        // 删除
        mainPlatformContractBillItemMapper.delete(new LambdaQueryWrapperX<SpMainPlatformContractBillItemDO>()
                .eq(SpMainPlatformContractBillItemDO::getSpId,spId));
    }

    private void validateMainPlatformContractBillItemExists(Long id) {
        if (mainPlatformContractBillItemMapper.selectById(id) == null) {
            throw exception(MAIN_PLATFORM_CONTRACT_BILL_ITEM_NOT_EXISTS);
        }
    }

    @Override
    public SpMainPlatformContractBillItemDO getMainPlatformContractBillItem(Long id) {
        return mainPlatformContractBillItemMapper.selectById(id);
    }

    @Override
    public List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemList(Collection<Long> ids) {
        return mainPlatformContractBillItemMapper.selectBatchIds(ids);
    }

    @Override
    public List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemListByContractId(Long spContractId) {
        return mainPlatformContractBillItemMapper.selectList(new LambdaQueryWrapperX<SpMainPlatformContractBillItemDO>()
                .eq(SpMainPlatformContractBillItemDO::getSpContractId,spContractId));
    }

    @Override
    public PageResult<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemPage(SpMainPlatformContractBillItemPageReqVO pageReqVO) {
        return mainPlatformContractBillItemMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SpMainPlatformContractBillItemDO> getMainPlatformContractBillItemList(SpMainPlatformContractBillItemExportReqVO exportReqVO) {
        return mainPlatformContractBillItemMapper.selectList(exportReqVO);
    }

    @Override
    public boolean createMainPlatformContractBillItemByList(List<SpMainPlatformContractBillItemCreateReqVO> createReqVOList) {
        List<SpMainPlatformContractBillItemDO> spMainPlatformContractBillItemDOList = createReqVOList.stream().map(
                reqVO -> SpMainPlatformContractBillItemConvert.INSTANCE.convert(reqVO)
        ).collect(Collectors.toList());
        mainPlatformContractBillItemMapper.insertBatch(spMainPlatformContractBillItemDOList);
        return true;
    }

    @Override
    public boolean updateMainPlatformContractBillItemByList(List<SpMainPlatformContractBillItemUpdateReqVO> updateReqVOList) {
        List<SpMainPlatformContractBillItemDO> spMainPlatformContractBillItemDOList = updateReqVOList.stream().map(
                reqVO -> SpMainPlatformContractBillItemConvert.INSTANCE.convert(reqVO)
        ).collect(Collectors.toList());
        mainPlatformContractBillItemMapper.updateBatchById(spMainPlatformContractBillItemDOList);
        return true;
    }

}
