package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import com.yitong.octopus.module.platform.controller.admin.bank.vo.PlatformBankRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 主体结算银行卡view Resp VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainBillBankCardViewRespVO extends SpMainBillBankCardBaseVO {

    @Schema(description = "id", required = true, example = "17403")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

    @Schema(description = "状态", required = true, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "银行信息")
    private PlatformBankRespVO platformBankVo;
}
