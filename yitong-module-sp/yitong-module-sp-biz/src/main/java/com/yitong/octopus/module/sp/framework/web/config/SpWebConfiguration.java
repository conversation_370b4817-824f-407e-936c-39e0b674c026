package com.yitong.octopus.module.sp.framework.web.config;

import com.yitong.octopus.framework.swagger.config.YitongSwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * product 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class SpWebConfiguration {

    /**
     * product 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi spGroupedOpenApi() {
        return YitongSwaggerAutoConfiguration.buildGroupedOpenApi("sp");
    }

}
