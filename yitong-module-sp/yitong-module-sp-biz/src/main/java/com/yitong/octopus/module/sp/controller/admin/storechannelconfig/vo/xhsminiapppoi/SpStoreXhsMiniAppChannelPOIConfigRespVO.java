package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi;

import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 服务商渠道配置信息创【小红书本地生活小程序】POI Request VO")
@Data
@ToString(callSuper = true)
public class SpStoreXhsMiniAppChannelPOIConfigRespVO {
    /**
     * appId
     */
    @Schema(description = "id", example = "1")
    private Long id;

    @Schema(description = "渠道商户门店名称", required = true, example = "true")
    private String channelStoreName;

    @Schema(description = "渠道绑定POI", required = true, example = "true")
    private String channelStorePoi;

}
