package com.yitong.octopus.module.sp.controller.admin.goodsspu.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商品渠道上下架 Request VO")
@Data
public class SpuChannelShelveXhsMIniAppReq {

	@Schema(description = "应用ID", required = true, example = "1")
	@NotNull(message = "应用ID不能为空")
	private Long appId;

	@Schema(description = "商品Id", required = true, example = "1")
	@NotNull(message = "商品不能为空")
	private Long spuId;

	@Schema(description = "上下架", required = true, example = "1")
	@NotNull(message = "上下架不能为空")
	private Integer action;

}
