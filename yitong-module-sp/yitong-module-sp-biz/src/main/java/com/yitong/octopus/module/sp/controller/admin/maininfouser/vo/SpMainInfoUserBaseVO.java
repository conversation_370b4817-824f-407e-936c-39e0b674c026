package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.*;

/**
* 商户子账号 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpMainInfoUserBaseVO {

    @Schema(description = "名称", required = true, example = "张三")
    @NotNull(message = "名称不能为空")
    private String name;

    @Schema(description = "手机号", required = true)
    @NotNull(message = "手机号不能为空")
    private String mobile;

    @Schema(description = "密码", required = true)
    @NotNull(message = "密码不能为空")
    private String password;

    @Schema(description = "状态", required = true, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    @Schema(description = "类型", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "门店ID", example = "26789")
    private Long storeId;

}
