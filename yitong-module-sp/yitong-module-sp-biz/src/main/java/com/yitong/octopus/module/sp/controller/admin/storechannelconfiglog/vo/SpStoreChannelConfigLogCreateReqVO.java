package com.yitong.octopus.module.sp.controller.admin.storechannelconfiglog.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 商家门店渠道配置信息表日志创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigLogCreateReqVO extends SpStoreChannelConfigLogBaseVO {

}
