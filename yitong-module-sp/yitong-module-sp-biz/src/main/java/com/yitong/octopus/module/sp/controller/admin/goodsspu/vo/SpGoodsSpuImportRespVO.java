package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 商家商品导入 Response VO")
@Data
@Builder
public class SpGoodsSpuImportRespVO {

    @Schema(description = "创建成功的列表", required = true)
    private List<String> createList;

    @Schema(description = "更新成功的列表", required = true)
    private List<String> updateList;

    @Schema(description = "导入失败的列表,key 为数据主键，value 为失败原因", required = true)
    private Map<String, String> failureList;

}
