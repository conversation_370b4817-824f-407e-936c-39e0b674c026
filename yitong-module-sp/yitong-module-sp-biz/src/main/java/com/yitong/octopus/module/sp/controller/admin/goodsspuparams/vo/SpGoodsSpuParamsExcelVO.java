package com.yitong.octopus.module.sp.controller.admin.goodsspuparams.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品spu Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpGoodsSpuParamsExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店Id")
    private Long storeId;

    @ExcelProperty("参数Id")
    private Integer paramId;

    @ExcelProperty("参数名称")
    private String paramName;

    @ExcelProperty("参数值")
    private String paramValue;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
