package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.xhsminiapppoi;

import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 服务商渠道配置信息创【小红书本地生活小程序】POI Request VO")
@Data
@ToString(callSuper = true)
public class SpStoreXhsMiniAppChannelPOIConfigUpdateReqVO {
    /**
     * appId
     */
    @Schema(description = "id", example = "1")
    @NotNull(message = "编码不能为空")
    private Long id;

    @NotEmpty(message = "渠道商户门店名称不能为空")
    @Schema(description = "渠道商户门店名称", required = true, example = "true")
    private String channelStoreName;

    @NotEmpty(message = "渠道绑定POI不能为空")
    @Schema(description = "渠道绑定POI", required = true, example = "true")
    private String channelStorePoi;
    /**
     * 小红书渠道字典
     * {
     *    xhsUserId: 111,
     *    xhsUserName: '小红书',
     * }
     */
    @Schema(description = "配置信息", required = true, example = "{name:'张三',age:18}")
    @NotEmpty(message = "配置信息不能为空")
    @JsonRawValue
    private String configValue;

}
