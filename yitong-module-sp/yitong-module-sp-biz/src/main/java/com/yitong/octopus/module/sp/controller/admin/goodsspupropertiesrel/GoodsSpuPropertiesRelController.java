//package com.yitong.octopus.module.sp.controller.admin.goodsspupropertiesrel;
//
//import org.springframework.web.bind.annotation.*;
//import javax.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//
//import jakarta.validation.constraints.*;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//
//import com.yitong.octopus.framework.common.pojo.PageParam;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.pojo.CommonResult;
//import com.yitong.octopus.framework.common.util.object.BeanUtils;
//import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
//
//import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
//
//import com.yitong.octopus.framework.operatelog.core.annotations.OperateLog;
//import static com.yitong.octopus.framework.operatelog.core.enums.OperateTypeEnum.*;
//
//import com.yitong.octopus.module.sp.controller.admin.goodsspupropertiesrel.vo.*;
//import com.yitong.octopus.module.sp.dal.dataobject.goodsspupropertiesrel.GoodsSpuPropertiesRelDO;
//import com.yitong.octopus.module.sp.service.goodsspu.GoodsSpuPropertiesRelService;
//
//@Tag(name = "管理后台 - 商家商品属性关系")
//@RestController
//@RequestMapping("/sp/goods-spu-properties-rel")
//@Validated
//public class GoodsSpuPropertiesRelController {
//
//    @Resource
//    private GoodsSpuPropertiesRelService goodsSpuPropertiesRelService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建商家商品属性关系")
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:create')")
//    public CommonResult<Long> createGoodsSpuPropertiesRel(@Valid @RequestBody GoodsSpuPropertiesRelSaveReqVO createReqVO) {
//        return success(goodsSpuPropertiesRelService.createGoodsSpuPropertiesRel(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新商家商品属性关系")
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:update')")
//    public CommonResult<Boolean> updateGoodsSpuPropertiesRel(@Valid @RequestBody GoodsSpuPropertiesRelSaveReqVO updateReqVO) {
//        goodsSpuPropertiesRelService.updateGoodsSpuPropertiesRel(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除商家商品属性关系")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:delete')")
//    public CommonResult<Boolean> deleteGoodsSpuPropertiesRel(@RequestParam("id") Long id) {
//        goodsSpuPropertiesRelService.deleteGoodsSpuPropertiesRel(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得商家商品属性关系")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:query')")
//    public CommonResult<GoodsSpuPropertiesRelRespVO> getGoodsSpuPropertiesRel(@RequestParam("id") Long id) {
//        GoodsSpuPropertiesRelDO goodsSpuPropertiesRel = goodsSpuPropertiesRelService.getGoodsSpuPropertiesRel(id);
//        return success(BeanUtils.toBean(goodsSpuPropertiesRel, GoodsSpuPropertiesRelRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得商家商品属性关系分页")
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:query')")
//    public CommonResult<PageResult<GoodsSpuPropertiesRelRespVO>> getGoodsSpuPropertiesRelPage(@Valid GoodsSpuPropertiesRelPageReqVO pageReqVO) {
//        PageResult<GoodsSpuPropertiesRelDO> pageResult = goodsSpuPropertiesRelService.getGoodsSpuPropertiesRelPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, GoodsSpuPropertiesRelRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出商家商品属性关系 Excel")
//    @PreAuthorize("@ss.hasPermission('sp:goods-spu-properties-rel:export')")
//    @OperateLog(type = EXPORT)
//    public void exportGoodsSpuPropertiesRelExcel(@Valid GoodsSpuPropertiesRelPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<GoodsSpuPropertiesRelDO> list = goodsSpuPropertiesRelService.getGoodsSpuPropertiesRelPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "商家商品属性关系.xls", "数据", GoodsSpuPropertiesRelRespVO.class,
//                        BeanUtils.toBean(list, GoodsSpuPropertiesRelRespVO.class));
//    }
//
//}