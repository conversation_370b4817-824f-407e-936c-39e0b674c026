package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 服务商门店-渠道配置信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreChannelConfigExcelVO {

    @ExcelProperty("ID")
    private Long id;

    @ExcelProperty("门店ID")
    private Long storeId;

    @ExcelProperty("平台渠道ID")
    private Long channelId;

    @ExcelProperty("配置信息值")
    private String configValue;

    @ExcelProperty("审核状态 ")
    private Integer auditStatus;

    @ExcelProperty("审批人")
    private Long auditUserId;

    @ExcelProperty("审批备注")
    private String auditReason;

    @ExcelProperty("审批时间")
    private LocalDateTime auditTime;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
