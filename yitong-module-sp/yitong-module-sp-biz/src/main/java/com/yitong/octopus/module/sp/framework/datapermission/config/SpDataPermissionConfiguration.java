//package com.yitong.octopus.module.sp.framework.datapermission.config;
//
//import com.yitong.octopus.framework.datapermission.core.rule.sp.SpDataPermissionRule;
//import com.yitong.octopus.framework.datapermission.core.rule.sp.SpDataPermissionRuleCustomizer;
//import com.yitong.octopus.module.sp.permission.SpPermissionApi;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.List;
//
///**
// * sp 模块的数据权限 Configuration
// *
// * <AUTHOR>
// */
//@Slf4j
//@Configuration(proxyBeanMethods = false)
//public class SpDataPermissionConfiguration {
//
//    private static final String TAB_ID= "id";
//
////    @Bean
////    public SpDataPermissionRuleCustomizer spDataPermissionRuleCustomizer() {
////        log.info("SpDataPermissionRuleCustomizer  init .......");
////        return rule -> {
////            TableInfoHelper.getTableInfos().forEach(tableInfo -> {
////                List<TableDataPermission> dataPermissionList = CollUtil.newArrayList(tableInfo.getEntityType().getAnnotationsByType(TableDataPermission.class));
////                TableDataPermissionGroup[] groups = tableInfo.getEntityType().getAnnotationsByType(TableDataPermissionGroup.class);
////                if (groups.length>0){
////                    for (TableDataPermissionGroup group : groups) {
////                        dataPermissionList.addAll(CollUtil.newArrayList(group.value()));
////                    }
////                }
////                if (CollUtil.isEmpty(dataPermissionList)){
////                    return;
////                }
////
////                dataPermissionList.forEach(table -> {
////                    if (SYS.equals(table.strategy())){
////                        return;
////                    }
////                    if (SP_ID.equals(table.columnRule())){
////                        rule.addSpColumn(tableInfo.getTableName(), table.columnName());
////                    }else if (SP_STORE_ID.equals(table.columnRule())){
////                        rule.addStoreColumn(tableInfo.getTableName(), table.columnName());
////                    }
////                });
////            });
////        };
////    }
//
//    @Bean
//    public SpDataPermissionRule spDataPermissionRule(SpPermissionApi spPermissionApi,
//                                                       List<SpDataPermissionRuleCustomizer> customizers) {
//        // 创建 DeptDataPermissionRule 对象
//        SpDataPermissionRule rule = new SpDataPermissionRule(spPermissionApi);
//        // 补全表配置
//        customizers.forEach(customizer -> customizer.customize(rule));
//        return rule;
//    }
//
//}
