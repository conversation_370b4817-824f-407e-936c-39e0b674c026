package com.yitong.octopus.module.sp.controller.admin.industry.vo;

import com.yitong.octopus.framework.common.pojo.PageParam;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 行业分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class IndustryPageReqVO extends PageParam {

	@Schema(description = "上级行业ID", example = "25294")
	private Long parentId;

}
