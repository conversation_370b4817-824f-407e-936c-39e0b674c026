package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 服务商门店-渠道配置信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigRespVO extends SpStoreChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "14253")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
