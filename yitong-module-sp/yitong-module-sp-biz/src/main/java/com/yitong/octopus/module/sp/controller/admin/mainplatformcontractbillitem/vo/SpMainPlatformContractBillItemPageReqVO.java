package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体平台合同结算信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainPlatformContractBillItemPageReqVO extends PageParam {

    @Schema(description = "服务商ID", example = "3810")
    private Long spId;

    @Schema(description = "服务商名称", example = "芋艿")
    private String spName;

    @Schema(description = "服务商合同ID", example = "1489")
    private Long spContractId;

    @Schema(description = "结算项ID", example = "22961")
    private Long billItemId;

    @Schema(description = "结算项值")
    private String billItemValue;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建人ID", example = "19499")
    private Long createUserId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
