package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto;

import com.yitong.octopus.module.sp.controller.admin.storechannelconfig.vo.SpStoreChannelConfigBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 服务商门店渠道配置信息 view dto")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigViewDto extends SpStoreChannelConfigBaseVO {

    @Schema(description = "ID", required = true, example = "14253")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "审核状态 ", required = true, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审批备注", example = "不香")
    private String auditReason;

    @Schema(description = "渠道商户门店ID", example = "axddd1233")
    private String channelStoreId;

    /**
     * 渠道返回结果-渠道审核成功之后
     */
    @Schema(description = "渠道店铺POI名称")
    private String channelStoreName;

    @Schema(description = "渠道商户门店POI", example = "axddd1233")
    private String channelStorePoi;

}
