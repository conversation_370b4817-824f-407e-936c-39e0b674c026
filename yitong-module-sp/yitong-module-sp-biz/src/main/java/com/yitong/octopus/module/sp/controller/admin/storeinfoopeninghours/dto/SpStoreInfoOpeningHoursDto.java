package com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* 商家门店营业时间 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class SpStoreInfoOpeningHoursDto {

    @Schema(description = "星期")
    private String days;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

}
