package com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 主体资质信息 Excel 导出 Request VO，参数和 SpMainInfoAqiPageReqVO 是一致的")
@Data
public class SpMainInfoAqiExportReqVO {

    @Schema(description = "主体ID", example = "853")
    private Long spId;

    @Schema(description = "资质编号")
    private String aqiNumber;

    @Schema(description = "资质有效期是否长期有效")
    private Integer aqiIsLong;

    @Schema(description = "资质发证机构")
    private String aqiOrg;

    @Schema(description = "资质状态", example = "1")
    private Byte aqiStatus;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建人ID", example = "2620")
    private Long createUserId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "资质类型", example = "2")
    private Integer aqiType;

}
