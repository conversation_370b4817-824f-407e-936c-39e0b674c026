package com.yitong.octopus.module.sp.controller.admin.storeinfogallery.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 商家门店图片 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoGalleryExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店Id")
    private Long storeId;

    @ExcelProperty(value = "图片类型", converter = DictConvert.class)
    @DictFormat("sp_store_info_gallery_type") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Byte imageType;

    @ExcelProperty("图片地址")
    private String imageUrl;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
