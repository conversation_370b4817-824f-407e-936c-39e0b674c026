package com.yitong.octopus.module.sp.controller.admin.maininfouser.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商户子账号分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoUserPageReqVO extends PageParam {

    @Schema(description = "商家Id", example = "26789")
    private Long spId;

    @Schema(description = "名称", example = "张三")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "类型", example = "1")
    private Integer type;

    @Schema(description = "门店ID", example = "26789")
    private Long storeId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] crtTime;

}
