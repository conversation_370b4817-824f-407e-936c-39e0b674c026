package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商家门店信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreInfoPageReqVO extends PageParam {

    @Schema(description = "门店id", example = "11434")
    private Long id;

    @Schema(description = "主体Id", example = "11434")
    private Long spId;

    @Schema(description = "类目Id", example = "4730")
    private Long categoryId;

    @Schema(description = "商家门店名称", example = "王五")
    private String storeName;

    @Schema(description = "所在省id", example = "8918")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "12694")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "10837")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "27127")
    private Integer spTownId;

    @Schema(description = "状态", example = "[2]")
    private Integer[] spStatus;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "联系人姓名", example = "王五")
    private String linkName;

    @Schema(description = "联系人手机")
    private String linkPhone;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败
     */
    @Schema(description = "认领状态 0 待提交 1 已认领 2 审核中 6 门店审核中 7 待提交资质 13 门店审核失败")
    private Integer claimStatus;

    /**
     * 主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "主体资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer mainAuditStatus;

    /**
     * 行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中
     */
    @Schema(description = "行业资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 8 主体变更中")
    private Integer industryAuditStatus;

    /**
     * 门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功
     */
    @Schema(description = "门店资质审核 0 待提交 1 待审核 2 审核中 3 审核通过 4 审核未通过 5 部分字段审核成功")
    private Integer poiAuditStatus;

}
