package com.yitong.octopus.module.sp.controller.admin.storeinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryListReqVO;
import com.yitong.octopus.module.platform.service.category.PlatformCategoryService;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfogallery.SpStoreInfoGalleryDO;
import com.yitong.octopus.module.sp.dal.dto.SpStoreInfoDto;
import com.yitong.octopus.module.sp.enums.SpStoreImageTypeEnum;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;
import com.yitong.octopus.module.sp.service.storeinfogallery.SpStoreInfoGalleryService;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;

import static com.yitong.octopus.framework.common.constant.TreeConstants.TREE_ROOT_NAME;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.storeinfo.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfo.SpStoreInfoDO;
import com.yitong.octopus.module.sp.convert.storeinfo.SpStoreInfoConvert;
import com.yitong.octopus.module.sp.service.storeinfo.SpStoreInfoService;

/**
 * 管理后台 - 商家门店信息"
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 商家门店信息")
@RestController
@RequestMapping("/sp/store-info")
@Validated
public class SpStoreInfoController {

    @Lazy
    @Resource
    private SpStoreInfoService storeInfoService;

    @Lazy
    @Resource
    private SpStoreInfoGalleryService spStoreInfoGalleryService;

    @Lazy
    @Resource
    private PlatformCategoryService platformCategoryService;

    @Lazy
    @Resource
    private SpMainInfoService spMainInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建商家门店信息")
    @PreAuthorize("@ss.hasPermission('sp:store-info:create')")
    public CommonResult<Long> createStoreInfo(@Valid @RequestBody SpStoreInfoCreateReqVO createReqVO) {
        return success(storeInfoService.createStoreInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商家门店信息")
    @PreAuthorize("@ss.hasPermission('sp:store-info:update')")
    public CommonResult<Boolean> updateStoreInfo(@Valid @RequestBody SpStoreInfoUpdateReqVO updateReqVO) {
        storeInfoService.updateStoreInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商家门店信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:store-info:delete')")
    public CommonResult<Boolean> deleteStoreInfo(@RequestParam("id") Long id) {
        storeInfoService.deleteStoreInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商家门店信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:store-info:query')")
    public CommonResult<SpStoreInfoViewRespVO> getStoreInfo(@RequestParam("id") Long id) {
        SpStoreInfoViewRespVO storeInfo = storeInfoService.getStoreInfoById(id);
        return success(storeInfo);
    }

    @GetMapping("/list-sp")
    @Operation(summary = "获得商家门店信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<SpStoreInfoSampleRespVO>> getStoreInfoListBySpId(@RequestParam("spId") Long spId,@RequestParam(value = "spStatus",required = false) Integer storeStatus) {
        List<SpStoreInfoDO> list = storeInfoService.getStoreInfoListBySample(new SpStoreInfoPageSampleReqVO().setSpId(spId).setStatus(storeStatus));
        return success(SpStoreInfoConvert.INSTANCE.convertListSample2(list));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商家门店信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:store-info:query')")
    public CommonResult<List<SpStoreInfoRespVO>> getStoreInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<SpStoreInfoDO> list = storeInfoService.getStoreInfoList(ids);
        return success(SpStoreInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商家门店信息分页")
    @PreAuthenticated
    public CommonResult<PageResult<SpStoreInfoViewRespVO>> getStoreInfoPage(@Valid SpStoreInfoPageReqVO pageVO) {
        PageResult<SpStoreInfoViewRespVO> pageResult = storeInfoService.getStoreInfoPage(pageVO);
        Tree<String> tree = platformCategoryService.getEnableCategoryTree(new PlatformCategoryListReqVO());
        pageResult.getList().forEach(vo ->{
            List<CharSequence> cateGoryList =tree.getParentsName(String.valueOf(vo.getCategoryId()),true);
            cateGoryList = cateGoryList.stream().filter(i -> !i.equals(TREE_ROOT_NAME)).collect(Collectors.toList());
            Collections.reverse(cateGoryList);
            vo.setCategoryName(StrUtil.join("/", CollectionUtil.removeEmpty(cateGoryList)));
            vo.setSpMainInfoRespVO(BeanUtil.toBean(spMainInfoService.getMainInfo(vo.getSpId()),SpMainInfoRespVO.class));
        });
        return success(pageResult);
    }

    @GetMapping("/page-sample")
    @Operation(summary = "获得商家门店信息分页（简单）")
    @PreAuthenticated
    public CommonResult<PageResult<SpStoreInfoRespSampleVO>> getStoreInfoPageSample(@Valid SpStoreInfoPageReqVO pageVO) {
        PageResult<SpStoreInfoViewRespVO> pageResult = storeInfoService.getStoreInfoPage(pageVO);
        PageResult<SpStoreInfoRespSampleVO> result = new PageResult<>(BeanUtil.copyToList(pageResult.getList(),SpStoreInfoRespSampleVO.class),pageResult.getTotal());
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商家门店信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:store-info:export')")
    public void exportStoreInfoExcel(@Valid SpStoreInfoPageReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpStoreInfoDto> list = storeInfoService.getStoreInfoMapList(exportReqVO);
        // 导出 Excel
        List<SpStoreInfoExcelVO> datas = SpStoreInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商家门店信息.xls", "数据", SpStoreInfoExcelVO.class, datas);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核门店信息")
    @PreAuthorize("@ss.hasPermission('sp:store-info:audit')")
    public CommonResult<Boolean> auditStoreInfo(@Valid @RequestBody SpStoreInfoAuditReqVO vo) {
        storeInfoService.auditStoreInfo(vo);
        return success(true);
    }

    @GetMapping("/list-sample")
    @Operation(summary = "获得商家门店信息列表-简化信息")
    @PreAuthenticated
    public CommonResult<List<SpStoreInfoRespSampleVO>> getStoreInfoListByStoreName(@Valid SpStoreInfoPageSampleReqVO vo) {
        List<SpStoreInfoDO> list =  storeInfoService.getStoreInfoListBySample(vo);
        List<SpStoreInfoGalleryDO>  spStoreInfoGalleryList = spStoreInfoGalleryService.getSpStoreInfoGalleryByImageTypeAndStoreIds(SpStoreImageTypeEnum.MAIN,list.stream().map(SpStoreInfoDO::getId).collect(Collectors.toList()));
        Map<Long,List<SpStoreInfoGalleryDO>> galleryMap = spStoreInfoGalleryList.stream().collect(Collectors.groupingBy(SpStoreInfoGalleryDO::getStoreId));
        List<SpStoreInfoRespSampleVO> result = list.stream().map(s ->{
            SpStoreInfoRespSampleVO sampleVO = BeanUtil.toBean(s,SpStoreInfoRespSampleVO.class);
            if (galleryMap.containsKey(s.getId())){
                List<SpStoreInfoGalleryDO> gallery = galleryMap.get(s.getId());
                sampleVO.setMainImage(CollectionUtil.getFirst(gallery).getImageUrl());
            }
            return sampleVO;
        }).collect(Collectors.toList());
        return success(result);
    }

    @GetMapping("/list-sample-spu")
    @Operation(summary = "根据商品获得商家门店信息列表-简化信息")
    @PreAuthenticated
    public CommonResult<List<SpStoreInfoRespSampleVO>> getStoreInfoListBySpuId(Long spuId) {
        List<SpStoreInfoDO> list =  storeInfoService.getStoreInfoListBySpuId(spuId);
        return success(SpStoreInfoConvert.INSTANCE.convertListSample(list));
    }

    @GetMapping("/init-channel")
    @Operation(summary = "获得商家门店信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthenticated
    public CommonResult<SpStoreInfoViewRespVO> initStoreInfoByChannel(@RequestParam("id") Long id) {
        SpStoreInfoViewRespVO storeInfo = storeInfoService.getStoreInfoById(id);
        return success(storeInfo);
    }

    @PutMapping("/disable")
    @Operation(summary = "禁用门店")
    @PreAuthorize("@ss.hasPermission('sp:store-info:disable')")
    public CommonResult<Boolean> disableStoreInfo(@RequestParam("id") Long id) {
        storeInfoService.disableStoreInfo(id);
        return success(true);
    }

    @PutMapping("/enable")
    @Operation(summary = "启用门店")
    @PreAuthorize("@ss.hasPermission('sp:store-info:disable')")
    public CommonResult<Boolean> enableStoreInfo(@RequestParam("id") Long id) {
        storeInfoService.enableStoreInfo(id);
        return success(true);
    }

    @GetMapping("/list-simple")
    @Operation(summary = "获得商家门店信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthenticated
    public CommonResult<List<SpStoreInfoRespSampleVO>> getStoreInfoListByIds(@RequestParam("ids") Collection<Long> ids) {
        List<SpStoreInfoDO> list = storeInfoService.getStoreInfoList(ids);
        return success(BeanUtil.copyToList(list,SpStoreInfoRespSampleVO.class));
    }

    @PutMapping("/audit-qual")
    @Operation(summary = "审核主体资质信息")
    @PreAuthorize("@ss.hasPermission('sp:store-info:audit')")
    public CommonResult<Boolean> auditMainInfo(@Valid @RequestBody SpStoreInfoAuditQualReqVO vo) {
        storeInfoService.auditStoreInfoQual(vo);
        return success(true);
    }

    /**
     *服务商/商家 打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/makeLabel")
    @Operation(summary = "服务商/商家 打标签")
    @PreAuthorize("@ss.hasPermission('sp:store-info:tags')")
    public CommonResult<Boolean>  makeLabel(@Valid @RequestBody SpStoreInfoTagRelSaveReqVO tagRelSaveReqVO) {
        storeInfoService.makeLabel(tagRelSaveReqVO);
        return success(true);
    }

    /**
     *服务商/商家 打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/batchMakeLabel")
    @Operation(summary = "服务商/商家 批量打标签")
    @PreAuthorize("@ss.hasPermission('sp:store-info:tags')")
    public CommonResult<Boolean>  batchMakeLabel(@Valid @RequestBody SpStoreInfoBatchTagRelSaveReqVO tagRelSaveReqVO) {
        storeInfoService.batchMakeLabel(tagRelSaveReqVO);
        return success(true);
    }

    @GetMapping("/tag-list")
    @Operation(summary = "获得服务商/商家标签列表")
    @Parameter(name = "reqVO", description = "编号列表", required = true)
    @PreAuthorize("@ss.hasPermission('sp:store-info:tags')")
    public CommonResult<List<PlatformTagGroupVo>> getTagList(@Valid SpStoreInfoTagReqVO reqVO) {
        List<PlatformTagGroupVo>list = storeInfoService.getTagList(reqVO);
        return success(list);
    }

}
