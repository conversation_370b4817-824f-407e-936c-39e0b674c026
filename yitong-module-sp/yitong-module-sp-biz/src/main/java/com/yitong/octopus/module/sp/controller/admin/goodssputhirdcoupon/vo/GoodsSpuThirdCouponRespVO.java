package com.yitong.octopus.module.sp.controller.admin.goodssputhirdcoupon.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpGoodsSpuNameJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 商品Spu三方券码 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GoodsSpuThirdCouponRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25547")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "商品ID", example = "20446")
    @JsonSerialize(using = SpGoodsSpuNameJsonSerializer.class)
    @ExcelProperty("商品ID")
    private Long spuId;

    @Schema(description = "券码")
    @ExcelProperty("券码")
    private String couponCode;

    @Schema(description = "状态：0无效 1有效", example = "1")
    @ExcelProperty(value = "状态：0无效 1有效", converter = DictConvert.class)
    @DictFormat("sp_goods_spu_third_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "核销开始时间")
    @ExcelProperty("核销开始时间")
    private LocalDateTime validDateFrom;

    @Schema(description = "核销开始时间")
    @ExcelProperty("核销开始时间")
    private LocalDateTime validDateTo;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}