package com.yitong.octopus.module.sp.controller.admin.storeinfomap.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商家门店地图信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpStoreInfoMapExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店ID")
    private Long storeId;

    @ExcelProperty("地图渠道ID")
    private Long mapChannelId;

    @ExcelProperty("门店地图ID")
    private String mapId;

    @ExcelProperty("门店POI")
    private String mapPoi;

    @ExcelProperty("门店地图POI链接")
    private String mapPoiUrl;

    @ExcelProperty("纬度")
    private String latitude;

    @ExcelProperty("经度")
    private String longitude;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
