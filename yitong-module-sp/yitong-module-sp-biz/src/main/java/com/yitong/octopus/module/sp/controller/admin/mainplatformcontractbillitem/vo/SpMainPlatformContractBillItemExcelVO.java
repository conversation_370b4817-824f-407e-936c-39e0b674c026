package com.yitong.octopus.module.sp.controller.admin.mainplatformcontractbillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 主体平台合同结算信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpMainPlatformContractBillItemExcelVO {

    @ExcelProperty("id")
    private Long id;

    @ExcelProperty("服务商ID")
    private Long spId;

    @ExcelProperty("服务商名称")
    private String spName;

    @ExcelProperty("服务商合同ID")
    private Long spContractId;

    @ExcelProperty("结算项ID")
    private Long billItemId;

    @ExcelProperty("结算项值")
    private String billItemValue;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建人ID")
    private Long createUserId;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
