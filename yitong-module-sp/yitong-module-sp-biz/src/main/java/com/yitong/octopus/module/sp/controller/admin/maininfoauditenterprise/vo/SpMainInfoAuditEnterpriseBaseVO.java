package com.yitong.octopus.module.sp.controller.admin.maininfoauditenterprise.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import javax.validation.constraints.*;

/**
* 主体认证信息（企业） Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
 * <AUTHOR>
 */
@Data
public class SpMainInfoAuditEnterpriseBaseVO {

    @Schema(description = "主体ID", example = "4375")
    private Long spId;

    @Schema(description = "法人姓名", required = true, example = "芋艿")
    private String legalName;

    @Schema(description = "营业执照号", required = true)
    private String licenseNum;

    @Schema(description = "营业执照所在省")
    private String licenseProvince;

    @Schema(description = "营业执照所在市")
    private String licenseCity;

    @Schema(description = "营业执照所在县")
    private String licenseCounty;

    @Schema(description = "营业执照所在镇")
    private String licenseTown;

    @Schema(description = "营业执照是否长期有效")
    private Integer licenseIsLong;

    @Schema(description = "营业执照电子版")
    private String licenseImg;

    @Schema(description = "银行开户名", example = "王五")
    private String bankAccountName;

    @Schema(description = "开户银行支行名称", example = "王五")
    private String bankName;

    @Schema(description = "开户银行所在省")
    private String bankProvince;

    @Schema(description = "开户银行所在市")
    private String bankCity;

    @Schema(description = "开户银行所在县")
    private String bankCounty;

    @Schema(description = "开户银行所在镇")
    private String bankTown;

}
