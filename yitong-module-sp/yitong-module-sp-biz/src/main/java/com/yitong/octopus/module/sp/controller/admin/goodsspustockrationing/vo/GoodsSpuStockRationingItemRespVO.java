package com.yitong.octopus.module.sp.controller.admin.goodsspustockrationing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 商品库存供给明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GoodsSpuStockRationingItemRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17931")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "商品供给ID", example = "22759")
    @ExcelProperty("商品供给ID")
    private Long rationingId;

    @Schema(description = "商品ID", example = "6550")
    @ExcelProperty("商品ID")
    private Long spuId;

    @Schema(description = "所属天")
    @ExcelProperty("所属天")
    private LocalDateTime day;

    @Schema(description = "状态", example = "2")
    @ExcelProperty("状态")
    private Boolean status;

    @Schema(description = "预计总库存数")
    @ExcelProperty("预计总库存数")
    private Integer totalStock;

    @Schema(description = "所属天售真是库存数")
    @ExcelProperty("所属天售真是库存数")
    private Integer realTotalStock;

    @Schema(description = "当天售卖数")
    @ExcelProperty("当天售卖数")
    private Integer saleTotal;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}