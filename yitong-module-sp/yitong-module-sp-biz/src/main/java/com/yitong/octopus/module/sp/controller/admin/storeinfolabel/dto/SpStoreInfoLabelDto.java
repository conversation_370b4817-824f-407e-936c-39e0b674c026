package com.yitong.octopus.module.sp.controller.admin.storeinfolabel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 商家门店标签关联 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoLabelDto {

    @Schema(description = "id", example = "25053")
    private Long id;


    @Schema(description = "标签Id", required = true, example = "13745")
    @NotNull(message = "标签Id不能为空")
    private Long labelId;

}
