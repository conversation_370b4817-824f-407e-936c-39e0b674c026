package com.yitong.octopus.module.sp.controller.admin.storechannelconfig.dto;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * 商家门店渠道配置信息
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商家门店渠道配置信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpStoreChannelConfigPageVo extends PageParam {

    @Schema(description = "商家门店Id", required = true, example = "1")
    @NotNull(message = "商家门店Id不能为空")
    private Long storeId;

    @Schema(description = "应用名称", example = "1")
    private String appName;

    @Schema(description = "应用Id", example = "1")
    private Long appId;
}
