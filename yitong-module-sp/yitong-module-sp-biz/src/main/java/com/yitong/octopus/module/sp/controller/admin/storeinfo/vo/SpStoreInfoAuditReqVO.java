package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店审核 Request VO")
@Data
@ToString(callSuper = true)
public class SpStoreInfoAuditReqVO {

    @Schema(description = "门店Id", example = "3656")
    @NotNull(message = "门店Id不能为空")
    private List<Long> storeIds;

    @Schema(description = "审核状态", example = "2")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审批备注", example = "不喜欢")
    private String auditReason;

}
