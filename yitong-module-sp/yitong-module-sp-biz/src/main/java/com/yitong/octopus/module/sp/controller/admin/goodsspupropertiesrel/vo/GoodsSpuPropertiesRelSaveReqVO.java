//package com.yitong.octopus.module.sp.controller.admin.goodsspupropertiesrel.vo;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.*;
//
//@Schema(description = "管理后台 - 商家商品属性关系新增/修改 Request VO")
//@Data
//public class GoodsSpuPropertiesRelSaveReqVO {
//
//    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10219")
//    private Long id;
//
//    @Schema(description = "商家ID", example = "22433")
//    private Long spId;
//
//    @Schema(description = "商品ID", example = "1071")
//    private Long spuId;
//
//    @Schema(description = "属性ID", example = "3532")
//    private Long propertyId;
//
//    @Schema(description = "属性值ID", example = "31528")
//    private Long valueId;
//
//    @Schema(description = "属性值名称", example = "王五")
//    private String valueName;
//
//}