package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 商家素材标签关系新增/修改 Request VO")
@Data
public class SpMainMaterialsBatchTagRelSaveReqVO {

    @Schema(description = "商家素材标签列表", example = "14993")
    @NotNull(message = "商家素材标签不能为空")
    private List<SpMainMaterialsTagRelSaveReqVO> tags;

}