package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.goodsspubillitem.SpGoodsSpuBillItemDO;
import com.yitong.octopus.module.sp.convert.goodsspubillitem.SpGoodsSpuBillItemConvert;
import com.yitong.octopus.module.sp.service.goodsspubilllitem.SpGoodsSpuBillItemService;

@Tag(name = "管理后台 - 商品spu结算信息")
@RestController
@RequestMapping("/sp/goods-spu-billl-item")
@Validated
public class SpGoodsSpuBillItemController {

    @Resource
    private SpGoodsSpuBillItemService goodsSpuBilllItemService;

    @PostMapping("/create")
    @Operation(summary = "创建商品spu结算信息")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:create')")
    public CommonResult<Long> createGoodsSpuBilllItem(@Valid @RequestBody SpGoodsSpuBillItemCreateReqVO createReqVO) {
        return success(goodsSpuBilllItemService.createGoodsSpuBillItem(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新商品spu结算信息")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:update')")
    public CommonResult<Boolean> updateGoodsSpuBilllItem(@Valid @RequestBody SpGoodsSpuBillItemUpdateReqVO updateReqVO) {
        goodsSpuBilllItemService.updateGoodsSpuBillItem(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除商品spu结算信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:delete')")
    public CommonResult<Boolean> deleteGoodsSpuBilllItem(@RequestParam("id") Long id) {
        goodsSpuBilllItemService.deleteGoodsSpuBillItem(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品spu结算信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:query')")
    public CommonResult<SpGoodsSpuBilItemRespVO> getGoodsSpuBilllItem(@RequestParam("id") Long id) {
        SpGoodsSpuBillItemDO goodsSpuBilllItem = goodsSpuBilllItemService.getGoodsSpuBillItem(id);
        return success(SpGoodsSpuBillItemConvert.INSTANCE.convert(goodsSpuBilllItem));
    }

    @GetMapping("/list")
    @Operation(summary = "获得商品spu结算信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:query')")
    public CommonResult<List<SpGoodsSpuBilItemRespVO>> getGoodsSpuBilllItemList(@RequestParam("ids") Collection<Long> ids) {
        List<SpGoodsSpuBillItemDO> list = goodsSpuBilllItemService.getGoodsSpuBillItemList(ids);
        return success(SpGoodsSpuBillItemConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品spu结算信息分页")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:query')")
    public CommonResult<PageResult<SpGoodsSpuBilItemRespVO>> getGoodsSpuBilllItemPage(@Valid SpGoodsSpuBillItemPageReqVO pageVO) {
        PageResult<SpGoodsSpuBillItemDO> pageResult = goodsSpuBilllItemService.getGoodsSpuBillItemPage(pageVO);
        return success(SpGoodsSpuBillItemConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出商品spu结算信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:goods-spu-billl-item:export')")
    public void exportGoodsSpuBilllItemExcel(@Valid SpGoodsSpuBilItemExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpGoodsSpuBillItemDO> list = goodsSpuBilllItemService.getGoodsSpuBillItemList(exportReqVO);
        // 导出 Excel
        List<SpGoodsSpuBillItemExcelVO> datas = SpGoodsSpuBillItemConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "商品spu结算信息.xls", "数据", SpGoodsSpuBillItemExcelVO.class, datas);
    }

}
