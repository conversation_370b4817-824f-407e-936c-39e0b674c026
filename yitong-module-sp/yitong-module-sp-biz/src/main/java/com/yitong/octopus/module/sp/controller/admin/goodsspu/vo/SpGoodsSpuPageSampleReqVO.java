package com.yitong.octopus.module.sp.controller.admin.goodsspu.vo;

import com.yitong.octopus.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品spu分页精简 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuPageSampleReqVO extends PageParam {

    @Schema(description = "查询条件", example = "XX下午茶")
    private String searchParams;

    @Schema(description = "商品ID", example = "13133")
    private Long spuId;

    @Schema(description = "商品名称", example = "XX下午茶")
    private String spuName;

    @Schema(description = "主体Id", example = "24153")
    private Long spId;

    @Schema(description = "商家门店Id", example = "2303")
    private Long storeId;

    @Schema(description = "类目", example = "25255")
    private Long categoryId;

    @Schema(description = "商品品牌编号", example = "23655")
    private Integer brandId;

}
