package com.yitong.octopus.module.sp.controller.admin.storeinfo.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.yitong.octopus.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商家门店信息 Excel 导出 Request VO，参数和 SpStoreInfoPageReqVO 是一致的")
@Data
public class SpStoreInfoExportReqVO {

    @Schema(description = "服务商/商家Id", example = "王五")
    private Long id;

    @Schema(description = "主体Id", example = "11434")
    private Long spId;

    @Schema(description = "类目Id", example = "4730")
    private Long categoryId;

    @Schema(description = "商家门店名称", example = "王五")
    private String storeName;

    @Schema(description = "所在省id", example = "8918")
    private Integer spProvinceId;

    @Schema(description = "所在市id", example = "12694")
    private Integer spCityId;

    @Schema(description = "所在县id", example = "10837")
    private Integer spCountyId;

    @Schema(description = "所在镇id", example = "27127")
    private Integer spTownId;

    @Schema(description = "状态", example = "[2]")
    private Integer[] spStatus;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "联系人姓名", example = "王五")
    private String linkName;

    @Schema(description = "联系人手机")
    private String linkPhone;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
