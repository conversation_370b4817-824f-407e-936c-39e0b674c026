package com.yitong.octopus.module.sp.controller.admin.maininfo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 商家属性关系查询 Request VO")
@Data
public class SpMainInfoPropertyReqVO {

    @Schema(description = "商家ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20541")
    @NotNull(message = "商家ID不能为空")
    private Long id;

    @Schema(description = "属性Id", example = "20541")
    private Long propertyId;

}