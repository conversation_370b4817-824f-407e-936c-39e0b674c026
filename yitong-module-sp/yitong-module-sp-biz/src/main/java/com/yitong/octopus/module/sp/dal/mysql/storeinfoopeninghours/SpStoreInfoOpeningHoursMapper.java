package com.yitong.octopus.module.sp.dal.mysql.storeinfoopeninghours;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfoopeninghours.SpStoreInfoOpeningHoursDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.sp.controller.admin.storeinfoopeninghours.vo.*;

/**
 * 商家门店营业时间 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SpStoreInfoOpeningHoursMapper extends BaseMapperX<SpStoreInfoOpeningHoursDO> {

    default PageResult<SpStoreInfoOpeningHoursDO> selectPage(SpStoreInfoOpeningHoursPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SpStoreInfoOpeningHoursDO>()
                .eqIfPresent(SpStoreInfoOpeningHoursDO::getSpId, reqVO.getSpId())
                .eqIfPresent(SpStoreInfoOpeningHoursDO::getStoreId, reqVO.getStoreId())
                .betweenIfPresent(SpStoreInfoOpeningHoursDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SpStoreInfoOpeningHoursDO::getId));
    }

    default List<SpStoreInfoOpeningHoursDO> selectList(SpStoreInfoOpeningHoursExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SpStoreInfoOpeningHoursDO>()
                .eqIfPresent(SpStoreInfoOpeningHoursDO::getSpId, reqVO.getSpId())
                .eqIfPresent(SpStoreInfoOpeningHoursDO::getStoreId, reqVO.getStoreId())
                .betweenIfPresent(SpStoreInfoOpeningHoursDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SpStoreInfoOpeningHoursDO::getId));
    }

}
