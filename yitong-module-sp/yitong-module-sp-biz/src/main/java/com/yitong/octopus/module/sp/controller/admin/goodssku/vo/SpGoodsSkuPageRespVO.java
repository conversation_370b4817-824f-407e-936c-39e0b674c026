package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yitong.octopus.module.sp.api.utils.jackson.SpMainInfoJsonSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 商品SKU信息
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 商品sku Response VO")
@Data
@ToString(callSuper = true)
public class SpGoodsSkuPageRespVO {

    @Schema(description = "主键", required = true, example = "25294")
    private Long id;

    @Schema(description = "主体Id", example = "19437")
    @JsonSerialize(using = SpMainInfoJsonSerializer.class)
    private Long spId;

    @Schema(description = "商品spuId", required = true, example = "2496")
    private Long spuId;

    @Schema(description = "Spu名称", required = true, example = "14979")
    private String spuName;

    @Schema(description = "市场价", required = true)
    private Long marketPrice;

    @Schema(description = "售价", required = true)
    private Long salePrice;

    private Integer billStock;

    @Schema(description = "可用库存")
    private Integer saleStock;

    @Schema(description = "预占库存")
    private Integer occupyStock;

    /**
     * 市场价
     */
    private Long marketAmount;
    /**
     * 售价
     */
    private Long saleAmount;

    @Schema(description = "销售截止时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime soldDateTo;

    @Schema(description = "创建时间", required = true)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;
}
