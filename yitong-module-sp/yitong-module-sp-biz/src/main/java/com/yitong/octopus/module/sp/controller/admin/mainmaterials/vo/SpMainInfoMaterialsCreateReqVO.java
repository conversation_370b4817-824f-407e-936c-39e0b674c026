package com.yitong.octopus.module.sp.controller.admin.mainmaterials.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体素材创建 Request VO")
@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SpMainInfoMaterialsCreateReqVO {

    @Schema(description = "商家名称ID", example = "10336")
    private Long spId;

    @Schema(description = "门店ID", example = "12719")
    private Long storeId;

    @Schema(description = "类型", required = true, example = "1")
    @NotNull(message = "类型不能为空")
    private String type;

    @Schema(description = "文件类型", required = true, example = "2")
    @NotNull(message = "文件类型不能为空")
    private Integer fileType;

    /**
     * 商品spuId
     */
    @Schema(description = "商品spuId")
    private Long spuId;

    /**
     * 商品skuId
     */
    @Schema(description = "商品skuId")
    private Long skuId;

    @Schema(description = "文件url列表", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "文件url列表不能为空")
    private List<FileInfo> fileUrlList;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FileInfo {
        private String fileUrl;
        private Long fileSize;
        private String coverUrl;
    }
}
