package com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;
import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.storeinfoauditlog.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.storeinfoauditlog.SpStoreInfoAuditLogDO;
import com.yitong.octopus.module.sp.convert.storeinfoauditlog.SpStoreInfoAuditLogConvert;
import com.yitong.octopus.module.sp.service.storeinfoauditlog.SpStoreInfoAuditLogService;

@Tag(name = "管理后台 - 门店审核记录")
@RestController
@RequestMapping("/sp/store-info-audit-log")
@Validated
public class SpStoreInfoAuditLogController {

    @Resource
    private SpStoreInfoAuditLogService storeInfoAuditLogService;

    @PostMapping("/create")
    @Operation(summary = "创建门店审核记录")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:create')")
    public CommonResult<Long> createStoreInfoAuditLog(@Valid @RequestBody SpStoreInfoAuditLogCreateReqVO createReqVO) {
        return success(storeInfoAuditLogService.createStoreInfoAuditLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店审核记录")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:update')")
    public CommonResult<Boolean> updateStoreInfoAuditLog(@Valid @RequestBody SpStoreInfoAuditLogUpdateReqVO updateReqVO) {
        storeInfoAuditLogService.updateStoreInfoAuditLog(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店审核记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:delete')")
    public CommonResult<Boolean> deleteStoreInfoAuditLog(@RequestParam("id") Long id) {
        storeInfoAuditLogService.deleteStoreInfoAuditLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店审核记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:query')")
    public CommonResult<SpStoreInfoAuditLogRespVO> getStoreInfoAuditLog(@RequestParam("id") Long id) {
        SpStoreInfoAuditLogDO storeInfoAuditLog = storeInfoAuditLogService.getStoreInfoAuditLog(id);
        return success(SpStoreInfoAuditLogConvert.INSTANCE.convert(storeInfoAuditLog));
    }

    @GetMapping("/list")
    @Operation(summary = "获得门店审核记录列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:query')")
    public CommonResult<List<SpStoreInfoAuditLogRespVO>> getStoreInfoAuditLogList(@RequestParam("ids") Collection<Long> ids) {
        List<SpStoreInfoAuditLogDO> list = storeInfoAuditLogService.getStoreInfoAuditLogList(ids);
        return success(SpStoreInfoAuditLogConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店审核记录分页")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:query')")
    public CommonResult<PageResult<SpStoreInfoAuditLogRespVO>> getStoreInfoAuditLogPage(@Valid SpStoreInfoAuditLogPageReqVO pageVO) {
        PageResult<SpStoreInfoAuditLogDO> pageResult = storeInfoAuditLogService.getStoreInfoAuditLogPage(pageVO);
        return success(SpStoreInfoAuditLogConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出门店审核记录 Excel")
    @PreAuthorize("@ss.hasPermission('sp:store-info-audit-log:export')")
    public void exportStoreInfoAuditLogExcel(@Valid SpStoreInfoAuditLogExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpStoreInfoAuditLogDO> list = storeInfoAuditLogService.getStoreInfoAuditLogList(exportReqVO);
        // 导出 Excel
        List<SpStoreInfoAuditLogExcelVO> datas = SpStoreInfoAuditLogConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "门店审核记录.xls", "数据", SpStoreInfoAuditLogExcelVO.class, datas);
    }

}
