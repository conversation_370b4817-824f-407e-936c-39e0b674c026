package com.yitong.octopus.module.sp.service.maininfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.Sets;
import com.yitong.octopus.framework.common.util.number.NumberUtils;
import com.yitong.octopus.framework.datapermission.core.annotation.DataPermission;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.security.core.LoginUser;
import com.yitong.octopus.framework.security.core.util.SecurityFrameworkUtils;
import com.yitong.octopus.framework.tenant.core.context.TenantContextHolder;
import com.yitong.octopus.framework.tenant.core.util.TenantUtils;
import com.yitong.octopus.module.infra.api.utils.SysConfigUtils;
import com.yitong.octopus.module.platform.api.property.PlatformPropertyApi;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyValueVo;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyVo;
import com.yitong.octopus.module.platform.api.tag.PlatformTagApi;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagVo;
import com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo.SpMainInfoAqiCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.maininfoauditlog.vo.SpMainInfoAuditLogCreateReqVO;
import com.yitong.octopus.module.sp.controller.admin.userdatascop.vo.PermissionAssignSpDataScopeReqVO;
import com.yitong.octopus.module.sp.dal.dataobject.maininfopropertiesrel.SpMainInfoPropertiesRelDO;
import com.yitong.octopus.module.sp.dal.dataobject.maininfo.SpMainInfoTagRelDO;
import com.yitong.octopus.module.sp.dal.dataobject.userdatascop.SpUserDataScopeDO;
import com.yitong.octopus.module.sp.enums.AuditTypeEnum;
import com.yitong.octopus.module.sp.enums.SpCommonStatusEnum;
import com.yitong.octopus.module.sp.enums.SpContractTypeEnum;
import com.yitong.octopus.module.sp.enums.SpTypeEnum;
import com.yitong.octopus.module.sp.mq.producer.maininfo.SpMainInfoProducer;
import com.yitong.octopus.module.sp.permission.enums.SpDataScopeEnum;
import com.yitong.octopus.module.sp.service.maininfoaqi.SpMainInfoAqiService;
import com.yitong.octopus.module.sp.service.maininfoauditenterprise.SpMainInfoAuditEnterpriseService;
import com.yitong.octopus.module.sp.service.maininfoauditlog.SpMainInfoAuditLogService;
import com.yitong.octopus.module.sp.service.maininfoauditpersonal.SpMainInfoAuditPersonalService;
import com.yitong.octopus.module.sp.service.mainplatformcontract.SpMainPlatformContractService;
import com.yitong.octopus.module.sp.service.userdatascop.SpUserDataScopeService;
import com.yitong.octopus.module.system.controller.admin.user.vo.user.UserCreateReqVO;
import com.yitong.octopus.module.system.enums.ErrorCodeConstants;
import com.yitong.octopus.module.system.service.permission.PermissionService;
import com.yitong.octopus.module.system.service.user.AdminUserService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfo.SpMainInfoDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.sp.convert.maininfo.SpMainInfoConvert;
import com.yitong.octopus.module.sp.dal.mysql.maininfo.SpMainInfoMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.framework.common.util.collection.CollectionUtils.convertMap;
import static com.yitong.octopus.module.platform.enums.ErrorCodeConstants.*;
import static com.yitong.octopus.module.sp.enums.ConfigConstants.*;
import static com.yitong.octopus.module.sp.enums.ErrorCodeConstants.*;

/**
 * 主体基本信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class SpMainInfoServiceImpl implements SpMainInfoService {

    @Resource
    private SpMainInfoMapper mainInfoMapper;

    @Resource
    private SpMainInfoAuditPersonalService spMainInfoAuditPersonalService;

    @Resource
    private SpMainInfoAuditEnterpriseService spMainInfoAuditEnterpriseService;

    @Resource
    private SpMainInfoAqiService spMainInfoAqiService;

    @Resource
    private SpMainPlatformContractService mainPlatformContractService;

    @Resource
    private SpMainInfoAuditLogService mainInfoAuditLogService;

    @Resource
    private AdminUserService userService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private SpMainInfoTagRelService spMainInfoTagRelService;

    @Resource
    private SpMainInfoPropertiesRelService spMainInfoPropertiesRelService;

    @Resource
    private PlatformTagApi platformTagApi;

    @Resource
    private PlatformPropertyApi platformPropertyApi;

    @Resource
    private SpUserDataScopeService spUserDataScopeService;

    /**
     * 【服务商/商家】缓存
     * key：部门编号 {@link SpMainInfoDO#getId()}
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    @Getter
    private volatile Map<Long, SpMainInfoDO> spMainInfoCache;
    /**
     * 父部门缓存
     * key：【服务商/商家】 {@link SpMainInfoDO#getOwnerSpId()}
     * value: 直接子【服务商/商家】列表
     *
     * 这里声明 volatile 修饰的原因是，每次刷新时，直接修改指向
     */
    @Getter
    private volatile Multimap<Long, SpMainInfoDO> parentSpMainInfoCache;

    @Resource
    private SpMainInfoProducer spMainInfoProducer;

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = RedisKeyConstants.SP_MAIN_INFO_IDS, key = "#reqVO.permission", condition = "#reqVO.permission != null")
    public Long createMainInfo(SpMainInfoCreateReqVO vo) {

        SpMainInfoDO mainInfo = SpMainInfoConvert.INSTANCE.convert(vo);
//        platformPropertyApi.checkProperties(vo.getProperties());
        validateSpSameExists(mainInfo);
        //初始化草稿状态
        mainInfo.setSpStatus(SpCommonStatusEnum.DRAFT.getStatus());
        // 非管理员添加，增加默认绑定
        LoginUser loginUser = SecurityFrameworkUtils.getLoginUser();
        if (!loginUser.isAdmin()){
            mainInfo.setOwnerSpId(loginUser.getSpId());
        }else {
            // 默认数据
            mainInfo.setOwnerSpId(NumberUtils.parseLong(SysConfigUtils.getConfigByKey(SYS_INIT_OWNER_SP)));
        }
        // 插入主体信息
        mainInfoMapper.insert(mainInfo);
        //认证信息
        //认证信息-个人
        if (SpTypeEnum.PERSON.getStatus().equals(vo.getSpType())){
            if (ObjectUtil.isNull(vo.getAuditPersonalVo())){
              throw exception(MAIN_INFO_AUDIT_PERSONAL_NOT_EXISTS);
            }
            vo.getAuditPersonalVo().setSpId(mainInfo.getId());
            spMainInfoAuditPersonalService.createMainInfoAuditPersonal(vo.getAuditPersonalVo());
        }else {   //认证信息-企业
            if (ObjectUtil.isNull(vo.getAuditEnterpriseVo())){
                throw exception(MAIN_INFO_AUDIT_ENTERPRISE_NOT_EXISTS);
            }
            vo.getAuditEnterpriseVo().setSpId(mainInfo.getId());
            spMainInfoAuditEnterpriseService.createMainInfoAuditEnterprise(vo.getAuditEnterpriseVo());
        }

        //资质信息
        if (ObjectUtil.isNotNull(vo.getAqiInfoVo())){
            vo.getAqiInfoVo().setSpId(mainInfo.getId());
            spMainInfoAqiService.createMainInfoAqi(vo.getAqiInfoVo());
        }

        //商标
        if (ObjectUtil.isNotNull(vo.getTrcInfoVo())){
            vo.getAqiInfoVo().setSpId(mainInfo.getId());
            spMainInfoAqiService.createMainInfoAqi(vo.getTrcInfoVo());
        }

        //合同信息
        if (ObjectUtil.isNotNull(vo.getContractVo())){
            vo.getContractVo().setSpId(mainInfo.getId());
            vo.getContractVo().setSpType(vo.getSpType());
            mainPlatformContractService.createMainPlatformContract(vo.getContractVo());
        }
        // 保存当前用户与商家关系
        Long currentUserId = SecurityFrameworkUtils.getLoginUserId();
        SpUserDataScopeDO spUserDataScopeDO = spUserDataScopeService.getUserDataScopeByUserId(currentUserId);
        PermissionAssignSpDataScopeReqVO scopeReqVO;
        if (ObjectUtil.isNull(spUserDataScopeDO)) {
            scopeReqVO = new PermissionAssignSpDataScopeReqVO()
                    .setUserId(currentUserId)
                    .setDataScope(SpDataScopeEnum.SP_CUSTOM.getScope())
                    .setDataScopeSpIds(Sets.newHashSet(mainInfo.getId()));
            //重新分配用户与商家关系
            spUserDataScopeService.assignUserSpDataScope(scopeReqVO);
            //刷新缓存
            spMainInfoProducer.sendSpMainInfoRefreshMessage();
        }else {
            if (SpDataScopeEnum.SP_CUSTOM.getScope().equals(spUserDataScopeDO.getDataScope())){
                spUserDataScopeDO.getDataScopeSpIds().add(mainInfo.getId());
                scopeReqVO = new PermissionAssignSpDataScopeReqVO()
                        .setUserId(currentUserId)
                        .setDataScope(SpDataScopeEnum.SP_CUSTOM.getScope())
                        .setDataScopeSpIds(spUserDataScopeDO.getDataScopeSpIds());
                //重新分配用户与商家关系
                spUserDataScopeService.assignUserSpDataScope(scopeReqVO);
                //刷新缓存
                spMainInfoProducer.sendSpMainInfoRefreshMessage();
            }
        }
        // 返回
        return mainInfo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = RedisKeyConstants.SP_MAIN_INFO_IDS, allEntries = true) // allEntries 清空所有缓存，因为 permission 如果变更，涉及到新老两个 permission。直接清理，简单有效
    public void updateMainInfo(SpMainInfoUpdateReqVO vo) {
        SpMainInfoDO mainInfo = mainInfoMapper.selectById(vo.getId());
        if (ObjectUtil.isNull(mainInfo)) {
            throw exception(MAIN_INFO_NOT_EXISTS);
        }
        SpMainInfoDO updateObj = SpMainInfoConvert.INSTANCE.convert(vo);
        validateSpSameExists(updateObj);
//        platformPropertyApi.checkProperties(vo.getProperties());
        mainInfoMapper.updateById(updateObj);
        //认证信息-个人
        if (SpTypeEnum.PERSON.getStatus().equals(vo.getSpType())){
            if (ObjectUtil.isNull(vo.getAuditPersonalVo())){
                throw exception(MAIN_INFO_AUDIT_PERSONAL_NOT_EXISTS);
            }
            vo.getAuditPersonalVo().setSpId(mainInfo.getId());
            spMainInfoAuditPersonalService.updateMainInfoAuditPersonal(vo.getAuditPersonalVo());
        }else {   //认证信息-企业
            if (ObjectUtil.isNull(vo.getAuditEnterpriseVo())){
                throw exception(MAIN_INFO_AUDIT_ENTERPRISE_NOT_EXISTS);
            }
            vo.getAuditEnterpriseVo().setSpId(mainInfo.getId());
            spMainInfoAuditEnterpriseService.updateMainInfoAuditEnterprise(vo.getAuditEnterpriseVo());
        }

        //资质信息
        if (ObjectUtil.isNotNull(vo.getAqiInfoVo())){
            //资质信息
            vo.getAqiInfoVo().setSpId(mainInfo.getId());
            spMainInfoAqiService.updateMainInfoAqi(vo.getAqiInfoVo());
        }
        // 商标资质
        if (ObjectUtil.isNotNull(vo.getTrcInfoVo())){
            vo.getTrcInfoVo().setSpId(mainInfo.getId());
            if (ObjectUtil.isNull(vo.getTrcInfoVo().getId())){
                spMainInfoAqiService.createMainInfoAqi(BeanUtil.toBean(vo.getTrcInfoVo(), SpMainInfoAqiCreateReqVO.class));
            }else {
                spMainInfoAqiService.updateMainInfoAqi(vo.getTrcInfoVo());
            }
        }

        //合同信息
        if (ObjectUtil.isNotNull(vo.getContractVo())){
            //合同信息
            if(StrUtil.isEmpty(vo.getContractVo().getLinkPhone())){
                vo.getContractVo().setLinkPhone(vo.getLinkPhone());
            }
            vo.getContractVo().setSpId(mainInfo.getId());
            vo.getContractVo().setSpType(vo.getSpType());
            mainPlatformContractService.updateMainPlatformContract(vo.getContractVo());
        }

        //刷新缓存
        spMainInfoProducer.sendSpMainInfoRefreshMessage();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
//    @CacheEvict(value = RedisKeyConstants.SP_MAIN_INFO_IDS, allEntries = true) // allEntries 清空所有缓存，因为 permission 如果变更，涉及到新老两个 permission。直接清理，简单有效
    public void auditMainInfo(SpMainInfoAuditReqVO auditReqVO, SpContractTypeEnum spContractTypeEnum) {
        if (CollectionUtil.isEmpty(auditReqVO.getSpIds())){
            return;
        }
        auditReqVO.getSpIds().forEach(spId ->{
            SpMainInfoDO mainInfo = mainInfoMapper.selectById(spId);
            if (ObjectUtil.isNull(mainInfo)) {
                throw exception(MAIN_INFO_NOT_EXISTS);
            }
            //非待审核的无法审批
            if(!SpCommonStatusEnum.PENDING_APPROVAL.getStatus().equals(mainInfo.getSpStatus())){
                throw exception(MAIN_INFO_NOTE_AUDIT_EXISTS);
            }
            //更新状态
            if (AuditTypeEnum.FAIL.getStatus().equals(auditReqVO.getAuditStatus())){
                mainInfo.setSpStatus(SpCommonStatusEnum.DRAFT.getStatus());
            }else if (AuditTypeEnum.SUCCESS.getStatus().equals(auditReqVO.getAuditStatus())){
                mainInfo.setSpStatus(SpCommonStatusEnum.SUCCESS.getStatus());
                //仅服务商生成后台账号
                if (SpContractTypeEnum.SP.getType().equals(spContractTypeEnum.getType())){
                    //开通系统账号
                    UserCreateReqVO reqVO = new UserCreateReqVO();
                    reqVO.setSpId(mainInfo.getId());
                    reqVO.setUsername(mainInfo.getLinkPhone());
                    reqVO.setNickname(mainInfo.getSpName());
                    reqVO.setMobile(mainInfo.getLinkPhone());

                    reqVO.setPassword(SysConfigUtils.getConfigAllByKey(SYS_USER_INIT_PASSWORD));
                    reqVO.setEmail(mainInfo.getLinkEmail());
                    Long userId = userService.createUser(reqVO);
                    String roleIdsStr = null;
                    if (SpContractTypeEnum.SP.equals(spContractTypeEnum)){
                        roleIdsStr = SysConfigUtils.getConfigSEByKey(SYS_USER_ROLES_INIT_SP);
                    }else if (SpContractTypeEnum.MERCHANT.equals(spContractTypeEnum)){
                        roleIdsStr = SysConfigUtils.getConfigSEByKey(SYS_USER_ROLES_INIT_MERCHANT);
                    }else {
                        exception(ErrorCodeConstants.ROLE_NOT_EXISTS);
                    }
                    Set<Long> roleIds = Sets.newHashSet(NumberUtils.parseLongArray(roleIdsStr));;
                    if (CollectionUtil.isEmpty(roleIds)){
                        exception(ErrorCodeConstants.ROLE_NOT_EXISTS);
                    }
                    //分配角色
                    permissionService.assignUserRole(userId,roleIds);
                }
            }else {
                throw exception(MAIN_INFO_NOTE_AUDIT_EXISTS);
            }
            mainInfoMapper.updateById(mainInfo);

            //记录审核日志
            SpMainInfoAuditLogCreateReqVO logVo = BeanUtil.toBean(auditReqVO,SpMainInfoAuditLogCreateReqVO.class);
            logVo.setSpName(mainInfo.getSpName());
            logVo.setAuditUserId(SecurityFrameworkUtils.getLoginUser().getId());
            logVo.setAuditTime(DateTime.now().toLocalDateTime());
            mainInfoAuditLogService.createMainInfoAuditLog(logVo);
        });
        //刷新缓存
        spMainInfoProducer.sendSpMainInfoRefreshMessage();
    }

    @Override
    public void auditMainInfoQual(SpMainInfoAuditQualReqVO auditReqVO) {
        if (ObjectUtil.isNull(auditReqVO.getMainAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getIndustryAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getPoiAuditStatus())
                && ObjectUtil.isNull(auditReqVO.getClaimStatus())){
            return;
        }
        LambdaUpdateWrapper<SpMainInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getMainAuditStatus()),SpMainInfoDO::getMainAuditStatus,auditReqVO.getMainAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getIndustryAuditStatus()),SpMainInfoDO::getIndustryAuditStatus,auditReqVO.getIndustryAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getPoiAuditStatus()),SpMainInfoDO::getPoiAuditStatus,auditReqVO.getPoiAuditStatus());
        updateWrapper.set(ObjectUtil.isNotNull(auditReqVO.getClaimStatus()),SpMainInfoDO::getClaimStatus,auditReqVO.getClaimStatus());
        updateWrapper.in(SpMainInfoDO::getId,auditReqVO.getIds());
        mainInfoMapper.update(updateWrapper);
    }

    @Override
//    @CacheEvict(value = RedisKeyConstants.SP_MAIN_INFO_IDS, allEntries = true) // allEntries 清空所有缓存，因为 permission 如果变更，涉及到新老两个 permission。直接清理，简单有效
    public void deleteMainInfo(Long id) {
        // 校验存在
        validateMainInfoExists(id);
        // 删除
        mainInfoMapper.deleteById(id);
        spMainInfoAuditEnterpriseService.deleteMainInfoAuditEnterpriseBySpId(id);
        spMainInfoAuditPersonalService.deleteMainInfoAuditPersonalBySpId(id);
        spMainInfoAqiService.deleteMainInfoAqiBySpId(id);
        mainPlatformContractService.deleteMainPlatformContractBySpId(id);
        //刷新缓存
        spMainInfoProducer.sendSpMainInfoRefreshMessage();
    }

    @Override
    public SpMainInfoDO getMainInfo(Long id) {
        return mainInfoMapper.selectById(id);
    }

    @DataPermission(enable = false) // 关闭数据权限，不然就会出现递归获取数据权限的问题
    @Override
    public SpMainInfoDO getMainInfoByOwnerId(Long ownerId) {
        return mainInfoMapper.selectById(ownerId);
    }

    @Override
    public SpMainInfoSimpleRespVO getMainInfoSampleById(Long id) {
        SpMainInfoDO spMainInfoDO = getMainInfo(id);
        return BeanUtil.toBean(spMainInfoDO,SpMainInfoSimpleRespVO.class);
    }

    @Override
    public List<SpMainInfoSimpleRespVO> getMainInfoSampleByIds(Collection<Long> ids) {
        List<SpMainInfoDO> spMainInfoDOList = mainInfoMapper.selectList(new LambdaQueryWrapperX<SpMainInfoDO>().in(SpMainInfoDO::getId,ids));
        return BeanUtil.copyToList(spMainInfoDOList,SpMainInfoSimpleRespVO.class);
    }

    @Override
    public SpMainInfoDO getMainInfoSampleByName(String spName) {
        return mainInfoMapper.selectOne(new LambdaQueryWrapperX<SpMainInfoDO>().eq(SpMainInfoDO::getSpName,spName));
    }


    /**
     * 检查服务商/商家名称唯一性
     * @param infoDO
     */
    private void validateSpSameExists(SpMainInfoDO infoDO) {
        List<SpMainInfoDO> mainInfoDOList = mainInfoMapper.selectList(new LambdaQueryWrapperX<SpMainInfoDO>()
                .eq(SpMainInfoDO::getSpName,infoDO.getSpName())
                .neIfPresent(SpMainInfoDO::getId,infoDO.getId())
        );
        if (CollUtil.isNotEmpty(mainInfoDOList)){
            throw exception(MAIN_INFO_NAME_EXISTS);
        }
    }


    private void validateMainInfoExists(Long id) {
        if (mainInfoMapper.selectById(id) == null) {
            throw exception(MAIN_INFO_NOT_EXISTS);
        }
    }

    @Override
    public SpMainInfoViewRespVO getMainInfoViewById(Long id) {
        SpMainInfoDO o = getMainInfo(id);
        if (ObjectUtil.isNull(o)){
            return new SpMainInfoViewRespVO();
        }
        SpMainInfoViewRespVO vo = BeanUtil.toBean(o, SpMainInfoViewRespVO.class);
        //认证信息
        vo.setAuditEnterpriseVo(spMainInfoAuditEnterpriseService.getMainInfoAuditEnterpriseByMainId(id));
        vo.setAuditPersonalVo(spMainInfoAuditPersonalService.getMainInfoAuditPersonalByMainId(id));
        //资质信息
        vo.setAqiInfoVo(spMainInfoAqiService.getMainInfoAqiByMainId(id));
        //商标授权书
        vo.setTrcInfoVo(spMainInfoAqiService.getMainInfoTrademarkAqiByMainId(id));
        //合同
        vo.setContractVo(mainPlatformContractService.getMainPlatformContractBySpId(id));
        if (ObjectUtil.isNotEmpty(o.getOwnerSpId())){
            vo.setOwnerVo(SpMainInfoConvert.INSTANCE.convert(getMainInfo(Long.valueOf(o.getOwnerSpId()))));
        }
        return vo;
    }

    @Override
    public SpMainInfoWithOwnerRespVO getMainInfoWithOwnerById(Long id) {
        SpMainInfoDO spMainInfoDO = getMainInfo(id);
        if (ObjectUtil.isNull(spMainInfoDO)){
            return null;
        }
        SpMainInfoWithOwnerRespVO vo = new SpMainInfoWithOwnerRespVO().setId(spMainInfoDO.getId()).setSpName(spMainInfoDO.getSpName());
        if (ObjectUtil.isNotNull(spMainInfoDO.getOwnerSpId())){
            SpMainInfoDO ownerInfo = getMainInfoByOwnerId(spMainInfoDO.getOwnerSpId());
            if (ObjectUtil.isNotNull(ownerInfo)){
                vo.setOwnerSp(new SpMainInfoWithOwnerRespVO().setId(ownerInfo.getId()).setSpName(ownerInfo.getSpName()));
            }
        }
        return vo;
    }

    @Override
    public List<SpMainInfoDO> getMainInfoList(Collection<Long> ids) {
        return mainInfoMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<SpMainInfoRespVO> getMainInfoPage(SpMainInfoPageReqVO pageReqVO, SpContractTypeEnum spContractTypeEnum) {
        PageResult<SpMainInfoDO> pageResult = mainInfoMapper.selectPage(pageReqVO,spContractTypeEnum);
        PageResult<SpMainInfoRespVO> result = SpMainInfoConvert.INSTANCE.convertPage(pageResult);
        if (CollectionUtil.isNotEmpty(result.getList())){
            result.getList().forEach( p ->{
                if (ObjectUtil.isNotEmpty(p.getOwnerSpId())){
                    SpMainInfoDO o = getMainInfo(p.getOwnerSpId());
                    if (ObjectUtil.isNotNull(o)){
                        p.setOwnerSpName(o.getSpName());
                    }
                }
            });
        }
        return result;
    }

    @Override
    public List<SpMainInfoDO> getMainInfoList(SpMainInfoPageReqVO exportReqVO, SpContractTypeEnum spContractTypeEnum) {
        return mainInfoMapper.selectList(exportReqVO,spContractTypeEnum);
    }

    @Override
    public void validateSpList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得商家信息
        List<SpMainInfoDO> posts = mainInfoMapper.selectBatchIds(ids);
        Map<Long, SpMainInfoDO> spMainMap = convertMap(posts, SpMainInfoDO::getId);
        // 校验
        ids.forEach(id -> {
            SpMainInfoDO infoDO = spMainMap.get(id);
            if (ObjectUtil.isNull(infoDO)) {
                throw exception(MAIN_INFO_NOT_EXISTS);
            }
            if (!SpCommonStatusEnum.SUCCESS.getStatus().equals(infoDO.getSpStatus())) {
                throw exception(MAIN_INFO_NOT_ENABLE, infoDO.getSpName());
            }
        });
    }

    @Override
    public List<SpMainInfoDO> getAutoSettleSpList() {
        return mainInfoMapper.getAutoSettleSpList();
    }

    @Override
    public void changeOwnerSpMainInfo(SpMainInfoChangeOwnerSpReqVO updateReqVO) {
        List<SpMainInfoDO>  spMainInfoDOList =  CollectionUtil.toList(updateReqVO.getIds()).stream().map(i ->
                 new SpMainInfoDO().setId(i).setOwnerSpId(updateReqVO.getOwnerSpId())).collect(Collectors.toList());
        mainInfoMapper.updateBatchById(spMainInfoDOList);
    }

    @Override
    public List<SpMainInfoSimpleTreeRespVO> getAllSpList() {
        List<SpMainInfoDO>  list = mainInfoMapper.selectList();
        return CollectionUtil.emptyIfNull(list).stream().map(i->
                new SpMainInfoSimpleTreeRespVO().setId(i.getId())
                        .setName(i.getSpName())
                        .setParentId(i.getOwnerSpId()))
                .collect(Collectors.toList());
    }

    /**
     * 初始化 {@link #parentSpMainInfoCache} 和 {@link #spMainInfoCache} 缓存
     */
    @Async
    @Override
    @PostConstruct
    @DataPermission(enable = false) // 关闭数据权限，不然就会出现递归获取数据权限的问题
    public synchronized void initLocalCache() {
        // 注意：忽略自动多租户，因为要全局初始化缓存
        TenantUtils.executeIgnore(() -> {
            // 第一步：查询数据
            List<SpMainInfoDO> list = mainInfoMapper.selectALL();
            log.info("[initLocalCache][缓存【服务商/商家】，数量为:{}【start】]", list.size());

            // 第二步：构建缓存
            ImmutableMap.Builder<Long, SpMainInfoDO> builder = ImmutableMap.builder();
            ImmutableMultimap.Builder<Long, SpMainInfoDO> parentBuilder = ImmutableMultimap.builder();
            list.forEach(spMainInfo -> {
                builder.put(spMainInfo.getId(), spMainInfo);
                if (ObjectUtil.isNotNull(spMainInfo.getOwnerSpId())){
                    parentBuilder.put(spMainInfo.getOwnerSpId(), spMainInfo);
                }
            });
            spMainInfoCache = builder.build();
            parentSpMainInfoCache = parentBuilder.build();
            log.info("[initLocalCache][缓存【服务商/商家】，数量为:{}【end】]", list.size());
        });
    }

    @Override
    public List<SpMainInfoDO> getSpMainListByParentIdFromCache(Long parentId, boolean recursive) {
        if (parentId == null) {
            return Collections.emptyList();
        }
        List<SpMainInfoDO> result = new ArrayList<>();
        // 递归，简单粗暴
        getSpMainInfoByParentIdFromCache(result, parentId,
                recursive ? Integer.MAX_VALUE : 1, // 如果递归获取，则无限；否则，只递归 1 次
                parentSpMainInfoCache);
        return result;
    }

    @Override
    public List<SpMainInfoDO> getSpMainListByParentId(Long parentId) {
        return mainInfoMapper.selectSpMainInfoByParentId(parentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void makeLabel(SpMainInfoTagRelSaveReqVO tagRelSaveReqVO) {
        SpMainInfoDO spMainInfoDO = getMainInfo(tagRelSaveReqVO.getId());
        if (ObjectUtil.isNull(spMainInfoDO)){
            throw exception(MAIN_INFO_NOT_EXISTS);
        }
        //移除相关标签
        List<PlatformTagVo> removeTag = tagRelSaveReqVO.getRemoveTag();
        if (CollectionUtil.isNotEmpty(removeTag)){
            spMainInfoTagRelService.remove(new LambdaQueryWrapperX<SpMainInfoTagRelDO>()
                    .eq(SpMainInfoTagRelDO::getSpId,tagRelSaveReqVO.getId())
                    .in(SpMainInfoTagRelDO::getTagId,removeTag.stream().map(PlatformTagVo::getId).collect(Collectors.toList()))
            );
        }

        //新增的标签
        List<PlatformTagVo> addTag = tagRelSaveReqVO.getAddTag();
        if (CollectionUtil.isNotEmpty(addTag)){
            List<SpMainInfoTagRelDO> tags = addTag.stream().map(tag ->{
                //检查标签的有效性
                PlatformTagVo platformTagVo = platformTagApi.getTagById(tag.getId());
                if (ObjectUtil.isNull(platformTagVo)){
                    throw exception(TAG_SELECT_DELETED);
                }
                SpMainInfoTagRelDO tagRel = new SpMainInfoTagRelDO();
                tagRel.setTagId(tag.getId());
                tagRel.setSpId(spMainInfoDO.getId());
                return tagRel;
            }).collect(Collectors.toList());
            spMainInfoTagRelService.saveOrUpdateBatch(tags);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchMakeLabel(SpMainInfoBatchTagRelSaveReqVO tagRelSaveReqVO) {
        tagRelSaveReqVO.getTags().forEach(tag -> makeLabel(tag));
    }

    @Override
    public List<PlatformTagGroupVo> getTagList(SpMainInfoTagReqVO reqVO) {
        List<SpMainInfoTagRelDO> tagList = spMainInfoTagRelService.getSpTagRelBySpuId(reqVO.getId());
        if (CollectionUtil.isEmpty(tagList)){
            return ListUtil.empty();
        }
        return platformTagApi.getTagGroupByIds(tagList.stream().map(SpMainInfoTagRelDO::getTagId).collect(Collectors.toList()),reqVO.getGroupId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void makeProperty(SpMainInfoPropertySaveReqVO reqVO) {
        SpMainInfoDO spMainInfoDO = getMainInfo(reqVO.getId());
        if (ObjectUtil.isNull(spMainInfoDO)){
            throw exception(MAIN_INFO_NOT_EXISTS);
        }
        //移除相关属性
        List<PlatformPropertyValueVo> removeProperties = reqVO.getRemove();
        if (CollectionUtil.isNotEmpty(removeProperties)){
            Set<Long> removeIds =  removeProperties.stream().map(PlatformPropertyValueVo::getId).collect(Collectors.toSet());
            spMainInfoPropertiesRelService.deleteMainInfoPropertiesRel(reqVO.getId(),removeIds);
        }

        //新增的属性
        List<PlatformPropertyValueVo> addTag = reqVO.getAdd();
        if (CollectionUtil.isNotEmpty(addTag)){
            List<SpMainInfoPropertiesRelDO> valueDtoList = addTag.stream().map(v ->{
                //检查属性的有效性
                PlatformPropertyValueVo propertyValueVo = platformPropertyApi.getPlatformPropertyById(v.getId());
                if (ObjectUtil.isNull(propertyValueVo)){
                    throw exception(PROPERTIES_SELECT_DELETED);
                }
                SpMainInfoPropertiesRelDO valueDto = new SpMainInfoPropertiesRelDO();
                valueDto.setPropertyId(v.getPropertyId());
                valueDto.setValueId(v.getId());
                valueDto.setValueName(v.getName());
                valueDto.setSpId(spMainInfoDO.getId());
                return valueDto;
            }).collect(Collectors.toList());
            spMainInfoPropertiesRelService.saveBatch(valueDtoList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchProperties(SpMainInfoBatchPropertySaveReqVO reqVO) {
        if (CollectionUtil.isNotEmpty(reqVO.getProperties())){
            reqVO.getProperties().stream().forEach(p ->makeProperty(p));
        }
    }

    @Override
    public List<PlatformPropertyVo> getPropertyList(SpMainInfoPropertyReqVO reqVO) {
        List<SpMainInfoPropertiesRelDO> spMainInfoPropertiesList = spMainInfoPropertiesRelService.getMainInfoPropertiesRelBySpuId(reqVO.getId());
        if (CollectionUtil.isEmpty(spMainInfoPropertiesList)){
            return ListUtil.empty();
        }
        return platformPropertyApi.getPropertyValueListByValueIdPropertyId(spMainInfoPropertiesList.stream().map(SpMainInfoPropertiesRelDO::getValueId).collect(Collectors.toList()),reqVO.getPropertyId());
    }

    /**
     * 递归获取所有的子【服务商/商家】，添加到 result 结果
     *
     * @param result 结果
     * @param parentId 父编号
     * @param recursiveCount 递归次数
     * @param parenSpInfoMap 父【服务商/商家】 Map，使用缓存，避免变化
     */
    private void getSpMainInfoByParentIdFromCache(List<SpMainInfoDO> result, Long parentId, int recursiveCount, Multimap<Long, SpMainInfoDO> parenSpInfoMap) {
        // 递归次数为 0，结束！
        if (recursiveCount == 0) {
            return;
        }

        // 获得子【服务商/商家】
        Collection<SpMainInfoDO> spInfoes = parenSpInfoMap.get(parentId);
        if (CollUtil.isEmpty(spInfoes)) {
            return;
        }
        // 针对多租户，过滤掉非当前租户的【服务商/商家】
        Long tenantId = TenantContextHolder.getTenantId();
        if (tenantId != null) {
            spInfoes = CollUtil.filterNew(spInfoes, spMainInfo -> tenantId.equals(spMainInfo.getTenantId()));
        }
        result.addAll(spInfoes);

        // 继续递归
        spInfoes.forEach(spInfo -> getSpMainInfoByParentIdFromCache(result, spInfo.getId(),
                recursiveCount - 1, parenSpInfoMap));
    }

}
