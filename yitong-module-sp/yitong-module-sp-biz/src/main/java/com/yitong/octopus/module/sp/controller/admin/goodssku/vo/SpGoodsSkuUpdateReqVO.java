package com.yitong.octopus.module.sp.controller.admin.goodssku.vo;

import javax.validation.constraints.NotNull;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品sku更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSkuUpdateReqVO extends SpGoodsSkuBaseVO {

    @Schema(description = "主键", required = true, example = "25294")
    @NotNull(message = "主键不能为空")
    private Long id;

	
}
