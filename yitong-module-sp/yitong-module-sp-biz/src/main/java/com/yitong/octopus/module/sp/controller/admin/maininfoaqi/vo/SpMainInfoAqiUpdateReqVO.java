package com.yitong.octopus.module.sp.controller.admin.maininfoaqi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 主体资质信息更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpMainInfoAqiUpdateReqVO extends SpMainInfoAqiBaseVO {

    @Schema(description = "id", required = true, example = "15165")
    @NotNull(message = "id不能为空")
    private Long id;

}
