package com.yitong.octopus.module.sp.controller.admin.mainbillbankcard.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.AreaConvert;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;
import com.yitong.octopus.module.platform.convert.excel.PlatformBankExcelConvert;
import com.yitong.octopus.module.sp.enums.DictTypeConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 商家核销账号 Excel 导入 VO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false) // 设置 chain = false，避免用户导入有问题
public class SpMainBillBankCardImportExcelVO {

    @ExcelProperty(value = "商家ID")
    @NotNull(message = "服务商/商家不能为空")
    private Long spId;

    @ExcelProperty(value = "门店Id")
    private Long storeId;

    @ExcelProperty(value = "收款账户类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SP_MAIN_BILL_BANK_CARD_CARD_TYPE)
    private Integer cardType;

    @ExcelProperty(value = "开户银行", converter = PlatformBankExcelConvert.class)
    @NotNull(message = "开户银行不能为空")
    private Long bankId;

    @ExcelProperty("收款账户名称")
    @NotEmpty(message = "收款账户名称不能为空")
    private String bankAccountName;

    @ExcelProperty("银行卡号")
    @NotEmpty(message = "银行卡号不能为空")
    private String bankNumber;

    @ExcelProperty("开户支行")
    @NotEmpty(message = "开户支行不能为空")
    private String subBankName;

    @ExcelProperty(value = "银行地址", converter = AreaConvert.class)
    @NotEmpty(message = "银行地址不能为空")
    private String bankProvince;

}
