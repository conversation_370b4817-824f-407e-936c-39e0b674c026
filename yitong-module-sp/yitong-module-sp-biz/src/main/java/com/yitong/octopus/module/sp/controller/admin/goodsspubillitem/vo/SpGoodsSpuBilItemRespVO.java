package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品spu结算信息 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SpGoodsSpuBilItemRespVO extends SpGoodsSpuBillItemBaseVO {

    @Schema(description = "主键", required = true, example = "17611")
    private Long id;

    @Schema(description = "创建时间", required = true)
    private LocalDateTime createTime;

}
