package com.yitong.octopus.module.sp.controller.admin.storeinfomap.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
* 商家门店地图信息 Base Dto，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class SpStoreInfoMapDto {

    @Schema(description = "id", example = "25053")
    private Long id;

    @Schema(description = "地图渠道ID", example = "1679")
    private Long mapChannelId;

    @Schema(description = "门店地图ID", required = true, example = "2795")
    @NotNull(message = "门店地图ID不能为空")
    private String mapId;

    @Schema(description = "门店POI")
    private String mapPoi;

    @Schema(description = "门店地图POI链接", required = true, example = "https://www.iocoder.cn")
    @NotNull(message = "门店地图POI链接不能为空")
    private String mapPoiUrl;

    @Schema(description = "纬度")
    private String latitude;

    @Schema(description = "经度", required = true)
    @NotNull(message = "经度不能为空")
    private String longitude;

    @Schema(description = "备注", example = "随便")
    private String remark;

}
