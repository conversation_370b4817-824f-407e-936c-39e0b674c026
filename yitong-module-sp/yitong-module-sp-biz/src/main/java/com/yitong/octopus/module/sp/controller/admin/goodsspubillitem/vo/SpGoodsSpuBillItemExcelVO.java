package com.yitong.octopus.module.sp.controller.admin.goodsspubillitem.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 商品spu结算信息 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class SpGoodsSpuBillItemExcelVO {

    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("主体Id")
    private Long spId;

    @ExcelProperty("商家门店Id")
    private Long storeId;

    @ExcelProperty("商品spuId")
    private Long spuId;

    @ExcelProperty("商品合同Id")
    private Long spContractId;

    @ExcelProperty("结算项ID")
    private Long billItemId;

    @ExcelProperty("结算项值")
    private String billItemValue;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
