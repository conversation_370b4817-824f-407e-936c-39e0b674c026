package com.yitong.octopus.module.sp.controller.admin.maininfo;

import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.framework.datapermission.core.annotation.DataPermission;
import com.yitong.octopus.framework.security.core.annotations.PreAuthenticated;
import com.yitong.octopus.module.platform.api.property.vo.PlatformPropertyVo;
import com.yitong.octopus.module.platform.api.tag.vo.PlatformTagGroupVo;
import com.yitong.octopus.module.sp.enums.SpContractTypeEnum;
import com.yitong.octopus.framework.datapermission.core.rule.sp.SpDataPermissionRule;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.sp.controller.admin.maininfo.vo.*;
import com.yitong.octopus.module.sp.dal.dataobject.maininfo.SpMainInfoDO;
import com.yitong.octopus.module.sp.convert.maininfo.SpMainInfoConvert;
import com.yitong.octopus.module.sp.service.maininfo.SpMainInfoService;

@Tag(name = "管理后台 - 主体基本信息")
@RestController
@RequestMapping("/sp/main-info")
@Validated
@DataPermission(includeRules = SpDataPermissionRule.class)
public class SpMainInfoController {

    @Resource
    private SpMainInfoService mainInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建主体基本信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info:create')")
    public CommonResult<Long> createMainInfo(@Valid @RequestBody SpMainInfoCreateReqVO createReqVO) {
        return success(mainInfoService.createMainInfo(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新主体基本信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info:update')")
    public CommonResult<Boolean> updateMainInfo(@Valid @RequestBody SpMainInfoUpdateReqVO updateReqVO) {
        mainInfoService.updateMainInfo(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除主体基本信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info:delete')")
    public CommonResult<Boolean> deleteMainInfo(@RequestParam("id") Long id) {
        mainInfoService.deleteMainInfo(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得主体基本信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('sp:main-info:query')")
    public CommonResult<SpMainInfoViewRespVO> getMainInfo(@RequestParam("id") Long id) {
        SpMainInfoViewRespVO mainInfo = mainInfoService.getMainInfoViewById(id);
        return success(mainInfo);
    }

    @GetMapping("/list")
    @Operation(summary = "获得主体基本信息列表")
    @Parameter(name = "ids", description = "编号列表", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('sp:main-info:query')")
    public CommonResult<List<SpMainInfoRespVO>> getMainInfoList(@RequestParam("ids") Collection<Long> ids) {
        List<SpMainInfoDO> list = mainInfoService.getMainInfoList(ids);
        return success(SpMainInfoConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得主体基本信息分页")
    @PreAuthorize("@ss.hasPermission('sp:main-info:query')")
    public CommonResult<PageResult<SpMainInfoRespVO>> getMainInfoPage(@Valid SpMainInfoPageReqVO pageVO) {
        PageResult<SpMainInfoRespVO> pageResult = mainInfoService.getMainInfoPage(pageVO, SpContractTypeEnum.SP);
        return success(pageResult);
    }

    @GetMapping("/page-simple")
    @Operation(summary = "获得主体基本信息分页（简单）")
    @PreAuthorize("@ss.hasPermission('sp:main-info:query')")
    public CommonResult<PageResult<SpMainInfoSimpleRespVO>> getMainInfoSimplePage(@Valid SpMainInfoPageReqVO pageVO) {
        PageResult<SpMainInfoRespVO> pageResult = mainInfoService.getMainInfoPage(pageVO, null);
        return success(BeanUtils.toBean(pageResult,SpMainInfoSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出主体基本信息 Excel")
    @PreAuthorize("@ss.hasPermission('sp:main-info:export')")
    public void exportMainInfoExcel(@Valid SpMainInfoPageReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<SpMainInfoDO> list = mainInfoService.getMainInfoList(exportReqVO, SpContractTypeEnum.SP);
        // 导出 Excel
        List<SpMainInfoExcelVO> datas = SpMainInfoConvert.INSTANCE.convertList02(list);
        ExcelUtils.write(response, "主体基本信息.xls", "数据", SpMainInfoExcelVO.class, datas);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核主体基本信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info:audit')")
    public CommonResult<Boolean> auditMainInfo(@Valid @RequestBody SpMainInfoAuditReqVO vo) {
        mainInfoService.auditMainInfo(vo, SpContractTypeEnum.SP);
        return success(true);
    }

    @GetMapping("/list-all-simple")
    @Operation(summary = "获取服务商/商家精简信息列表", description = "只包含被开启的服务商和商家，主要用于前端的下拉选项")
    @PreAuthenticated
    public CommonResult<List<SpMainInfoSimpleTreeRespVO>> getSimpleSpMainInfoList() {
        return success(mainInfoService.getAllSpList());
    }

    /**
     *服务商/商家 打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/makeLabel")
    @Operation(summary = "服务商/商家 打标签")
    @PreAuthorize("@ss.hasPermission('sp:main-info:tags')")
    public CommonResult<Boolean>  makeLabel(@Valid @RequestBody SpMainInfoTagRelSaveReqVO tagRelSaveReqVO) {
        mainInfoService.makeLabel(tagRelSaveReqVO);
        return success(true);
    }

    /**
     *服务商/商家 打标签
     * @param tagRelSaveReqVO
     * @return
     */
    @PostMapping("/batchMakeLabel")
    @Operation(summary = "服务商/商家 批量打标签")
    @PreAuthorize("@ss.hasPermission('sp:main-info:tags')")
    public CommonResult<Boolean>  batchMakeLabel(@Valid @RequestBody SpMainInfoBatchTagRelSaveReqVO tagRelSaveReqVO) {
        mainInfoService.batchMakeLabel(tagRelSaveReqVO);
        return success(true);
    }

    @GetMapping("/tag-list")
    @Operation(summary = "获得服务商/商家标签列表")
    @Parameter(name = "reqVO", description = "编号列表", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info:tags')")
    public CommonResult<List<PlatformTagGroupVo>> getTagList(@Valid SpMainInfoTagReqVO reqVO) {
        List<PlatformTagGroupVo>list = mainInfoService.getTagList(reqVO);
        return success(list);
    }

    /**
     *服务商/商家属性
     * @param reqVO
     * @return
     */
    @PostMapping("/makeProperty")
    @Operation(summary = "服务商/商家加属性")
    @PreAuthorize("@ss.hasPermission('sp:main-info:property')")
    public CommonResult<Boolean>  makeProperty(@Valid @RequestBody SpMainInfoPropertySaveReqVO reqVO) {
        mainInfoService.makeProperty(reqVO);
        return success(true);
    }

    /**
     *服务商/商家属性【批量】
     * @param reqVO
     * @return
     */
    @PostMapping("/batchMakeProperty")
    @Operation(summary = "服务商/商家 批量属性")
    @PreAuthorize("@ss.hasPermission('sp:main-info:property')")
    public CommonResult<Boolean>  batchMakeProperty(@Valid @RequestBody SpMainInfoBatchPropertySaveReqVO reqVO) {
        mainInfoService.batchProperties(reqVO);
        return success(true);
    }

    @GetMapping("/property-list")
    @Operation(summary = "获得服务商/商家属性列表")
    @Parameter(name = "reqVO", description = "编号列表", required = true)
    @PreAuthorize("@ss.hasPermission('sp:main-info:query')")
    public CommonResult<List<PlatformPropertyVo>> getPropertyList(@Valid SpMainInfoPropertyReqVO reqVO) {
        List<PlatformPropertyVo>list = mainInfoService.getPropertyList(reqVO);
        return success(list);
    }

    @GetMapping("/list-simple")
    @Operation(summary = "获取服务商/商家精简信息列表", description = "只包含被开启的服务商和商家，主要用于前端的下拉选项")
    @PreAuthenticated
    public CommonResult<List<SpMainInfoSimpleRespVO>> getSimpleSpMainInfoListByIds(@RequestParam("ids") Collection<Long> ids) {
        return success(mainInfoService.getMainInfoSampleByIds(ids));
    }

    @PutMapping("/audit-qual")
    @Operation(summary = "审核主体资质信息")
    @PreAuthorize("@ss.hasPermission('sp:main-info:audit')")
    public CommonResult<Boolean> auditMainInfo(@Valid @RequestBody SpMainInfoAuditQualReqVO vo) {
        mainInfoService.auditMainInfoQual(vo);
        return success(true);
    }
}
