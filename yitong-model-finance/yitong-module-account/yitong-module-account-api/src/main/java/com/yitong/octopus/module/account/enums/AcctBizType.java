package com.yitong.octopus.module.account.enums;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
public enum AcctBizType {

	//@formatter:off
	SETTLE("SETTLE", "SETTLE"),
	WITHDRAW("<PERSON><PERSON><PERSON><PERSON><PERSON>", "WIT<PERSON><PERSON><PERSON>"),
	;
	//@formatter:on
	private String code;

	private String desc;

	private AcctBizType(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public String code() {
		return code;
	}

	public String desc() {
		return desc;
	}

	public static AcctBizType resolveByCode(String code) {
		if (code != null) {
			for (AcctBizType rtnCd : values()) {
				if (rtnCd.code().equals(code)) {
					return rtnCd;
				}
			}
		}
		return null;
	}

}
