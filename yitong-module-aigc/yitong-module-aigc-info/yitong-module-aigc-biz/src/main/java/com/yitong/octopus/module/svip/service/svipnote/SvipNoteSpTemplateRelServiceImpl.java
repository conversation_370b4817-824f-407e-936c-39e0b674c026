package com.yitong.octopus.module.svip.service.svipnote;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yitong.octopus.framework.common.enums.YTCommonStatusEnum;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.aigc.controller.admin.article.vo.ArticleGenerateReqVO;
import com.yitong.octopus.module.svip.dal.dataobject.svipnotesp.SvipNoteSpDO;
import com.yitong.octopus.module.svip.dal.dataobject.svipnotesp.SvipNoteSpStoreTemplateRelDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.controller.admin.svipnotesp.vo.*;
import com.yitong.octopus.module.svip.dal.dataobject.svipnotesp.SvipNoteSpTemplateRelDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.svipnotesp.SvipNoteSpTemplateRelMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.*;

/**
 * 增值服务商家笔记计划 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SvipNoteSpTemplateRelServiceImpl extends ServiceImpl<SvipNoteSpTemplateRelMapper,SvipNoteSpTemplateRelDO>  implements SvipNoteSpTemplateRelService {

    @Resource
    private SvipNoteSpService svipNoteSpService;

    @Override
    public Long createNoteSpTemplateRel(SvipNoteSpTemplateRelSaveReqVO reqVO) {
        SvipNoteSpDO svipNoteSp = svipNoteSpService.getNoteSp(reqVO.getNoteSpId());
        if (ObjectUtil.isNull(svipNoteSp)){
            throw exception(NOTE_SP_NOT_EXISTS);
        }
        // 插入
        SvipNoteSpTemplateRelDO rel = BeanUtils.toBean(reqVO, SvipNoteSpTemplateRelDO.class);
        rel.setSpId(svipNoteSp.getSpId());
        initModelParams(rel);
        getBaseMapper().insert(rel);
        // 返回
        return rel.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createNoteSpTemplateRelByBatch(SvipNoteSpTemplateRelSaveBatchReqVO reqVO) {
        SvipNoteSpDO svipNoteSp = svipNoteSpService.getNoteSp(reqVO.getNoteSpId());
        if (ObjectUtil.isNull(svipNoteSp)){
            throw exception(NOTE_SP_NOT_EXISTS);
        }
        List<SvipNoteSpTemplateRelDO> list = reqVO.getModelIds().stream().distinct()
                .filter(templateId -> ObjectUtil.isNull(getSvipNoteSpTemplateRelByNoteIdAndTemplateId(reqVO.getNoteSpId(),templateId)))
                .map(templateId ->{
                    SvipNoteSpTemplateRelDO rel = new SvipNoteSpTemplateRelDO();
                    rel.setNoteSpId(svipNoteSp.getId());
                    rel.setSpId(svipNoteSp.getSpId());
                    rel.setStatus(YTCommonStatusEnum.ENABLE.getStatus());
                    rel.setModelId(templateId);
                    initModelParams(rel);
                    rel.setWeight(100);
                    return  rel;
        }).collect(Collectors.toList());
       getBaseMapper().insertBatch(list);
    }

    private static void initModelParams(SvipNoteSpTemplateRelDO rel) {
        if (ObjectUtil.isNull(rel.getModelParams())){
            rel.setModelParams(new ArticleGenerateReqVO().setTemplateId(rel.getModelId()).setSpId(rel.getSpId().toString()));
        }
    }

    @Override
    public void updateNoteSpTemplateRelStatus(SvipNoteSpTemplateRelStatusReqVO reqVO) {
        getBaseMapper().update(
                new LambdaUpdateWrapper<SvipNoteSpTemplateRelDO>()
                        .set(SvipNoteSpTemplateRelDO::getStatus,reqVO.getStatus())
                        .in(SvipNoteSpTemplateRelDO::getId,reqVO.getIds())
        );
    }

    @Override
    public void updateNoteSpTemplateRel(SvipNoteSpTemplateRelSaveReqVO updateReqVO) {
        // 校验存在
        validateNoteSpTemplateRelExistsByNoteIdAndTemplateId(updateReqVO.getId(),updateReqVO.getNoteSpId(),updateReqVO.getModelId());
        // 更新
        SvipNoteSpTemplateRelDO rel = getNoteSpTemplateRel(updateReqVO.getId());
        if (ObjectUtil.isNull(rel)){
            throw exception(NOTE_SP_TEMPLATE_REL_NOT_EXISTS);
        }
        BeanUtils.copyProperties(updateReqVO, rel);
        initModelParams(rel);
        getBaseMapper().updateById(rel);
    }

    @Override
    public void deleteNoteSpTemplateRel(Long id) {
        // 校验存在
        validateNoteSpTemplateRelExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateNoteSpTemplateRelExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(NOTE_SP_TEMPLATE_REL_NOT_EXISTS);
        }
    }

    private void validateNoteSpTemplateRelExistsByNoteIdAndTemplateId(Long id,Long noteSpId, Long templateId ) {
        SvipNoteSpTemplateRelDO rel =  getBaseMapper().selectOne(new LambdaQueryWrapperX<SvipNoteSpTemplateRelDO>()
                .eq(SvipNoteSpTemplateRelDO::getNoteSpId,noteSpId)
                .eq(SvipNoteSpTemplateRelDO::getModelId,templateId)
                .neIfPresent(SvipNoteSpTemplateRelDO::getId,id)
        );
        if (ObjectUtil.isNotNull(rel)) {
            throw exception(NOTE_SP_TEMPLATE_REL_EXISTS);
        }
    }

    @Override
    public SvipNoteSpTemplateRelDO getNoteSpTemplateRel(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<SvipNoteSpTemplateRelDO> getNoteSpTemplateRelPage(SvipNoteSpTemplateRelPageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public List<SvipNoteSpTemplateRelDO> getSvipNoteSpTemplateRelBySpNoteId(Long spNoteId) {
        return getBaseMapper().selectList(new LambdaQueryWrapperX<SvipNoteSpTemplateRelDO>()
                .eq(SvipNoteSpTemplateRelDO::getStatus,YTCommonStatusEnum.ENABLE.getStatus())
        );
    }

    @Override
    public SvipNoteSpTemplateRelDO getSvipNoteSpTemplateRelByNoteIdAndTemplateId(Long noteSpId, Long templateId) {
        return getBaseMapper().selectOne(new LambdaQueryWrapperX<SvipNoteSpTemplateRelDO>()
            .eq(SvipNoteSpTemplateRelDO::getNoteSpId,noteSpId)
            .eq(SvipNoteSpTemplateRelDO::getModelId,templateId)
        );
    }

}