package com.yitong.octopus.module.aigc.controller.admin.talentitem.vo.item;

import com.yitong.octopus.module.aigc.controller.admin.article.vo.ArticleGenerateReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;

import static com.yitong.octopus.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
* 达人笔记 Base VO，提供给添加、修改、详细的子 VO 使用
* 如果子 VO 存在差异的字段，请不要添加到这里，影响 Swagger 文档生成
*/
@Data
public class TalentItemBaseVO {

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "类型", example = "2")
    private Integer type;

    @Schema(description = "笔记商户计划ID", example = "16720")
    private Long itemSpPlanId;

    @Schema(description = "达人ID", example = "20726")
    private Long talentId;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "审核结果")
    private String auditResult;

    @Schema(description = "审核人ID", example = "27180")
    private Long auditUserId;

    @Schema(description = "审核部门", example = "29141")
    private Long deptId;

    @Schema(description = "商家审核状态")
    private Integer auditStatusSp;

    @Schema(description = "商家审核结果")
    private String auditResultSp;

    @Schema(description = "商家审核人ID", example = "30433")
    private Long auditSpUserId;

    @Schema(description = "审核商户", example = "29825")
    private Long spId;

    @Schema(description = "门店", example = "29825")
    private List<Long> storeIds;

    @Schema(description = "笔记图片")
    @NotNull(message = "笔记图片不能为空")
    private List<String> itemImages;

    @Schema(description = "笔记标题")
    @NotEmpty(message = "笔记标题不能为空")
    private String itemTitle;

    @NotEmpty(message = "笔记内容不能为空")
    @Schema(description = "笔记内容")
    private String itemContent;

    @Schema(description = "笔记用户")
    private List<String> itemUser;

    @Schema(description = "笔记标签")
    private List<String> itemTags;

    @Schema(description = "笔记ID", example = "19335")
    private String itemId;

    @Schema(description = "笔记链接", example = "https://www.iocoder.cn")
    private String itemUrl;

    @Schema(description = "笔记发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime itemPublishTime;

    @Schema(description = "笔记失效时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime itemExpireTime;

    @Schema(description = "笔记点赞")
    private Integer itemLikeTotal;

    @Schema(description = "笔记收藏")
    private Integer itemLikeCollect;

    @Schema(description = "笔记评论")
    private Integer itemLikeComment;

    @Schema(description = "笔记分享")
    private Integer itemLikeShare;

    @Schema(description = "笔记关注")
    private Integer itemLikeAttention;

    @Schema(description = "全部-笔记曝光")
    private Integer itemExposureTotal;

    @Schema(description = "全部-笔记点击")
    private Integer itemClickTotal;

    @Schema(description = "笔记发布地址")
    private String itemAddr;

    /**
     * 批次号
     */
    @Schema(description = "批次号")
    private String batchNo;
    /**
     * 场景ID
     */
    @Schema(description = "场景")
    private String scenesId;
    /**
     * 模型ID
     */
    @Schema(description = "模型ID")
    private String modelId;

    @Schema(description = "【计划】模型参数（JSON）", example = "{'name':'123'}")
    private ArticleGenerateReqVO modelParams;

    @Schema(description = "是否POI，0:否 ,1:是")
    private Integer isPoi;

    @Schema(description = "SPUID")
    private Long spuId;

    @Schema(description = "权限类型：0:公开 1:私人")
    private Integer roleType;

    @Schema(description = "定时发布：0:否,1:是")
    private Integer timeType;

    @Schema(description = "发布时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime timeInfo;

    @Schema(description = "发布日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime publishDay;

    @Schema(description = "发布消息")
    private String publishMsg;
}
