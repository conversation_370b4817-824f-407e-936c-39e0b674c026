package com.yitong.octopus.module.svip.service.svipnote;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.yitong.octopus.framework.common.enums.YTCommonStatusEnum;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.sp.enums.SpStoreStatusEnum;
import com.yitong.octopus.module.svip.controller.admin.svipnotesp.vo.SvipNoteSpStorePageReqVO;
import com.yitong.octopus.module.svip.controller.admin.svipnotesp.vo.SvipNoteSpStoreSaveBatchReqVO;
import com.yitong.octopus.module.svip.controller.admin.svipnotesp.vo.SvipNoteSpStoreSaveReqVO;
import com.yitong.octopus.module.svip.controller.admin.svipnotesp.vo.SvipNoteStatusReqVO;
import com.yitong.octopus.module.svip.dal.dataobject.svipnotesp.SvipNoteSpDO;
import com.yitong.octopus.module.svip.dal.dto.svipnotesp.SvipNoteGeneratorDto;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yitong.octopus.module.svip.dal.dataobject.svipnotesp.SvipNoteSpStoreDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;

import com.yitong.octopus.module.svip.dal.mysql.svipnotesp.SvipNoteSpStoreMapper;

import java.util.List;
import java.util.stream.Collectors;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.svip.enums.ErrorCodeConstants.*;

/**
 * 增值服务笔记商家门店 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SvipNoteSpStoreServiceImpl extends ServiceImpl<SvipNoteSpStoreMapper,SvipNoteSpStoreDO>  implements SvipNoteSpStoreService {

    @Override
    public Long createNoteSpStore(SvipNoteSpStoreSaveReqVO reqVO) {
        validateNoteSpStoreSame(reqVO.getId(),reqVO.getNoteSpId(),reqVO.getStoreId());
        // 插入
        SvipNoteSpStoreDO noteSpStore = BeanUtils.toBean(reqVO, SvipNoteSpStoreDO.class);
        getBaseMapper().insert(noteSpStore);
        // 返回
        return noteSpStore.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createNoteSpStoreBatch(SvipNoteSpStoreSaveBatchReqVO createReqVO) {
        List<SvipNoteSpStoreDO> noteSpStoreDOList = createReqVO.getStoreId().stream().distinct()
            .filter(storeId -> ObjectUtil.isNull(getSvipNoteSpStoreDOByNoteIdAndStoreId(null,createReqVO.getNoteSpId(),storeId)))
            .map(storeId->{
                SvipNoteSpStoreDO storeDO = new SvipNoteSpStoreDO();
                storeDO.setNoteSpId(createReqVO.getNoteSpId());
                storeDO.setSpId(createReqVO.getSpId());
                storeDO.setStoreId(storeId);
                storeDO.setStatus(YTCommonStatusEnum.ENABLE.getStatus());
            return storeDO;
        }).collect(Collectors.toList());

        getBaseMapper().insertBatch(noteSpStoreDOList);
    }

    @Override
    public void updateNoteSpStore(SvipNoteSpStoreSaveReqVO reqVO) {
        validateNoteSpStoreSame(reqVO.getId(),reqVO.getNoteSpId(),reqVO.getStoreId());
        // 校验存在
        validateNoteSpStoreExists(reqVO.getId());
        // 更新
        SvipNoteSpStoreDO updateObj = BeanUtils.toBean(reqVO, SvipNoteSpStoreDO.class);
        getBaseMapper().updateById(updateObj);
    }

    @Override
    public void updateNoteStatus(SvipNoteStatusReqVO reqVO) {
        getBaseMapper().update(new LambdaUpdateWrapper<SvipNoteSpStoreDO>()
                .set(SvipNoteSpStoreDO::getStatus,reqVO.getStatus())
                .in(SvipNoteSpStoreDO::getId,reqVO.getIds())
        );
    }

    @Override
    public void deleteNoteSpStore(Long id) {
        // 校验存在
        validateNoteSpStoreExists(id);
        // 删除
        getBaseMapper().deleteById(id);
    }

    private void validateNoteSpStoreExists(Long id) {
        if (getBaseMapper().selectById(id) == null) {
            throw exception(NOTE_SP_STORE_NOT_EXISTS);
        }
    }

    private void validateNoteSpStoreSame(Long id,Long noteId,Long storeId) {
        if (ObjectUtil.isNotNull(getSvipNoteSpStoreDOByNoteIdAndStoreId(id,noteId,storeId))) {
            throw exception(NOTE_SP_STORE_EXISTS);
        }
    }

    private SvipNoteSpStoreDO getSvipNoteSpStoreDOByNoteIdAndStoreId(Long id,Long noteId,Long storeId){
        return getBaseMapper().selectOne(new LambdaQueryWrapperX<SvipNoteSpStoreDO>()
                .eq(SvipNoteSpStoreDO::getNoteSpId,noteId)
                .eq(SvipNoteSpStoreDO::getStoreId,storeId)
                .neIfPresent(SvipNoteSpStoreDO::getId,id)
        );
    }

    @Override
    public SvipNoteSpStoreDO getNoteSpStore(Long id) {
        return getBaseMapper().selectById(id);
    }

    @Override
    public PageResult<SvipNoteSpStoreDO> getNoteSpStorePage(SvipNoteSpStorePageReqVO pageReqVO) {
        return getBaseMapper().selectPage(pageReqVO);
    }

    @Override
    public SvipNoteGeneratorDto getSvipNoteGeneratorDto(Long spStoreNoteId) {
        return getBaseMapper().getSvipNoteGeneratorDto(spStoreNoteId);
    }

}