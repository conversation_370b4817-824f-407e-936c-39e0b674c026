package com.yitong.octopus.module.aigc.dal.mysql.talentaccount;

import java.util.*;

import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.framework.mybatis.core.mapper.BaseMapperX;
import com.yitong.octopus.module.aigc.dal.dataobject.talentaccount.TalentAccountDO;
import org.apache.ibatis.annotations.Mapper;
import com.yitong.octopus.module.aigc.controller.admin.talentaccount.vo.*;

/**
 * 达人账号 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TalentAccountMapper extends BaseMapperX<TalentAccountDO> {

    default PageResult<TalentAccountDO> selectPage(TalentAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TalentAccountDO>()
                .eqIfPresent(TalentAccountDO::getTalentId, reqVO.getTalentId())
                .eqIfPresent(TalentAccountDO::getStatus, reqVO.getStatus())
                .likeIfPresent(TalentAccountDO::getUserName, reqVO.getUserName())
                .eqIfPresent(TalentAccountDO::getLoginType, reqVO.getLoginType())
                .betweenIfPresent(TalentAccountDO::getLastLoginTime, reqVO.getLastLoginTime())
                .betweenIfPresent(TalentAccountDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TalentAccountDO::getId));
    }

    default List<TalentAccountDO> selectList(TalentAccountExportReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TalentAccountDO>()
                .eqIfPresent(TalentAccountDO::getTalentId, reqVO.getTalentId())
                .eqIfPresent(TalentAccountDO::getStatus, reqVO.getStatus())
                .likeIfPresent(TalentAccountDO::getUserName, reqVO.getUserName())
                .eqIfPresent(TalentAccountDO::getLoginType, reqVO.getLoginType())
                .betweenIfPresent(TalentAccountDO::getLastLoginTime, reqVO.getLastLoginTime())
                .betweenIfPresent(TalentAccountDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(TalentAccountDO::getId));
    }

}
