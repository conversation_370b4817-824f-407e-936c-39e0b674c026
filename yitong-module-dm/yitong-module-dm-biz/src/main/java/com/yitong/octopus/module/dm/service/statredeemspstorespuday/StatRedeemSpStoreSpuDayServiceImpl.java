package com.yitong.octopus.module.dm.service.statredeemspstorespuday;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yitong.octopus.module.dm.dal.dataobject.statredeemspstoreday.StatRedeemSpStoreDayDO;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import com.yitong.octopus.module.dm.controller.admin.statredeemspstorespuday.vo.*;
import com.yitong.octopus.module.dm.dal.dataobject.statredeemspstorespuday.StatRedeemSpStoreSpuDayDO;
import com.yitong.octopus.framework.common.pojo.PageResult;

import com.yitong.octopus.module.dm.convert.statredeemspstorespuday.StatRedeemSpStoreSpuDayConvert;
import com.yitong.octopus.module.dm.dal.mysql.statredeemspstorespuday.StatRedeemSpStoreSpuDayMapper;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.dm.enums.ErrorCodeConstants.*;

/**
 * 汇总商家门店商品核销统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class StatRedeemSpStoreSpuDayServiceImpl implements StatRedeemSpStoreSpuDayService {

    @Resource
    private StatRedeemSpStoreSpuDayMapper statRedeemSpStoreSpuDayMapper;

    @Override
    public Long createStatRedeemSpStoreSpuDay(StatRedeemSpStoreSpuDayCreateReqVO createReqVO) {
        // 插入
        StatRedeemSpStoreSpuDayDO statRedeemSpStoreSpuDay = StatRedeemSpStoreSpuDayConvert.INSTANCE.convert(createReqVO);
        statRedeemSpStoreSpuDayMapper.insert(statRedeemSpStoreSpuDay);
        // 返回
        return statRedeemSpStoreSpuDay.getId();
    }

    @Override
    public void updateStatRedeemSpStoreSpuDay(StatRedeemSpStoreSpuDayUpdateReqVO updateReqVO) {
        // 校验存在
        validateStatRedeemSpStoreSpuDayExists(updateReqVO.getId());
        // 更新
        StatRedeemSpStoreSpuDayDO updateObj = StatRedeemSpStoreSpuDayConvert.INSTANCE.convert(updateReqVO);
        statRedeemSpStoreSpuDayMapper.updateById(updateObj);
    }

    @Override
    public void deleteStatRedeemSpStoreSpuDay(Long id) {
        // 校验存在
        validateStatRedeemSpStoreSpuDayExists(id);
        // 删除
        statRedeemSpStoreSpuDayMapper.deleteById(id);
    }

    private void validateStatRedeemSpStoreSpuDayExists(Long id) {
        if (statRedeemSpStoreSpuDayMapper.selectById(id) == null) {
            throw exception(STAT_REDEEM_SP_STORE_SPU_DAY_NOT_EXISTS);
        }
    }

    @Override
    public StatRedeemSpStoreSpuDayDO getStatRedeemSpStoreSpuDay(Long id) {
        return statRedeemSpStoreSpuDayMapper.selectById(id);
    }

    @Override
    public List<StatRedeemSpStoreSpuDayDO> getStatRedeemSpStoreSpuDayList(Collection<Long> ids) {
        return statRedeemSpStoreSpuDayMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<StatRedeemSpStoreSpuDayDO> getStatRedeemSpStoreSpuDayPage(StatRedeemSpStoreSpuDayPageReqVO pageReqVO) {
        return statRedeemSpStoreSpuDayMapper.selectPage(pageReqVO);
    }

    @Override
    public List<StatRedeemSpStoreSpuDayDO> getStatRedeemSpStoreSpuDayList(StatRedeemSpStoreSpuDayExportReqVO exportReqVO) {
        return statRedeemSpStoreSpuDayMapper.selectList(exportReqVO);
    }

    @Override
    public List<StatRedeemSpStoreSpuDayDO> statRedeemStoreSpuByDay(String day) {
        return statRedeemSpStoreSpuDayMapper.statRedeemStoreSpuByDay(day);
    }

    @Override
    public List<StatRedeemSpStoreDayDO> statRedeemStoreDayByDay(String day) {
        return statRedeemSpStoreSpuDayMapper.statRedeemStoreByDay(day);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateRedeemSaleDay(List<StatRedeemSpStoreSpuDayDO> statSaleDayDOList) {
        if (CollectionUtil.isEmpty(statSaleDayDOList)){
            return;
        }
        statSaleDayDOList.forEach(i->{
            StatRedeemSpStoreSpuDayDO checkDo = statRedeemSpStoreSpuDayMapper.selectByStatRedeemSp(i);
            if (ObjectUtil.isNull(checkDo)){
                statRedeemSpStoreSpuDayMapper.insert(i);
            }else {
                i.setId(checkDo.getId());
                statRedeemSpStoreSpuDayMapper.updateById(i);
            }
        });
    }

    @Override
    public StatRedeemSpStoreSpuDayDO getStatRedeemDayPageTotal(StatRedeemSpStoreSpuDayPageReqVO pageReqVO) {
        return statRedeemSpStoreSpuDayMapper.selectPageTotal(pageReqVO);
    }

}
