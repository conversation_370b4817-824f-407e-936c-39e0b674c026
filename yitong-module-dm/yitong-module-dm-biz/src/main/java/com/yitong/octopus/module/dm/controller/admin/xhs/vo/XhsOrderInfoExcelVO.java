package com.yitong.octopus.module.dm.controller.admin.xhs.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 小红书本地生活订单 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class XhsOrderInfoExcelVO {

    @ExcelProperty("商家ID")
    private String sellerId;

    @ExcelProperty("店铺名称")
    private String sellerName;

    @ExcelProperty("匹配")
    private String match;

    @ExcelProperty("商家类目")
    private String category;

    @ExcelProperty("订单ID")
    private String orderId;

    @ExcelProperty("商品ID")
    private String channelSpuId;

    @ExcelProperty("商品名称")
    private String spuName;

    @ExcelProperty("商品城市")
    private String spuCity;

    @ExcelProperty("一级渠道")
    private String oneChannel;

    @ExcelProperty("入口来源")
    private String inSource;

    @ExcelProperty("商详前前一步页面")
    private String goodsViewBefore;

    @ExcelProperty("商详前最后一步页面")
    private String goodsViewAfter;

    @ExcelProperty("载体")
    private String saleType;

    @ExcelProperty("笔记/商家ID")
    private String saleId;

    @ExcelProperty("商家/笔记链接")
    private String saleUrl;

    @ExcelProperty("笔记类型")
    private String itemType;

    @ExcelProperty("kol_id")
    private String kolId;

    @ExcelProperty("作者昵称")
    private String kolNickname;

    @ExcelProperty("核销时间")
    private String redeemTime;

    @ExcelProperty("流量类型")
    private String trafficType;

    @ExcelProperty("粉丝数")
    private Integer kolFansTotal;

    @ExcelProperty("是否核销")
    private Integer isRedeem;

    @ExcelProperty("核销周期（天）")
    private Integer redeemDays;

    @ExcelProperty("订单最新状态")
    private Integer orderStatus;

    @ExcelProperty("DGTV")
    private Double dgtv;

    @ExcelProperty("是否店转（搜索或扫码或分享、且当天核销）")
    private Integer isStoreTransfer;

}
