<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yitong.octopus</groupId>
        <artifactId>yitong-module-app</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yitong-module-app-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        product 模块，主要实现商品相关功能
        例如：品牌、商品分类、spu、sku等功能。
    </description>

    <dependencies>
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-app-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-channel-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-sp-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 小红书小程序 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-opensdk-xhs-minapp</artifactId>
        </dependency>

        <!-- 小红书小程序-服务商 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-opensdk-xhs-minapptp</artifactId>
        </dependency>

        <!-- 小红书分享平台 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-opensdk-xhs-agora</artifactId>
        </dependency>

        <!-- Job 相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-job</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-excel</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.yitong.octopus</groupId>-->
<!--            <artifactId>yitong-spring-boot-starter-biz-operatelog</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-module-app-xhsminiapp-api</artifactId>
            <version>1.7.2-snapshot</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yitong.octopus</groupId>
            <artifactId>yitong-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
    </dependencies>

</project>
