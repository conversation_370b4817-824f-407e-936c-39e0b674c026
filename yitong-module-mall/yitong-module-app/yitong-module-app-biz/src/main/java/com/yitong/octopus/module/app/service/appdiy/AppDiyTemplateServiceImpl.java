package com.yitong.octopus.module.app.service.appdiy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.framework.common.util.string.StringUtils;
import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.yitong.octopus.module.app.controller.admin.appdiy.vo.page.DiyPageSaveVO;
import com.yitong.octopus.module.app.controller.admin.appdiy.vo.template.DiyTemplatePageReqVO;
import com.yitong.octopus.module.app.controller.admin.appdiy.vo.template.DiyTemplatePropertyUpdateRequestVO;
import com.yitong.octopus.module.app.controller.admin.appdiy.vo.template.DiyTemplateSaveVO;
import com.yitong.octopus.module.app.controller.admin.appdiy.vo.template.DiyTemplateUpdateVO;
import com.yitong.octopus.module.app.dal.dataobject.appdiy.AppDiyTemplateDO;
import com.yitong.octopus.module.app.dal.dataobject.appdiy.AppDiyTemplateRefDO;
import com.yitong.octopus.module.app.dal.mysql.appdiy.AppDiyTemplateMapper;
import com.yitong.octopus.module.app.dal.mysql.appdiy.AppDiyTemplateRefMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.yitong.octopus.module.app.enums.ErrorCodeConstants.*;


/**
 * 装修模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppDiyTemplateServiceImpl implements AppDiyTemplateService {

    @Resource
    private AppDiyTemplateMapper diyTemplateMapper;

    @Resource
    private AppDiyTemplateRefMapper diyTemplateRefMapper;

    @Resource
    private AppDiyPageService diyPageService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createDiyTemplate(DiyTemplateSaveVO reqVO) {
        // 校验名称唯一
        validateNameUnique(null, reqVO.getName());
        // 插入
        AppDiyTemplateDO diyTemplate = BeanUtils.toBean(reqVO, AppDiyTemplateDO.class);
        if (StringUtils.isEmpty(diyTemplate.getProperty())){
            diyTemplate.setProperty("{}");
        }
        diyTemplateMapper.insert(diyTemplate);
        // 创建默认页面
        createDefaultPage(diyTemplate);
        //若是指定模板则创建关联关系
        if (!reqVO.getIsGeneral() && ObjectUtil.isNotNull(reqVO.getAppId())){
            diyTemplateRefMapper.insert(AppDiyTemplateRefDO.builder()
                    .appId(reqVO.getAppId())
                    .templateId(diyTemplate.getId())
                    .used(false)
                    .usedTime(LocalDateTime.now())
                    .build());
        }
        // 返回
        return diyTemplate.getId();
    }

    /**
     * 创建模板下面的默认页面
     * 默认创建两个页面：首页、我的
     *
     * @param diyTemplate 模板对象
     */
    private void createDefaultPage(AppDiyTemplateDO diyTemplate) {
        String remark = String.format("模板【%s】自动创建", diyTemplate.getName());
        diyPageService.createDiyPage(new DiyPageSaveVO().setTemplateId(diyTemplate.getId()).setName( "首页").setRemark(remark));
        diyPageService.createDiyPage(new DiyPageSaveVO().setTemplateId(diyTemplate.getId()).setName( "我的").setRemark(remark));
    }

    @Override
    public void updateDiyTemplate(DiyTemplateUpdateVO reqVO) {
        // 校验存在
        validateDiyTemplateExists(reqVO.getId());
        // 校验名称唯一
        validateNameUnique(reqVO.getId(), reqVO.getName());
        // 更新
        AppDiyTemplateDO updateObj = BeanUtils.toBean(reqVO, AppDiyTemplateDO.class);
        diyTemplateMapper.updateById(updateObj);
    }

    void validateNameUnique(Long id, String name) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        AppDiyTemplateDO template = diyTemplateMapper.selectByName(name);
        if (template == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的模板
        if (id == null || !template.getId().equals(id)) {
            throw exception(DIY_TEMPLATE_NAME_USED, name);
        }
    }

    @Override
    public void deleteDiyTemplate(Long id) {
        // 校验存在
        validateDiyTemplateExists(id);
        // 校验使用中
        List<AppDiyTemplateRefDO> templateRefsList = diyTemplateRefMapper.getAppDiyTemplateRefUsedByTemplateId(id);
        if (CollUtil.isNotEmpty(templateRefsList)) {
            throw exception(DIY_TEMPLATE_USED_CANNOT_DELETE);
        }
        // 删除
        diyTemplateMapper.deleteById(id);
    }

    private AppDiyTemplateDO validateDiyTemplateExists(Long id) {
        AppDiyTemplateDO diyTemplateDO = diyTemplateMapper.selectById(id);
        if (diyTemplateDO == null) {
            throw exception(DIY_TEMPLATE_NOT_EXISTS);
        }
        return diyTemplateDO;
    }

    @Override
    public AppDiyTemplateDO getDiyTemplate(Long id) {
        return diyTemplateMapper.selectById(id);
    }

    @Override
    public List<AppDiyTemplateDO> getDiyTemplateByIds(List<Long> ids) {
        return diyTemplateMapper.selectList(new LambdaQueryWrapperX<AppDiyTemplateDO>()
                .in(AppDiyTemplateDO::getId,ids)
                .last("ORDER BY FIELD(id,"+CollUtil.join(ids,",")+")")
        );
    }

    @Override
    public PageResult<AppDiyTemplateDO> getDiyTemplatePage(DiyTemplatePageReqVO pageReqVO) {
        return diyTemplateMapper.selectPage(pageReqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void useDiyTemplate(Long templateId,Long appId) {
        // 校验存在
        validateDiyTemplateExists(templateId);
        // 已使用的更新为未使用
        AppDiyTemplateRefDO appDiyTemplateRef = diyTemplateRefMapper.getAppDiyTemplateRefByTemplateIdAndppId(templateId,appId);
        if (ObjectUtil.isNull(appDiyTemplateRef)) {
            diyTemplateRefMapper.updateAppDiyTemplateRefUsedByAppId(appId,false);
            diyTemplateRefMapper.insert(AppDiyTemplateRefDO.builder()
                    .appId(appId)
                    .templateId(templateId)
                    .used(true)
                    .usedTime(LocalDateTime.now())
                    .build());
            return;
        }
        if (!appDiyTemplateRef.getUsed()) {
            //根据app设置模板为未使用
            diyTemplateRefMapper.updateAppDiyTemplateRefUsedByAppId(appId,false);
            //跟新使用模板模板为使用
            diyTemplateRefMapper.updateAppDiyTemplateRefUsedByTemplateIdAndAppId(templateId,appId,true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDiyTemplateProperty(DiyTemplatePropertyUpdateRequestVO updateReqVO) {
        // 校验存在
        validateDiyTemplateExists(updateReqVO.getId());
        // 更新模板属性
        AppDiyTemplateDO updateObj = BeanUtils.toBean(updateReqVO, AppDiyTemplateDO.class);
        diyTemplateMapper.updateById(updateObj);
    }

    @Override
    public AppDiyTemplateDO getUsedDiyTemplate(Long appId) {
        return diyTemplateMapper.selectByUsed(true,appId);
    }

}
