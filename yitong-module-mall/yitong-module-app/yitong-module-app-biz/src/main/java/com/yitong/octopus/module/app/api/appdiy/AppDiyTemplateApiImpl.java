package com.yitong.octopus.module.app.api.appdiy;

import com.yitong.octopus.framework.common.util.object.BeanUtils;
import com.yitong.octopus.module.app.api.appdiy.vo.AppDiyTemplateVO;
import com.yitong.octopus.module.app.dal.dataobject.appdiy.AppDiyTemplateDO;
import com.yitong.octopus.module.app.service.appdiy.AppDiyTemplateService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AppDiyTemplateApiImpl implements AppDiyTemplateApi {

    @Lazy
    @Resource
    private AppDiyTemplateService appDiyTemplateService;;
    @Override
    public AppDiyTemplateVO getAppDiyTemplateById(Long templateId) {
        AppDiyTemplateDO appDiyTemplateDO =  appDiyTemplateService.getDiyTemplate(templateId);
        return BeanUtils.toBean(appDiyTemplateDO,AppDiyTemplateVO.class);
    }
}
