package com.yitong.octopus.module.app.controller.admin.appproviderticket.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 渠道应用服务商ticket 新增/修改 Request VO")
@Data
public class AppProviderTicketSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14452")
    private Long id;

    @Schema(description = "渠道ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28363")
    @NotNull(message = "渠道ID不能为空")
    private Long channelId;

    @Schema(description = "应用服务商ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18898")
    @NotNull(message = "应用服务商ID不能为空")
    private Long appProviderId;

    @Schema(description = "ticket", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "ticket不能为空")
    private String ticket;

    @Schema(description = "过期时间(s)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "过期时间(s)不能为空")
    private Integer expireIn;

    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime expireInTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

}