package com.yitong.octopus.module.app.controller.admin.appproviderticket;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import com.yitong.octopus.framework.common.pojo.PageParam;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.CommonResult;
import com.yitong.octopus.framework.common.util.object.BeanUtils;
import static com.yitong.octopus.framework.common.pojo.CommonResult.success;

import com.yitong.octopus.framework.excel.core.util.ExcelUtils;
import com.yitong.octopus.module.app.controller.admin.appproviderticket.vo.*;
import com.yitong.octopus.module.app.dal.dataobject.appproviderticket.AppProviderTicketDO;
import com.yitong.octopus.module.app.service.appproviderticket.AppProviderTicketService;

@Tag(name = "管理后台 - 渠道应用服务商ticket")
@RestController
@RequestMapping("/app/provider-ticket")
@Validated
public class AppProviderTicketController {

    @Resource
    private AppProviderTicketService providerTicketService;

    @PostMapping("/create")
    @Operation(summary = "创建渠道应用服务商ticket")
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:create')")
    public CommonResult<Long> createProviderTicket(@Valid @RequestBody AppProviderTicketSaveReqVO createReqVO) {
        return success(providerTicketService.createProviderTicket(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新渠道应用服务商ticket")
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:update')")
    public CommonResult<Boolean> updateProviderTicket(@Valid @RequestBody AppProviderTicketSaveReqVO updateReqVO) {
        providerTicketService.updateProviderTicket(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除渠道应用服务商ticket")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:delete')")
    public CommonResult<Boolean> deleteProviderTicket(@RequestParam("id") Long id) {
        providerTicketService.deleteProviderTicket(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得渠道应用服务商ticket")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:query')")
    public CommonResult<AppProviderTicketRespVO> getProviderTicket(@RequestParam("id") Long id) {
        AppProviderTicketDO providerTicket = providerTicketService.getProviderTicket(id);
        return success(BeanUtils.toBean(providerTicket, AppProviderTicketRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得渠道应用服务商ticket分页")
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:query')")
    public CommonResult<PageResult<AppProviderTicketRespVO>> getProviderTicketPage(@Valid AppProviderTicketPageReqVO pageReqVO) {
        PageResult<AppProviderTicketDO> pageResult = providerTicketService.getProviderTicketPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AppProviderTicketRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出渠道应用服务商ticketExcel")
    @PreAuthorize("@ss.hasPermission('app:provider-ticket:export')")
    public void exportProviderTicketExcel(@Valid AppProviderTicketPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AppProviderTicketDO> list = providerTicketService.getProviderTicketPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "渠道应用服务商ticket.xls", "数据", AppProviderTicketRespVO.class,
                        BeanUtils.toBean(list, AppProviderTicketRespVO.class));
    }

}