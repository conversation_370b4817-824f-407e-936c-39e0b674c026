package com.yitong.octopus.module.app.service.appbanner;

import java.util.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yitong.octopus.module.app.controller.admin.appbanner.vo.*;
import com.yitong.octopus.module.app.dal.dataobject.appbanner.AppBannerDO;
import com.yitong.octopus.framework.common.pojo.PageResult;
import com.yitong.octopus.framework.common.pojo.PageParam;

import javax.validation.Valid;

/**
 * 渠道应用Banner管理 Service 接口
 *
 * <AUTHOR>
 */
public interface AppBannerService extends IService<AppBannerDO> {

    /**
     * 创建渠道应用Banner管理
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBanner(@Valid AppBannerSaveReqVO createReqVO);

    /**
     * 更新渠道应用Banner管理
     *
     * @param updateReqVO 更新信息
     */
    void updateBanner(@Valid AppBannerSaveReqVO updateReqVO);

    /**
     * 删除渠道应用Banner管理
     *
     * @param id 编号
     */
    void deleteBanner(Long id);

    /**
     * 获得渠道应用Banner管理
     *
     * @param id 编号
     * @return 渠道应用Banner管理
     */
    AppBannerDO getBanner(Long id);

    /**
     * 获得渠道应用Banner管理分页
     *
     * @param pageReqVO 分页查询
     * @return 渠道应用Banner管理分页
     */
    PageResult<AppBannerDO> getBannerPage(AppBannerPageReqVO pageReqVO);

}