//package com.yitong.octopus.module.app.service.xhscomponent.impl;
//
//import com.fasterxml.jackson.core.type.TypeReference;
//import com.yitong.octopus.framework.common.pojo.PageResult;
//import com.yitong.octopus.framework.common.util.json.JsonUtils;
//import com.yitong.octopus.framework.mybatis.core.query.LambdaQueryWrapperX;
//import com.yitong.octopus.module.app.controller.admin.xhscomponent.vo.XhsServiceComponentCreateReqVO;
//import com.yitong.octopus.module.app.controller.admin.xhscomponent.vo.XhsServiceComponentPageReqVO;
//import com.yitong.octopus.module.app.controller.admin.xhscomponent.vo.XhsServiceComponentUpdateReqVO;
//import com.yitong.octopus.module.app.convert.xhscomponent.XhsServiceComponentConvert;
//import com.yitong.octopus.module.app.dal.dataobject.xhscomponent.XhsServiceComponentDO;
//import com.yitong.octopus.module.app.dal.dataobject.xhscomponent.XhsServiceComponentPriceDO;
//import com.yitong.octopus.module.app.dal.dataobject.xhscomponent.XhsServiceComponentStockDO;
//import com.yitong.octopus.module.app.dal.dataobject.xhscomponent.XhsServiceComponentStoreDO;
//import com.yitong.octopus.module.app.dal.dataobject.xhscomponent.XhsServiceComponentVersionDO;
//import com.yitong.octopus.module.app.dal.mysql.xhscomponent.XhsServiceComponentMapper;
//import com.yitong.octopus.module.app.dal.mysql.xhscomponent.XhsServiceComponentPriceMapper;
//import com.yitong.octopus.module.app.dal.mysql.xhscomponent.XhsServiceComponentStockMapper;
//import com.yitong.octopus.module.app.dal.mysql.xhscomponent.XhsServiceComponentStoreMapper;
//import com.yitong.octopus.module.app.dal.mysql.xhscomponent.XhsServiceComponentVersionMapper;
//import com.yitong.octopus.module.app.enums.xhscomponent.XhsServiceComponentStatusEnum;
//import com.yitong.octopus.module.app.enums.xhscomponent.XhsServiceComponentVersionTypeEnum;
//import com.yitong.octopus.module.app.service.xhscomponent.XhsServiceComponentService;
//import com.yitong.octopus.module.opensdk.xhsminiapp.client.ComponentClient;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.component.request.ComponentCreateRequest;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.component.request.ComponentDeleteRequest;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.component.request.ComponentUpdateRequest;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.component.response.ComponentCreateResponse;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.component.response.ComponentUpdateResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//import org.springframework.validation.annotation.Validated;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//
//import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.yitong.octopus.module.app.enums.ErrorCodeConstants.*;
//import com.yitong.octopus.module.app.controller.app.vo.component.AppXhsServiceComponentPageReqVO;
//
///**
// * 小红书服务组件 Service 实现类
// *
// * <AUTHOR>
// */
//@Service
//@Validated
//@Slf4j
//public class XhsServiceComponentServiceImpl implements XhsServiceComponentService {
//
//    @Resource
//    private XhsServiceComponentMapper xhsServiceComponentMapper;
//
//    @Resource
//    private XhsServiceComponentVersionMapper xhsServiceComponentVersionMapper;
//    
//    @Resource
//    private XhsServiceComponentStockMapper xhsServiceComponentStockMapper;
//    
//    @Resource
//    private XhsServiceComponentPriceMapper xhsServiceComponentPriceMapper;
//    
//    @Resource
//    private XhsServiceComponentStoreMapper xhsServiceComponentStoreMapper;
//
//    @Resource
//    private ComponentClient componentClient;
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Long createXhsServiceComponent(XhsServiceComponentCreateReqVO createReqVO) {
//        // 插入组件
//        XhsServiceComponentDO xhsServiceComponent = XhsServiceComponentConvert.INSTANCE.convert(createReqVO);
//        xhsServiceComponent.setDraftVersion(1); // 初始版本为1
//        xhsServiceComponentMapper.insert(xhsServiceComponent);
//
//        // 插入版本
//        XhsServiceComponentVersionDO version = new XhsServiceComponentVersionDO();
//        version.setComponentId(xhsServiceComponent.getId());
//        version.setAppId(xhsServiceComponent.getAppId());
//        version.setVersion(1);
//        version.setAttr(xhsServiceComponent.getAttr());
//        version.setServiceName(createReqVO.getServiceName());
//        version.setServiceImg(createReqVO.getServiceImg());
//        version.setCategoryId(createReqVO.getCategoryId());
//        version.setServiceLink(createReqVO.getServiceLink());
//        version.setStatus(XhsServiceComponentStatusEnum.DRAFT.getStatus());
//        version.setVersionType(XhsServiceComponentVersionTypeEnum.DRAFT.getType());
//        xhsServiceComponentVersionMapper.insert(version);
//        
//        // 同步到小红书
//        try {
//            syncToXhsChannel(xhsServiceComponent, version);
//        } catch (Exception e) {
//            log.error("[createXhsServiceComponent][同步小红书服务组件失败，组件ID({})，异常信息({})]", 
//                    xhsServiceComponent.getId(), e.getMessage());
//            // 不抛出异常，允许创建成功但同步失败的情况
//        }
//
//        return xhsServiceComponent.getId();
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateXhsServiceComponent(XhsServiceComponentUpdateReqVO updateReqVO) {
//        // 校验存在
//        XhsServiceComponentDO xhsServiceComponent = validateXhsServiceComponentExists(updateReqVO.getId());
//
//        // 更新组件
//        XhsServiceComponentDO updateObj = XhsServiceComponentConvert.INSTANCE.convert(updateReqVO);
//        xhsServiceComponentMapper.updateById(updateObj);
//
//        // 更新草稿版本
//        XhsServiceComponentVersionDO draftVersion = xhsServiceComponentVersionMapper.selectDraftByComponentId(updateReqVO.getId());
//        if (draftVersion == null) {
//            // 如果没有草稿版本，创建一个新的草稿版本
//            Integer newVersion = 1;
//            if (xhsServiceComponent.getActiveVersion() != null) {
//                newVersion = xhsServiceComponent.getActiveVersion() + 1;
//            }
//            draftVersion = new XhsServiceComponentVersionDO();
//            draftVersion.setComponentId(xhsServiceComponent.getId());
//            draftVersion.setAppId(xhsServiceComponent.getAppId());
//            draftVersion.setVersion(newVersion);
//            draftVersion.setStatus(XhsServiceComponentStatusEnum.DRAFT.getStatus());
//            draftVersion.setVersionType(XhsServiceComponentVersionTypeEnum.DRAFT.getType());
//            
//            // 更新组件的草稿版本号
//            xhsServiceComponent.setDraftVersion(newVersion);
//            xhsServiceComponentMapper.updateById(xhsServiceComponent);
//        }
//
//        // 更新版本信息
//        draftVersion.setAttr(updateObj.getAttr());
//        draftVersion.setServiceName(updateReqVO.getServiceName());
//        draftVersion.setServiceImg(updateReqVO.getServiceImg());
//        draftVersion.setCategoryId(updateReqVO.getCategoryId());
//        draftVersion.setServiceLink(updateReqVO.getServiceLink());
//        xhsServiceComponentVersionMapper.updateById(draftVersion);
//        
//        // 同步到小红书
//        try {
//            syncToXhsChannel(xhsServiceComponent, draftVersion);
//        } catch (Exception e) {
//            log.error("[updateXhsServiceComponent][同步小红书服务组件失败，组件ID({})，异常信息({})]", 
//                    xhsServiceComponent.getId(), e.getMessage());
//            // 不抛出异常，允许更新成功但同步失败的情况
//        }
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void deleteXhsServiceComponent(Long id) {
//        // 校验存在
//        XhsServiceComponentDO xhsServiceComponent = validateXhsServiceComponentExists(id);
//        
//        // 同步删除到小红书
//        try {
//            deleteFromXhsChannel(xhsServiceComponent);
//        } catch (Exception e) {
//            log.error("[deleteXhsServiceComponent][同步删除小红书服务组件失败，组件ID({})，异常信息({})]", 
//                    xhsServiceComponent.getId(), e.getMessage());
//            // 不抛出异常，允许删除成功但同步失败的情况
//        }
//        
//        // 删除组件
//        xhsServiceComponentMapper.deleteById(id);
//        
//        // 删除相关版本
//        xhsServiceComponentVersionMapper.delete(
//                new LambdaQueryWrapperX<XhsServiceComponentVersionDO>()
//                        .eq(XhsServiceComponentVersionDO::getComponentId, id));
//        
//        // 删除相关库存
//        xhsServiceComponentStockMapper.delete(
//                new LambdaQueryWrapperX<XhsServiceComponentStockDO>()
//                        .eq(XhsServiceComponentStockDO::getComponentId, id));
//        
//        // 删除相关价格
//        xhsServiceComponentPriceMapper.delete(
//                new LambdaQueryWrapperX<XhsServiceComponentPriceDO>()
//                        .eq(XhsServiceComponentPriceDO::getComponentId, id));
//        
//        // 删除相关门店
//        xhsServiceComponentStoreMapper.delete(
//                new LambdaQueryWrapperX<XhsServiceComponentStoreDO>()
//                        .eq(XhsServiceComponentStoreDO::getComponentId, id));
//    }
//
//    private XhsServiceComponentDO validateXhsServiceComponentExists(Long id) {
//        XhsServiceComponentDO xhsServiceComponent = xhsServiceComponentMapper.selectById(id);
//        if (xhsServiceComponent == null) {
//            throw exception(XHS_SERVICE_COMPONENT_NOT_EXISTS);
//        }
//        return xhsServiceComponent;
//    }
//
//    @Override
//    public XhsServiceComponentDO getXhsServiceComponent(Long id) {
//        return xhsServiceComponentMapper.selectById(id);
//    }
//
//    @Override
//    public PageResult<XhsServiceComponentDO> getXhsServiceComponentPage(AppXhsServiceComponentPageReqVO pageReqVO) {
//        // 只查询有效的服务组件（有生效版本的）
//        return xhsServiceComponentMapper.selectPageByApp(pageReqVO);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public boolean syncXhsServiceComponent(Long id) {
//        // 校验存在
//        XhsServiceComponentDO xhsServiceComponent = validateXhsServiceComponentExists(id);
//        
//        // 获取草稿版本
//        XhsServiceComponentVersionDO draftVersion = xhsServiceComponentVersionMapper.selectDraftByComponentId(id);
//        if (draftVersion == null) {
//            throw exception(XHS_SERVICE_COMPONENT_DRAFT_VERSION_NOT_EXISTS);
//        }
//
//        try {
//            // 同步到小红书
//            syncToXhsChannel(xhsServiceComponent, draftVersion);
//            
//            // 更新版本状态为审核中
//            draftVersion.setStatus(XhsServiceComponentStatusEnum.WAIT_AUDIT.getStatus());
//            draftVersion.setVersionType(XhsServiceComponentVersionTypeEnum.AUDIT.getType());
//            xhsServiceComponentVersionMapper.updateById(draftVersion);
//            
//            // 更新组件的审核中版本号
//            xhsServiceComponent.setAuditVersion(draftVersion.getVersion());
//            xhsServiceComponent.setDraftVersion(null); // 清空草稿版本
//            xhsServiceComponentMapper.updateById(xhsServiceComponent);
//            
//            return true;
//        } catch (Exception e) {
//            log.error("同步小红书服务组件失败", e);
//            throw exception(XHS_SERVICE_COMPONENT_SYNC_FAIL);
//        }
//    }
//    
//    /**
//     * 同步服务组件到小红书渠道
//     * 
//     * @param serviceDO 服务组件
//     * @param versionDO 服务组件版本
//     */
//    private void syncToXhsChannel(XhsServiceComponentDO serviceDO, XhsServiceComponentVersionDO versionDO) {
//        try {
//            // 判断是否已有组件ID
//            if (StringUtils.isNotBlank(serviceDO.getOutComponentId())) {
//                // 已有组件ID，执行更新操作
//                ComponentUpdateRequest updateRequest = buildUpdateRequest(serviceDO, versionDO);
//                ComponentUpdateResponse response = componentClient.updateComponent(updateRequest);
//                log.info("[syncToXhsChannel][更新服务组件成功，组件ID({})，返回结果({})]", 
//                        serviceDO.getOutComponentId(), JsonUtils.toJsonString(response));
//            } else {
//                // 没有组件ID，执行创建操作
//                ComponentCreateRequest createRequest = buildCreateRequest(serviceDO, versionDO);
//                ComponentCreateResponse response = componentClient.createComponent(createRequest);
//                log.info("[syncToXhsChannel][创建服务组件成功，返回结果({})]", JsonUtils.toJsonString(response));
//                
//                // 更新组件ID
//                if (StringUtils.isNotBlank(response.getComponentId())) {
//                    XhsServiceComponentDO updateDO = new XhsServiceComponentDO();
//                    updateDO.setId(serviceDO.getId());
//                    updateDO.setOutComponentId(response.getComponentId());
//                    xhsServiceComponentMapper.updateById(updateDO);
//                }
//            }
//        } catch (Exception e) {
//            log.error("[syncToXhsChannel][同步服务组件异常，组件ID({})，异常信息({})]", 
//                    serviceDO.getId(), e.getMessage());
//            throw e;
//        }
//    }
//    
//    /**
//     * 从小红书渠道删除服务组件
//     * 
//     * @param serviceDO 服务组件
//     */
//    private void deleteFromXhsChannel(XhsServiceComponentDO serviceDO) {
//        try {
//            // 判断是否有组件ID
//            if (StringUtils.isNotBlank(serviceDO.getOutComponentId())) {
//                // 构建删除请求
//                ComponentDeleteRequest deleteRequest = new ComponentDeleteRequest();
//                List<String> componentIdList = new ArrayList<>();
//                componentIdList.add(serviceDO.getOutComponentId());
//                deleteRequest.setComponentIdList(componentIdList);
//                
//                // 调用删除接口
//                componentClient.deleteComponent(deleteRequest);
//                log.info("[deleteFromXhsChannel][删除服务组件成功，组件ID({})]", serviceDO.getOutComponentId());
//            }
//        } catch (Exception e) {
//            log.error("[deleteFromXhsChannel][删除服务组件异常，组件ID({})，异常信息({})]", 
//                    serviceDO.getId(), e.getMessage());
//            throw e;
//        }
//    }
//    
//    /**
//     * 构建创建服务组件请求
//     * 
//     * @param serviceDO 服务组件
//     * @param versionDO 服务组件版本
//     * @return 创建请求
//     */
//    private ComponentCreateRequest buildCreateRequest(XhsServiceComponentDO serviceDO, XhsServiceComponentVersionDO versionDO) {
//        ComponentCreateRequest request = new ComponentCreateRequest();
//        request.setSolutionCode(serviceDO.getSolutionCode());
//        request.setServiceName(versionDO.getServiceName());
//        request.setServiceImg(versionDO.getServiceImg());
//        request.setCategoryId(versionDO.getCategoryId());
//        request.setServiceLink(versionDO.getServiceLink());
//        request.setOutComponentId(serviceDO.getOutComponentId() != null ? 
//                serviceDO.getOutComponentId() : String.valueOf(serviceDO.getId()));
//        
//        // 构建组件属性
//        Map<String, String> componentAttrs = buildComponentAttrs(serviceDO, versionDO);
//        request.setComponentAttrs(componentAttrs);
//        
//        return request;
//    }
//    
//    /**
//     * 构建更新服务组件请求
//     * 
//     * @param serviceDO 服务组件
//     * @param versionDO 服务组件版本
//     * @return 更新请求
//     */
//    private ComponentUpdateRequest buildUpdateRequest(XhsServiceComponentDO serviceDO, XhsServiceComponentVersionDO versionDO) {
//        ComponentUpdateRequest request = new ComponentUpdateRequest();
//        request.setComponentId(serviceDO.getOutComponentId());
//        request.setUpdateType("ALL_ATTR_OVERWRITE"); // 全属性覆盖
//        request.setServiceName(versionDO.getServiceName());
//        request.setServiceImg(versionDO.getServiceImg());
//        request.setCategoryId(versionDO.getCategoryId());
//        request.setServiceLink(versionDO.getServiceLink());
//        
//        // 构建组件属性
//        Map<String, String> componentAttrs = buildComponentAttrs(serviceDO, versionDO);
//        request.setComponentAttrs(componentAttrs);
//        
//        return request;
//    }
//    
//    /**
//     * 构建组件属性
//     * 
//     * @param serviceDO 服务组件
//     * @param versionDO 服务组件版本
//     * @return 组件属性
//     */
//    private Map<String, String> buildComponentAttrs(XhsServiceComponentDO serviceDO, XhsServiceComponentVersionDO versionDO) {
//        Map<String, String> componentAttrs = new HashMap<>();
//        
//        // 解析属性JSON
//        if (StringUtils.isNotBlank(versionDO.getAttr())) {
//            try {
//                Map<String, Object> attrMap = JsonUtils.parseObject(versionDO.getAttr(), Map.class);
//                
//                // 根据不同服务类型处理属性
//                String solutionCode = serviceDO.getSolutionCode();
//                
//                // 通用属性
//                if (attrMap.containsKey("service_choose_time")) {
//                    componentAttrs.put("service_choose_time", String.valueOf(attrMap.get("service_choose_time")));
//                }
//                if (attrMap.containsKey("service_instant_confirm")) {
//                    componentAttrs.put("service_instant_confirm", String.valueOf(attrMap.get("service_instant_confirm")));
//                }
//                if (attrMap.containsKey("service_change_reservation")) {
//                    componentAttrs.put("service_change_reservation", String.valueOf(attrMap.get("service_change_reservation")));
//                }
//                
//                // 处理门店列表
//                if (attrMap.containsKey("shop_list")) {
//                    componentAttrs.put("shop_list", String.valueOf(attrMap.get("shop_list")));
//                }
//                
//                // 处理库存类型和详情
//                if (attrMap.containsKey("stock_type")) {
//                    componentAttrs.put("stock_type", String.valueOf(attrMap.get("stock_type")));
//                }
//                if (attrMap.containsKey("stock_detail")) {
//                    componentAttrs.put("stock_detail", String.valueOf(attrMap.get("stock_detail")));
//                }
//                
//                // 处理价格类型和详情
//                if (attrMap.containsKey("price_type")) {
//                    componentAttrs.put("price_type", String.valueOf(attrMap.get("price_type")));
//                }
//                if (attrMap.containsKey("price_detail")) {
//                    componentAttrs.put("price_detail", String.valueOf(attrMap.get("price_detail")));
//                }
//                
//                // 团购类型特有属性
//                if ("GROUP_BUY".equals(solutionCode) && attrMap.containsKey("product_id")) {
//                    componentAttrs.put("product_id", String.valueOf(attrMap.get("product_id")));
//                }
//                
//                // 处理其他属性
//                for (Map.Entry<String, Object> entry : attrMap.entrySet()) {
//                    if (!componentAttrs.containsKey(entry.getKey())) {
//                        componentAttrs.put(entry.getKey(), String.valueOf(entry.getValue()));
//                    }
//                }
//            } catch (Exception e) {
//                log.error("[buildComponentAttrs][解析属性JSON异常，组件ID({})，异常信息({})]", 
//                        serviceDO.getId(), e.getMessage());
//            }
//        }
//        
//        return componentAttrs;
//    }
//} 