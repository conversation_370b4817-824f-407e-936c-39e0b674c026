<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.app.dal.mysql.apptoken.AppTokenMapper">

    <update id="expireTokenByAppId" parameterType="java.lang.Long">
        UPDATE yt_app_token
        SET `status` = 0
        WHERE app_id = #{appId} AND `status` = 1 AND expire_in_time &lt; DATE_ADD(NOW(),INTERVAL ${times} MINUTE)
    </update>

</mapper>