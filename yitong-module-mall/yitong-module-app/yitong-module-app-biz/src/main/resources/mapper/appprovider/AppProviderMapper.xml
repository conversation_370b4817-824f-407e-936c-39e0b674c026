<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.app.dal.mysql.appprovider.AppProviderMapper">

    <select id="getAuthExpireAppInfoByAppId" resultMap="mybatis-plus_AppProviderDO">
        SELECT
            a.*
             ,t.id toekn_id
        FROM yt_app_provider a
        LEFT JOIN yt_app_provider_token t  ON  t.app_provider_id = a.id AND t.`status`= 1 AND t.expire_in_time > DATE_ADD(NOW(),INTERVAL ${time} MINUTE) AND t.deleted = 0
        WHERE a.id = #{appId} AND a.deleted = 0
        HAVING t.id IS NULL
    </select>
</mapper>