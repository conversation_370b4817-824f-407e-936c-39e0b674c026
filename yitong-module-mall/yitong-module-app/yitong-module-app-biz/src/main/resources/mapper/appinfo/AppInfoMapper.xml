<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yitong.octopus.module.app.dal.mysql.appinfo.AppInfoMapper">

    <resultMap id="AppInfoTokenVoResultMap" type="com.yitong.octopus.module.app.api.app.vo.AppInfoTokenVo">
        <result column="sp_id" property="spId" typeHandler="com.yitong.octopus.framework.mybatis.core.type.LongListTypeHandler"/>
        <result column="config" property="config" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="getAuthExpireAppInfoByChannelId" resultMap="AppInfoTokenVoResultMap">
        SELECT
            a.*
             ,t.id toekn_id
             ,t.access_token
        FROM yt_app_info a
        LEFT JOIN yt_app_token  t  ON  t.app_id = a.id AND t.`status`= 1 AND t.expire_in_time > DATE_ADD(NOW(),INTERVAL ${time} MINUTE) AND t.deleted = 0
        WHERE a.channel_id = #{channelId} AND a.deleted = 0
        HAVING t.id IS NULL
    </select>

    <select id="getAuthExpireAppInfoByAppId" resultMap="AppInfoTokenVoResultMap">
        SELECT
            a.*
             ,t.id toekn_id
             ,t.access_token
        FROM yt_app_info a
        LEFT JOIN yt_app_token  t  ON  t.app_id = a.id AND t.`status`= 1 AND t.expire_in_time > DATE_ADD(NOW(),INTERVAL ${time} MINUTE) AND t.deleted = 0
        WHERE a.id = #{appId} AND a.deleted = 0
        HAVING t.id IS NULL
    </select>

<!--    <select id="getAuthAccessTokenByAppId" resultType="java.lang.String">-->
<!--        SELECT-->
<!--            a.*-->
<!--             ,t.access_token-->
<!--        FROM yt_app_info a-->
<!--        LEFT JOIN yt_app_token  t  ON  t.app_id = a.id AND t.`status`= 1 AND t.expire_in_time > DATE_ADD(NOW(),INTERVAL ${time} MINUTE) AND t.deleted = 0-->
<!--        WHERE a.id = #{appId} AND a.deleted = 0-->
<!--    </select>-->

    <resultMap id="AppInfoChannelVoResultMap" type="com.yitong.octopus.module.app.api.app.vo.AppInfoChannelVo">
        <result column="sp_id" property="spId" typeHandler="com.yitong.octopus.framework.mybatis.core.type.LongListTypeHandler"/>
        <result column="config" property="config" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="getInfoWithChannelById" resultMap="AppInfoChannelVoResultMap">
        SELECT
            ai.channel_id
            ,ci.`code` channel_code
            ,ci.`name` channel_name
            ,ai.category_id
            ,ai.sort
            ,ai.id
            ,ai.sp_id
            ,ai.`name`
            ,ai.`status`
            ,ai.app_id
            ,ai.config
            ,t.id toekn_id
            ,t.access_token
        FROM yt_app_info ai
         JOIN yt_channel_info ci ON ai.channel_id =  ci.id AND ci.deleted = 0
         LEFT JOIN yt_app_token  t  ON  t.app_id = ai.id AND t.`status`= 1 AND t.expire_in_time > NOW() AND t.deleted = 0
        WHERE ai.id =#{appId} AND ai.deleted = 0
    </select>

    <select id="selectListOrderByIds"
            resultType="com.yitong.octopus.module.app.dal.dataobject.appinfo.AppInfoDO">
        SELECT *
        FROM yt_app_info
        WHERE id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        ORDER BY FIELD(id,
            <foreach collection="ids" item="id2"  separator=",">
                #{id2}
            </foreach>
         )
    </select>

</mapper>