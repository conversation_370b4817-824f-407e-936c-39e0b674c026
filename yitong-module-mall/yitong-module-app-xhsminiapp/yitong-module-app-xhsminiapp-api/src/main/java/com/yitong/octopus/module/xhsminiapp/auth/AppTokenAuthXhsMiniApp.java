//package com.yitong.octopus.module.xhsminiapp.auth;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.yitong.octopus.framework.common.enums.YTCommonStatusEnum;
//import com.yitong.octopus.framework.common.util.date.LocalDateTimeUtils;
//import com.yitong.octopus.framework.common.util.json.JsonUtils;
//import com.yitong.octopus.module.app.controller.admin.apptoken.vo.AppTokenSaveReqVO;
//import com.yitong.octopus.module.app.dal.dataobject.appinfo.AppInfoDO;
//import com.yitong.octopus.module.app.dal.dataobject.apptoken.AppTokenDO;
//import com.yitong.octopus.module.app.service.appinfo.AppInfoService;
//import com.yitong.octopus.module.app.service.apptoken.AppTokenService;
//import com.yitong.octopus.module.opensdk.xhsminiapp.auth.vo.XhsMiniAppAuthVo;
//import com.yitong.octopus.module.opensdk.xhsminiapp.client.OauthClient;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.oauth.request.GetAccessTokenRequest;
//import com.yitong.octopus.module.opensdk.xhsminiapp.entity.oauth.response.GetAccessTokenResponse;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.Duration;
//import java.time.LocalDateTime;
//import java.time.temporal.ChronoUnit;
//
//import static com.yitong.octopus.framework.common.exception.util.ServiceExceptionUtil.exception;
//import static com.yitong.octopus.module.product.enums.ErrorCodeConstants.INFO_NOT_VALID;
//
///**
// * 小红书小程序获取token
// */
//@Slf4j
//@Component
//public class AppTokenAuthXhsMiniApp implements AppTokenAuth{
//    @Resource
//    private AppInfoService appInfoService;
//
//    @Resource
//    private AppTokenService appTokenService;
//
//    @Override
//    public XhsMiniAppAuthVo getAccessToken(Long appId) {
//        AppInfoDO appInfoDO = appInfoService.getInfoById(appId);
//        if (ObjectUtil.isNull(appInfoDO)){
//            log.info("app info is null：{}",appId);
//            throw exception(INFO_NOT_VALID);
//        }
//        if (YTCommonStatusEnum.DISABLE.getStatus().equals(appInfoDO.getStatus())){
//            log.info("app info status disable：[{}-{}]",appId,appInfoDO.getName());
//            throw exception(INFO_NOT_VALID);
//        }
//        AppTokenDO appToken = appTokenService.getAuthAppTokenByAppId(appId);
//        log.info("appid:{},appToken:{}",appId, JsonUtils.toJsonString(appToken));
//        if (ObjectUtil.isNull(appToken) || LocalDateTimeUtils.between( LocalDateTime.now(),appToken.getExpireInTime(), ChronoUnit.MINUTES)<5){
//            AppInfoDO app = appInfoService.getInfoById(appId);
//            GetAccessTokenResponse resp = new OauthClient().execute(new GetAccessTokenRequest()
//                    .setAppid(app.getAppId())
//                    .setSecret(app.getConfig()));
//            //用户token
//            AppTokenSaveReqVO createReqVO = new AppTokenSaveReqVO()
//                    .setAccessToken(resp.getAccessToken())
//                    .setExpireIn(resp.getExpireIn())
//                    .setExpireInTime(LocalDateTimeUtils.addTime(Duration.ofSeconds(resp.getExpireIn())))
//                    .setStatus(YTCommonStatusEnum.ENABLE.getStatus())
//                    .setAppId(app.getId())
//                    .setAppName(app.getName())
//                    .setChannelId(app.getChannelId());
//            appTokenService.createToken(createReqVO);
//            if (ObjectUtil.isNotNull(appToken)){
//                //过期历史token
//                appToken.setStatus(YTCommonStatusEnum.DISABLE.getStatus());
//                appTokenService.updateById(appToken);
//            }
//            return new XhsMiniAppAuthVo(appInfoDO.getId(),appInfoDO.getAppId(),resp.getAccessToken());
//        }
//        return new XhsMiniAppAuthVo(appInfoDO.getId(),appInfoDO.getAppId(),appToken.getAccessToken());
//    }
//
//}
