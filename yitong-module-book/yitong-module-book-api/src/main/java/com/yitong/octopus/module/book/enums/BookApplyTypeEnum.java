package com.yitong.octopus.module.book.enums;

import com.yitong.octopus.framework.common.core.EnumKeyArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 预约申请（1 无限制、2 需要提前N 小时、3  需要提前N 天）
 */
@Getter
@AllArgsConstructor
public enum BookApplyTypeEnum implements EnumKeyArrayValuable {

    ALL(1, "无限制"),
    N_HOUR(2, "需要提前N小时"),
    N_DAY(3, "需要提前N天");

    private final Integer type;
    private final String name;
    public static final Object[] ARRAYS = Arrays.stream(values()).map(BookApplyTypeEnum::getType).toArray();

    @Override
    public Object[] array() {
        return ARRAYS;
    }
}
