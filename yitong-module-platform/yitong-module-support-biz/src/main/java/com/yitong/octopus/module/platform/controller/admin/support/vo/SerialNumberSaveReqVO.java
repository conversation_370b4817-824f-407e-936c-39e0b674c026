package com.yitong.octopus.module.platform.controller.admin.support.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 单号生成器定义新增/修改 Request VO")
@Data
public class SerialNumberSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10943")
    private Long serialNumberId;

    @Schema(description = "业务名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "业务名称不能为空")
    private String businessName;

    @Schema(description = "格式[yyyy]表示年,[mm]标识月,[dd]表示日,[nnn]表示三位数字")
    private String format;

    @Schema(description = "规则格式。none没有周期, year 年周期, month月周期, day日周期", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "规则格式。none没有周期, year 年周期, month月周期, day日周期不能为空")
    private String ruleType;

    @Schema(description = "初始值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "初始值不能为空")
    private Integer initNumber;

    @Schema(description = "步长随机数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "步长随机数不能为空")
    private Integer stepRandomRange;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}