package com.yitong.octopus.module.platform.dal.dataobject.tag;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import com.yitong.octopus.framework.mybatis.core.dataobject.BaseDO;

/**
 * 平台标签 DO
 *
 * <AUTHOR>
 */
@TableName("yt_platform_tag")
@KeySequence("yt_platform_tag_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 微信端返回的id
     */
    private String tagId;

    /**
     * 标签名
     */
    private String name;

    /**
     * 标签组id
     */
    private Long groupId;

    /**
     * 标签类型[1:商家标签;2:商品签]
     */
    private Integer tagType;

    /**
     * 状态(0正常 1停用)
     */
    private Integer status;

}