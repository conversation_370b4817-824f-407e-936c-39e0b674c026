package com.yitong.octopus.module.platform.service.category;


import cn.hutool.core.lang.tree.Tree;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryCreateReqVO;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryRespVO;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryUpdateReqVO;
import com.yitong.octopus.module.platform.controller.admin.category.vo.PlatformCategoryListReqVO;
import com.yitong.octopus.module.platform.dal.dataobject.category.PlatformCategoryDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台分类 Service 接口
 *
 * <AUTHOR>
 */
public interface PlatformCategoryService {

    /**
     * 创建平台分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCategory(@Valid PlatformCategoryCreateReqVO createReqVO);

    /**
     * 更新平台分类
     *
     * @param updateReqVO 更新信息
     */
    void updateCategory(@Valid PlatformCategoryUpdateReqVO updateReqVO);

    /**
     * 删除平台分类
     *
     * @param id 编号
     */
    void deleteCategory(Long id);

    /**
     * 获得平台分类
     *
     * @param id 编号
     * @return 平台分类
     */
    PlatformCategoryDO getCategory(Long id);

    /**
     * 获得平台分类
     *
     * @param id 编号
     * @return 平台分类
     */
    PlatformCategoryRespVO getCategoryById(Long id);


    /**
     * 校验平台分类
     *
     * @param id 分类编号
     */
    void validateCategory(Long id);

    /**
     * 获得平台分类的层级
     *
     * @param id 编号
     * @return 平台分类的层级
     */
    Integer getCategoryLevel(Long id);

    /**
     * 获得平台分类列表
     *
     * @param listReqVO 查询条件
     * @return 平台分类列表
     */
    List<PlatformCategoryDO> getEnableCategoryList(PlatformCategoryListReqVO listReqVO);

    /**
     * 获得开启状态的平台分类列表
     *
     * @return 平台分类列表
     */
    List<PlatformCategoryDO> getEnableCategoryList();

    /**
     * 获得开启状态的平台分类树形结构
     *
     * @return 平台分类树形结构
     */
    Tree<String> getEnableCategoryTree(PlatformCategoryListReqVO treeListReqVO);

}
