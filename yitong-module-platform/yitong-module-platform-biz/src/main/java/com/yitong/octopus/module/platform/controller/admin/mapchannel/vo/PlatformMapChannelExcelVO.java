package com.yitong.octopus.module.platform.controller.admin.mapchannel.vo;

import lombok.*;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yitong.octopus.framework.excel.core.annotations.DictFormat;
import com.yitong.octopus.framework.excel.core.convert.DictConvert;


/**
 * 平台地图渠道 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class PlatformMapChannelExcelVO {

    @ExcelProperty("分类编号")
    private Long id;

    @ExcelProperty("渠道编码")
    private String code;

    @ExcelProperty(value = "开启状态", converter = DictConvert.class)
    @DictFormat("yt_platform_map_channel_status") // TODO 代码优化：建议设置到对应的 XXXDictTypeConstants 枚举类中
    private Integer status;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("API 的账号")
    private String apiKey;

    @ExcelProperty("API 的秘钥")
    private String apiSecret;

    @ExcelProperty("API地址")
    private String apiUrl;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
